<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                             http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.yy.hdzk</groupId>
		<artifactId>hdpt_activity_xlogic_parent</artifactId>
		<version>1.0.0</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<artifactId>hdpt_activity_xlogic_common</artifactId>
	<packaging>jar</packaging>
	<name>HDPT Activity Xlogic - Common</name>

	<properties>
		<mobile-kafka-client.version>0.1.9.1</mobile-kafka-client.version>
		<cncf.opentelemetry.version>1.25.0</cncf.opentelemetry.version>
	</properties>

	<dependencies>

		<dependency>
			<groupId>org.apache.dubbo</groupId>
			<artifactId>dubbo-spring-boot-starter</artifactId>
			<version>${dubbo_yrpc_version}</version>
			<exclusions>
				<exclusion>
					<artifactId>dubbo-rpc-http</artifactId>
					<groupId>org.apache.dubbo</groupId>
				</exclusion>
				<exclusion>
					<artifactId>libthrift</artifactId>
					<groupId>org.apache.thrift</groupId>
				</exclusion>
				<exclusion>
					<artifactId>apollo-client</artifactId>
					<groupId>com.ctrip.framework.apollo</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hawk.metrics.client</artifactId>
					<groupId>com.duowan</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-io</artifactId>
					<groupId>commons-io</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-lang</artifactId>
					<groupId>commons-lang</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-logging</artifactId>
					<groupId>commons-logging</groupId>
				</exclusion>
				<exclusion>
					<artifactId>zookeeper</artifactId>
					<groupId>org.apache.zookeeper</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- Starter for building web, including RESTful, applications using Spring
			MVC. Uses Tomcat as the default embedded container -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-tomcat</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>  <!-- 因tomcat 8.5 的cookie有问题，换成 jetty 容器 -->
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jetty</artifactId>
		</dependency>


		<!-- Starter for using Redis key-value data store with Spring Data Redis
			and the Jedis client -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>

		<!-- Starter for using JDBC with the Tomcat JDBC connection pool -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jdbc</artifactId>
		</dependency>

		<!-- Starter for cache -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-cache</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>

		<!--spring session 与redis应用基本环境配置,需要开启redis后才可以使用，不然启动Spring boot会报错 -->
		<dependency>
			<groupId>org.springframework.session</groupId>
			<artifactId>spring-session-data-redis</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka</artifactId>
		</dependency>

		<!-- Ehcache -->
	    <dependency>
	    	<groupId>net.sf.ehcache</groupId>
	    	<artifactId>ehcache</artifactId>
	    </dependency>

		<!-- MYSQL -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>

		<!-- memcached 依赖 -->
		<dependency>
			<groupId>com.google.code.simple-spring-memcached</groupId>
			<artifactId>xmemcached-provider</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.code.simple-spring-memcached</groupId>
			<artifactId>spring-cache</artifactId>
		</dependency>

		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
		</dependency>

		<dependency>
			<groupId>org.codehaus.jackson</groupId>
			<artifactId>jackson-jaxrs</artifactId>
		</dependency>
		<dependency>
			<groupId>org.codehaus.jackson</groupId>
			<artifactId>jackson-mapper-asl</artifactId>
		</dependency>
		<dependency>
			<groupId>org.codehaus.jackson</groupId>
			<artifactId>jackson-core-asl</artifactId>
		</dependency>
		<dependency>
			<groupId>org.codehaus.janino</groupId>
			<artifactId>janino</artifactId>
		</dependency>

		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.thrift</groupId>
			<artifactId>libthrift</artifactId>
		</dependency>

		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
		</dependency>

	    <dependency>
	        <groupId>com.yy.aomi</groupId>
	        <artifactId>aomi-sdk-all</artifactId>
	    </dependency>

		<dependency>
			<groupId>c3p0</groupId>
			<artifactId>c3p0</artifactId>
		</dependency>

		<dependency>
			<groupId>org.reflections</groupId>
			<artifactId>reflections</artifactId>
		</dependency>



		<!-- begin: udb login jar info -->
		<dependency>
			<groupId>cn.huanju.udb.lgn</groupId>
			<artifactId>ca-client</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.huanju.udb.lgn</groupId>
			<artifactId>udb-client-intranet</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.huanju.udb.lgn</groupId>
			<artifactId>ca-common</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.huanju.udb.lgn</groupId>
			<artifactId>oauth</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.huanju.udb.lgn</groupId>
			<artifactId>oauth-consumer</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.huanju.udb.lgn</groupId>
			<artifactId>oauth-httpclient3</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-httpclient</groupId>
			<artifactId>commons-httpclient</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-collections</groupId>
			<artifactId>commons-collections</artifactId>
		</dependency>
		<dependency>
			  <groupId>com.google.code.gson</groupId>
			  <artifactId>gson</artifactId>
		</dependency>
		<!-- end: udb login jar info -->

		<dependency>
			<groupId>com.yy.zhuiya</groupId>
			<artifactId>zhuiya-log4j2-appender</artifactId>
			<version>3.2.0-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<groupId>com.yy.ent.commons</groupId>
					<artifactId>commons-protopack</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.dubbo</groupId>
			<artifactId>dubbo-all</artifactId>
		</dependency>

		<!-- 引入伴郎：end -->

        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>2.1.0</version>
        </dependency>


		<!-- begin: jserverlib -->
		<dependency>
			<groupId>com.yy.jserverlib</groupId>
			<artifactId>jserverlib-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.yy.jserverlib</groupId>
			<artifactId>jserverlib-sal</artifactId>
		</dependency>
		<dependency>
			<groupId>com.yy.jserverlib</groupId>
			<artifactId>jserverlib-rpc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
		</dependency>
		<!-- end: jserverlib -->

		<dependency>
			<groupId>com.yy.yxst</groupId>
			<artifactId>yxst-lib-svc-sdk</artifactId>
		</dependency>

		<dependency>
			<groupId>net.sourceforge.jexcelapi</groupId>
			<artifactId>jxl</artifactId>
		</dependency>

		<dependency>
			<groupId>com.yy.yxst</groupId>
			<artifactId>mobile-kafka-client</artifactId>
			<version>${mobile-kafka-client.version}</version>
			<exclusions>
				<exclusion>
					<groupId>ch.qos.logback</groupId>
					<artifactId>logback-classic</artifactId>
				</exclusion>
				<exclusion>
					<groupId>ch.qos.logback</groupId>
					<artifactId>logback-core</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.kafka</groupId>
			<artifactId>kafka-clients</artifactId>
			<version>2.0.1</version>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>

        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-core</artifactId>
            <version>1.3.7</version>
            <scope>test</scope>
        </dependency>

		<dependency>
			<groupId>com.yy.hdzk</groupId>
			<artifactId>hdpt_protocol</artifactId>
			<version>2.2.7-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.yy.boot.component</groupId>
			<artifactId>webdb-spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>com.yy.boot.component</groupId>
			<artifactId>cul-spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>com.yy.boot.component</groupId>
			<artifactId>metrics-spring-boot-starter</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.aspectj</groupId>
					<artifactId>aspectjweaver</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.yy.boot</groupId>
			<artifactId>y-boot-starter-thread-pool</artifactId>
		</dependency>

		<dependency>
			<groupId>org.hibernate.validator</groupId>
			<artifactId>hibernate-validator</artifactId>
		</dependency>

		<dependency>
			<groupId>com.lmax</groupId>
			<artifactId>disruptor</artifactId>
			<version>3.4.2</version>
		</dependency>

		<dependency>
			<groupId>io.opentelemetry</groupId>
			<artifactId>opentelemetry-api</artifactId>
			<version>${cncf.opentelemetry.version}</version>
		</dependency>
		<dependency>
			<groupId>io.opentelemetry.instrumentation</groupId>
			<artifactId>opentelemetry-instrumentation-annotations</artifactId>
			<version>${cncf.opentelemetry.version}</version>
		</dependency>

		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
		</dependency>

		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjrt</artifactId>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>${maven.compiler.source}</source>
					<target>${maven.compiler.target}</target>
					<encoding>${project.build.sourceEncoding}</encoding>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<repositories>
		<repository>
			<id>public</id>
			<name>YYEnt Public Repositories</name>
			<url>https://nexus.yy.com/music/content/groups/public/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
	</repositories>

</project>
