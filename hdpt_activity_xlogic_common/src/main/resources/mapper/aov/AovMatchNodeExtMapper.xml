<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.aov.AovMatchNodeExtMapper">
    <insert id="batchInsertMatchNodes">
        insert into aov_match_node (phase_id, round_id, node_index, `state`, team_id, uid)
        values
        <foreach collection="matchNodes" item="matchNode" separator=",">
        (#{matchNode.phaseId}, #{matchNode.roundId}, #{matchNode.nodeIndex}, #{matchNode.state}, #{matchNode.teamId}, #{matchNode.uid})
        </foreach>
    </insert>
    <update id="updateMatchNode" parameterType="com.yy.gameecology.common.bean.UpdateMatchNode">
        <selectKey resultType="java.lang.Integer" keyProperty="score" order="AFTER">
            select score from aov_match_node where id = #{nodeId}
        </selectKey>
        update aov_match_node
        <set>
            <if test="targetState != null">
                , `state` = #{targetState}
            </if>
            <if test="teamId != null">
                , team_id = #{teamId}
            </if>
            <if test="advanceType != null">
                , advance_type = #{advanceType}
            </if>
            <if test="teamState != null">
                , team_state = #{teamState}
            </if>
            <if test="uid != null">
                ,uid = #{uid}
            </if>
            <if test="score != null">
                , score = score + #{score}, game_count = game_count + 1
            </if>
            <if test="aliveScore != null">
                , alive_score = alive_score + #{aliveScore}
            </if>
        </set>
        where id = #{nodeId} and `state` = #{sourceState}
    </update>
    <update id="updateMatchNodesState">
        update aov_match_node set `state` = #{targetState}
        where id in <foreach collection="nodeIds" item="nodeId" separator="," open="(" close=")">#{nodeId}</foreach>
        and `state` = #{sourceState}
    </update>

    <select id="selectNodeByRoundId" resultMap="com.yy.gameecology.common.db.mapper.aov.AovMatchNodeMapper.BaseResultMap">
        select <include refid="com.yy.gameecology.common.db.mapper.aov.AovMatchNodeMapper.Base_Column_List" />
        from aov_match_node where phase_id = #{phaseId} and round_id = #{roundId}
        <if test="state != null">
            and `state` = #{state}
        </if>
        order by node_index
    </select>

    <select id="selectNode" resultMap="com.yy.gameecology.common.db.mapper.aov.AovMatchNodeMapper.BaseResultMap">
        select <include refid="com.yy.gameecology.common.db.mapper.aov.AovMatchNodeMapper.Base_Column_List" />
        from aov_match_node
        where phase_id = #{phaseId} and node_index = #{nodeIndex}
    </select>

    <select id="selectTeamNodesByPhaseId" resultMap="com.yy.gameecology.common.db.mapper.aov.AovMatchNodeMapper.BaseResultMap">
        select <include refid="com.yy.gameecology.common.db.mapper.aov.AovMatchNodeMapper.Base_Column_List" />
        from aov_match_node
        where phase_id = #{phaseId}
    </select>
    <select id="selectNodeByNodeIds"  resultMap="com.yy.gameecology.common.db.mapper.aov.AovMatchNodeMapper.BaseResultMap">
        select <include refid="com.yy.gameecology.common.db.mapper.aov.AovMatchNodeMapper.Base_Column_List" />
        from aov_match_node
        id in <foreach collection="nodeIds" item="nodeId" separator="," open="(" close=")">#{nodeId}</foreach>
    </select>
    <select id="selectRoundTeamNode" resultMap="com.yy.gameecology.common.db.mapper.aov.AovMatchNodeMapper.BaseResultMap">
        select <include refid="com.yy.gameecology.common.db.mapper.aov.AovMatchNodeMapper.Base_Column_List" />
        from aov_match_node
        where phase_id = #{phaseId} and round_id = #{roundId} and team_id = #{teamId}
        order by node_index desc
        limit 1
    </select>
    <select id="batchSelectByNodes" resultMap="com.yy.gameecology.common.db.mapper.aov.AovMatchNodeMapper.BaseResultMap">
        select <include refid="com.yy.gameecology.common.db.mapper.aov.AovMatchNodeMapper.Base_Column_List" />
        from aov_match_node
        where phase_id = #{phaseId}
        <if test="nodeIndexes != null and nodeIndexes.size() > 0">
            and node_index in <foreach collection="nodeIndexes" item="nodeIndex" separator="," open="(" close=")">#{nodeIndex}</foreach>
        </if>
    </select>

    <select id="selectMatchListNodes" resultMap="com.yy.gameecology.common.db.mapper.aov.AovMatchNodeMapper.BaseResultMap">
        select <include refid="com.yy.gameecology.common.db.mapper.aov.AovMatchNodeMapper.Base_Column_List" />
        from aov_match_node
        where phase_id = #{phaseId}
        <if test="states != null and states.size() > 0">
            and state in <foreach collection="states" item="state" separator="," open="(" close=")">#{state}</foreach>
        </if>
        <if test="teamStates != null and teamStates.size() > 0">
            and team_state in <foreach collection="teamStates" item="teamState" separator="," open="(" close=")">#{teamState}</foreach>
        </if>
    </select>
</mapper>