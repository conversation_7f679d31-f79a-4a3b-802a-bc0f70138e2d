package com.yy.gameecology.common.db.model.gameecology;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2021/1/6 19:24
 * @Modified:
 */

import com.yy.gameecology.common.annotation.TableColumn;
import org.springframework.jdbc.core.RowMapper;

@TableColumn(underline = true)
public class BroadcastRankResultConfig {
    public static String TABLE_NAME = "ge_broadcast_rank_result_config";
    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    private static final long serialVersionUID = 1L;

    public static RowMapper<BroadcastRankResultConfig> ROW_MAPPER = null;
    /**
     * 主键id
     */
    private Long id;

    /**
     * 活动id
     */
    private Long actId;

    /**
     * 榜单id
     */
    private Long rankId;

    /**
     * 贡献榜单ID（针对rank_id的）
     */
    private Long contriRankId;

    /**
     * 阶段id
     */
    private Long phaseId;

    /**
     * 榜单事件uri，1003=榜单开始，1004=榜单结束，1005=阶段开始，1006=阶段结束，1007=晋级结算开始，1008=晋级结算结束
     */
    private Integer uri;

    /**
     * 计算类型，0=普通榜单，1=pk胜方，2=pk负方，3=达成分值，4=贡献 (代替 rankType 的）
     */
    private Integer calcType;

    /**
     * 计算值，calc_type=3时是分值，其它是排名
     */
    private Integer calcValue;

    /**
     * 不指定默认是0，使用默认转换
     */
    private Integer moduleType;

    /**
     * 不指定默认是0，使用roletype
     */
    private Integer matchType;

    /**
     * 0=单业务广播（角色所属的模板），非零指定模板，逗号分隔
     */
    private String broadcastType;

    /**
     * 频道跳转标记，0：不跳转，非0：要跳转（默认为1）
     */
    private Integer skipFlag;

    /**
     * 广播文本
     */
    private String text;

    /**
     * 有效状态：1=有效
     */
    private Integer status;

    /**
     * 扩展数据
     */
    private String extjson;

    /**
     * 备注
     */
    private String remark;


    public BroadcastRankResultConfig() {
    }



    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getActId() {
        return actId;
    }

    public void setActId(Long actId) {
        this.actId = actId;
    }

    public Long getRankId() {
        return rankId;
    }

    public void setRankId(Long rankId) {
        this.rankId = rankId;
    }

    public Long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(Long phaseId) {
        this.phaseId = phaseId;
    }

    public Integer getUri() {
        return uri;
    }

    public void setUri(Integer uri) {
        this.uri = uri;
    }

    public Integer getModuleType() {
        return moduleType;
    }

    public void setModuleType(Integer moduleType) {
        this.moduleType = moduleType;
    }

    public Integer getMatchType() {
        return matchType;
    }

    public void setMatchType(Integer matchType) {
        this.matchType = matchType;
    }

    public String getBroadcastType() {
        return broadcastType;
    }

    public void setBroadcastType(String broadcastType) {
        this.broadcastType = broadcastType;
    }

    public Integer getSkipFlag() {
        return skipFlag;
    }

    public void setSkipFlag(Integer skipFlag) {
        this.skipFlag = skipFlag;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getContriRankId() {
        return contriRankId;
    }

    public void setContriRankId(Long contriRankId) {
        this.contriRankId = contriRankId;
    }

    public Integer getCalcType() {
        return calcType;
    }

    public void setCalcType(Integer calcType) {
        this.calcType = calcType;
    }

    public Integer getCalcValue() {
        return calcValue;
    }

    public void setCalcValue(Integer calcValue) {
        this.calcValue = calcValue;
    }

    public String getExtjson() {
        return extjson;
    }

    public void setExtjson(String extjson) {
        this.extjson = extjson;
    }
}
