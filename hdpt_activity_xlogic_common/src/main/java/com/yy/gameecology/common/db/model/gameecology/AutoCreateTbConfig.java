package com.yy.gameecology.common.db.model.gameecology;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-01-17 11:39
 **/
@Data
@TableColumn(underline = true)
public class AutoCreateTbConfig implements Serializable {
    public static String TABLE_NAME = "sys_auto_create_tb_config";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<AutoCreateTbConfig> ROW_MAPPER = null;

    /**
     * id
     */
    private Long id;

    /**
     * 配置了组件id，为组件专用表，活动必须要配置了对应的组件，才会对表进行初始化
     */
    private Integer cmptId;

    /**
     * config_name
     */
    private String configName;

    /**
     * config_script
     */
    private String configScript;

    /**
     * 1==有效 0==无效
     */
    private Integer state;

    /*
     *清理灰度数据的时候是否清理这个表
     */
    private Integer autoClearGreyData;

    /**
     * remark
     */
    private String remark;

    /**
     * create_time
     */
    private Date createTime;


}
