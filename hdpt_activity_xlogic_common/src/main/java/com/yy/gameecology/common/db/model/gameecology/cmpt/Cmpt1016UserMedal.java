package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Getter;
import lombok.Setter;
import org.springframework.jdbc.core.RowMapper;

import java.util.Date;

@Getter
@Setter
@TableColumn(underline = true)
public class Cmpt1016UserMedal {

    public static String TABLE_NAME = "cmpt_1016_user_medal";

    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<Cmpt1016UserMedal> ROW_MAPPER = (rs, rowNum) -> {
        Cmpt1016UserMedal result = new Cmpt1016UserMedal();
        result.setId(rs.getLong("id"));
        result.setActId(rs.getLong("act_id"));
        result.setUid(rs.getLong("uid"));
        result.setLevel(rs.getInt("level"));
        result.setCreateTime(rs.getTimestamp("create_time"));
        result.setUpdateTime(rs.getTimestamp("update_time"));

        return result;
    };

    private Long id;

    protected Long actId;

    protected Long uid;

    /**
     * 当前已获得的勋章等级
     */
    protected Integer level;

    protected Date createTime;

    protected Date updateTime;
}
