package com.yy.gameecology.common.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/4/17
 */
public enum YoPopupMessagePlatform {
    UNKNOWN,
    ANDROID,
    IOS,
    PC;

    private static final Map<Integer, YoPopupMessagePlatform> MAPPING;

    static {
        MAPPING = Arrays.stream(YoPopupMessagePlatform.values())
                .collect(Collectors.toUnmodifiableMap(YoPopupMessagePlatform::ordinal, Function.identity()));
    }

    public static YoPopupMessagePlatform from(int value) {
        return MAPPING.getOrDefault(value, YoPopupMessagePlatform.UNKNOWN);
    }

    public int binaryFLag() {
        // 01 android
        // 10 ios
        // 11 android&ios
        return 1 << (this.ordinal() - 1);
    }

}
