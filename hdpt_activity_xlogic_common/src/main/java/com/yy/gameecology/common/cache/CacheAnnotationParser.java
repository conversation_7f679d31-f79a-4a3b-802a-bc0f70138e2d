
package com.yy.gameecology.common.cache;

import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.cache.interceptor.CacheDefinition;
import com.yy.gameecology.common.cache.interceptor.DefaultCacheDefinition;
import org.springframework.core.BridgeMethodResolver;
import org.springframework.util.ClassUtils;

import java.lang.annotation.Annotation;
import java.lang.reflect.AnnotatedElement;
import java.lang.reflect.Method;

public class CacheAnnotationParser {

    public CacheDefinition computeCacheOperationDefinition(Method method, Class<?> targetClass) {

        // The method may be on an interface, but we need attributes from the target class.
        // If the target class is null, the method will be unchanged.
        Method specificMethod = ClassUtils.getMostSpecificMethod(method, targetClass);
        // If we are dealing with method with generic parameters, find the original method.
        specificMethod = BridgeMethodResolver.findBridgedMethod(specificMethod);

        // First try is the method in the target class.
        CacheDefinition opDef = findCacheOperation(specificMethod);
        if (opDef != null) {
            return opDef;
        }

        // Second try is the caching operation on the target class.
        opDef = findCacheDefinition(specificMethod.getDeclaringClass());
        if (opDef != null) {
            return opDef;
        }

        if (specificMethod != method) {
            // Fall back is to look at the original method.
            opDef = findCacheOperation(method);
            if (opDef != null) {
                return opDef;
            }
            // Last fall back is the class of the original method.
            return findCacheDefinition(method.getDeclaringClass());
        }
        return null;
    }

    protected CacheDefinition findCacheDefinition(Class<?> clazz) {
        return determineCacheDefinition(clazz);
    }

    protected CacheDefinition findCacheOperation(Method method) {
        return determineCacheDefinition(method);
    }

    protected CacheDefinition determineCacheDefinition(AnnotatedElement ae) {
        CacheDefinition attr = this.parseCacheAnnotation(ae);
        return attr;
    }

    protected CacheDefinition parseCacheAnnotation(AnnotatedElement ae) {
        Cached ann = findAnnotation(ae, Cached.class);
        if (ann != null) {
            DefaultCacheDefinition dcd = new DefaultCacheDefinition();
            dcd.setCacheName(ann.cacheName());
            dcd.setKey(ann.key());
            dcd.setTimeToLiveMillis(ann.timeToLiveMillis());
            dcd.setDynamicTimeToLive(ann.dynamicTimeToLive());
            return dcd;
        } else {
            return null;
        }
    }

    private <T extends Annotation> T findAnnotation(AnnotatedElement ae, Class<T> annotationType) {
        T ann = ae.getAnnotation(annotationType);
        if (ann == null) {
            for (Annotation metaAnn : ae.getAnnotations()) {
                ann = metaAnn.annotationType().getAnnotation(annotationType);
                if (ann != null) {
                    break;
                }
            }
        }
        return ann;
    }
}
