package com.yy.service;

public class SdkInitData {
    private long     appId;
    private String  s2sRegKey;
    
    private boolean bTestEnv;
    
    private boolean bConfigProxy;
    private boolean bConfigBroadcast;
    private boolean bConfigUnicast;
    private boolean bConfigRouteBus;
    
    private boolean bConfigHA;
    private long    HAappGroupId;
    private long     HAslaveMode;
    private long     HAmasterMode;
    
    private long[]     fakeIsp;
    private long     uHATimeout;
    
    /**
     * 
     * @param appId             -> apply in Service Platform http://nb.service.yy.com/
     * @param s2sRegKey         -> apply in Service Platform http://nb.service.yy.com/
     *                                  s2sRegKey apply name svcapp_%appid%, %appid% is your appId number, eg. svcapp_999
     * @param bTestEnv          -> to use or not use [Test Environment] wiki:http://121.14.43.221/dokuwiki/doku.php?id=test_env
     *                                  False for Production Env; True for Test Env, to test your APP logic
     * @param bConfigProxy      -> to use or not use [Proxy], to recv/send yy client msg
     * @param bConfigBroadcast  -> to use or not use [Broadcast], to send broadcast or group-user msg
     * @param bConfigUnicast    -> to use or not use [Unicast], to send unicast msg
     * @param bConfigRouteBus   -> to use or not use [Route Bus], to route message between Service Platform service process
     * 
     * @param bConfigHA         -> to use or not use [HA], master-slave standby mode to achieve High Available Service
     */
    public SdkInitData(long appId, String s2sRegKey, boolean bTestEnv,
        boolean bConfigProxy, boolean bConfigBroadcast, boolean bConfigUnicast) {
        this.appId              = appId;
        this.s2sRegKey          = s2sRegKey;
        
        this.bTestEnv           = bTestEnv;
        
        this.bConfigProxy       = bConfigProxy;
        this.bConfigBroadcast   = bConfigBroadcast;
        this.bConfigUnicast     = bConfigUnicast;
        this.bConfigRouteBus    = false;
        
        this.bConfigHA          = false;
        this.HAappGroupId       = 0;
        this.HAslaveMode        = 0;
        this.HAmasterMode       = 0;
        this.fakeIsp            = null;
        this.uHATimeout         = 60000;
    }

    public long getAppId() {
        return appId;
    }

    public void setAppId(long appId) {
        this.appId = appId;
    }

    public String getS2sRegKey() {
        return s2sRegKey;
    }

    public void setS2sRegKey(String s2sRegKey) {
        this.s2sRegKey = s2sRegKey;
    }

    public boolean isbTestEnv() {
        return bTestEnv;
    }

    public void setbTestEnv(boolean bTestEnv) {
        this.bTestEnv = bTestEnv;
    }

    public boolean isbConfigProxy() {
        return bConfigProxy;
    }

    public void setbConfigProxy(boolean bConfigProxy) {
        this.bConfigProxy = bConfigProxy;
    }

    public boolean isbConfigBroadcast() {
        return bConfigBroadcast;
    }

    public void setbConfigBroadcast(boolean bConfigBroadcast) {
        this.bConfigBroadcast = bConfigBroadcast;
    }

    public boolean isbConfigUnicast() {
        return bConfigUnicast;
    }

    public void setbConfigUnicast(boolean bConfigUnicast) {
        this.bConfigUnicast = bConfigUnicast;
    }

    public boolean isbConfigRouteBus() {
        return bConfigRouteBus;
    }

    public void setbConfigRouteBus(boolean bConfigRouteBus) {
        this.bConfigRouteBus = bConfigRouteBus;
    }

    public boolean isbConfigHA() {
        return bConfigHA;
    }

    public void setbConfigHA(boolean bConfigHA) {
        this.bConfigHA = bConfigHA;
    }

    public long getHAappGroupId() {
        return HAappGroupId;
    }

    public void setHAappGroupId(long hAappGroupId) {
        HAappGroupId = hAappGroupId;
    }

    public long getHAslaveMode() {
        return HAslaveMode;
    }

    public void setHAslaveMode(long hAslaveMode) {
        HAslaveMode = hAslaveMode;
    }

    public long getHAmasterMode() {
        return HAmasterMode;
    }

    public void setHAmasterMode(long hAmasterMode) {
        HAmasterMode = hAmasterMode;
    }

    public long[] getFakeIsp() {
        return fakeIsp;
    }

    public void setFakeIsp(long[] setIsp) {
        this.fakeIsp = setIsp;
    }

    public long getHATimeout() {
        return uHATimeout;
    }

    public void setHATimeout(long setHATimeout) {
        this.uHATimeout = setHATimeout;
    }
}
