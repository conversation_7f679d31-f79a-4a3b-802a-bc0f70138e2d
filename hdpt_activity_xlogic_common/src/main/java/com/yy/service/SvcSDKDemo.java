/**
 * <AUTHOR>
 */

package com.yy.service;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

class SvcSdkCallbackDemo implements SvcSdkProxyCallbackIf {
    public void onRecvProxyMsg(long suid, long uid, long topsid, long subsid, byte[] data,
            Map<Long, String> ext) {
        String userIp = ext.get(UserExtKey.UXK_UIP.ordinal());
        System.out.println("[Demo::onRecvProxyMsg] suid:" + suid + " uid:" + uid + " topsid:" + topsid
                + " subsid:" + subsid + " data.size:" + data.length + " userIp:" + userIp);

        // handle client message
        String strData = new String(data);
        System.out.println("[Demo::onRecvProxyMsg] data:" + strData);

        // answer client some message
        SvcSDK.getInstance().sdkSendUidMsg(suid, uid, data, Maps.newHashMap());
    }
}


public class SvcSDKDemo {
    private static final Logger log = LoggerFactory.getLogger(SvcSDKDemo.class);

    public static void main(String[] args) {
        System.out.println("svcsdk instance:" + SvcSDK.getInstance());
        // a. set app callback to svc sdk
        SvcSdkCallbackDemo cbDemo = new SvcSdkCallbackDemo();
        SvcSDK.getInstance().setAppCallback(cbDemo, null);

        SdkInitData init = new SdkInitData(999, "eef85c2c5b9a652b897d303d90ac1643", true, true, true, true);
        // b. svc sdk init
        SvcSDK.getInstance().sdkInit(init);
        // c. svc sdk start
        SvcSDK.getInstance().sdkStart();

        // TEST: subscribe range
        HashSet<Pair> rangeSet = Sets.newHashSet();
        rangeSet.add(new Pair(1000, 2000));
        rangeSet.add(new Pair(2000, 3000));
        SvcSDK.getInstance().sdkSubscribeRange(rangeSet.toArray(new Pair[0]));

        SvcSDKDemo appDemo = new SvcSDKDemo();
        while (true) {
            try {
                appDemo.Timer();
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    private long m_uTimerTick = 0;

    public void Timer() {
        ++m_uTimerTick;
        String msg = "hello timertick:" + m_uTimerTick;
        // TEST: broadcast interface
        final int i10 = 10, i20 = 20, i30 = 30;
        if (m_uTimerTick % i20 == 0) {
            String msgBc = msg + " by bc";
            SvcSDK.getInstance().sdkBcSid(31809645, 1901992634, msgBc.getBytes());
            System.out.println("bc msg.len:" + msgBc.length());

            long[] uidSet = {574773722, 50012774, 574773471};
            SvcSDK.getInstance().sdkBcUidsSeq(31809645, 574773722, uidSet, 100, msgBc.getBytes(), 0);
        }

        // TEST: unicast interface
        if (m_uTimerTick % i30 == 0) {
            String msgUc = msg + " by uc";
            SvcSDK.getInstance().sdkUcUidMsg(574773722, msgUc.getBytes());
            System.out.println("uc msg.len:" + msgUc.length());
        }

        // TEST: sdkGetUserOnlineCtx
        if (m_uTimerTick % i10 == 0) {
            UserOnlineCtx[] ctxes = SvcSDK.getInstance().sdkGetUserOnlineCtx(1100086401498L);
            if(ctxes != null) {
                for(UserOnlineCtx ctx : ctxes) {
                    if (ctx.isInvalid()) {
                        System.out.println("user clientAppKey:" + ctx.getClientAppKey() + " platformType:"
                                + ctx.getPlatformType() + " clientOsType:" + ctx.getClientOsType());
                    } else {
                        System.out.println("user 1100086401498 not online");
                    }
                }
            }
        }
    }
}
