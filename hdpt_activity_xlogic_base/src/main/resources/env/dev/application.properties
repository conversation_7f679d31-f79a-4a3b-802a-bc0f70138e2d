spring.application.name=hdzk_activity${group}
spring.main.allow-bean-definition-overriding=true

#spring redis session, can be none, redis, ...(need redis 2.8 + version support)
spring.session.store-type=none
logging.config=classpath:log4j2-dev.xml

#eagle eye report setting
eagle_eye_appver=2.5.0
eagle_eye_srvver=3.0.0
eagle_eye_scales=5,10,20,30,50,100,200,300,500,800,1000,2000,3000,5000,8000,15000,20000,40000,60000,120000

mybatis.mapper-locations=classpath:mapper/**/*.xml
mybatis.configuration.map-underscore-to-camel-case=true

# redis config
gameecology.redis.sentinel.master=ge_activity_redis_test_001
gameecology.redis.sentinel.nodes=fstst-group0001-001-sentinel.yy.com:20013,fstst-group0001-002-sentinel.yy.com:20013,fstst-group0001-003-sentinel.yy.com:20013
gameecology.redis.timeout=5s
gameecology.redis.lettuce.pool.max-active=25
gameecology.redis.lettuce.pool.max-idle=25
gameecology.redis.lettuce.pool.time-between-eviction-runs=60s

# redis config for global control or cache - added by guoliping/2020-12-23
gameecology.global.redis.sentinel.master=ge_activity_redis_test_001
gameecology.global.redis.sentinel.nodes=fstst-group0001-001-sentinel.yy.com:20013,fstst-group0001-002-sentinel.yy.com:20013,fstst-group0001-003-sentinel.yy.com:20013
gameecology.global.redis.database=0
gameecology.global.redis.timeout=5s
gameecology.global.redis.lettuce.pool.max-active=25
gameecology.global.redis.lettuce.pool.max-idle=25
gameecology.global.redis.lettuce.pool.time-between-eviction-runs=60s

# redis group config, The first group saves some old data and public data. Try not to use them
gameecology.group0.redis.sentinel.master=ge_activity_redis_test_001
gameecology.group0.redis.sentinel.nodes=fstst-group0001-001-sentinel.yy.com:20013,fstst-group0001-002-sentinel.yy.com:20013,fstst-group0001-003-sentinel.yy.com:20013
gameecology.group0.redis.database=1
gameecology.group0.redis.timeout=5s
gameecology.group0.redis.lettuce.pool.max-active=25
gameecology.group0.redis.lettuce.pool.max-idle=25
gameecology.group0.redis.lettuce.pool.time-between-eviction-runs=60s

gameecology.group1.redis.sentinel.master=ge_activity_redis_test_001
gameecology.group1.redis.sentinel.nodes=fstst-group0001-001-sentinel.yy.com:20013,fstst-group0001-002-sentinel.yy.com:20013,fstst-group0001-003-sentinel.yy.com:20013
gameecology.group1.redis.database=2
gameecology.group1.redis.timeout=5s
gameecology.group1.redis.lettuce.pool.max-active=25
gameecology.group1.redis.lettuce.pool.max-idle=25
gameecology.group1.redis.lettuce.pool.time-between-eviction-runs=60s

gameecology.group2.redis.sentinel.master=ge_activity_redis_test_001
gameecology.group2.redis.sentinel.nodes=fstst-group0001-001-sentinel.yy.com:20013,fstst-group0001-002-sentinel.yy.com:20013,fstst-group0001-003-sentinel.yy.com:20013
gameecology.group2.redis.database=3
gameecology.group2.redis.timeout=5s
gameecology.group2.redis.lettuce.pool.max-active=25
gameecology.group2.redis.lettuce.pool.max-idle=25
gameecology.group2.redis.lettuce.pool.time-between-eviction-runs=60s

gameecology.group3.redis.sentinel.master=ge_activity_redis_test_001
gameecology.group3.redis.sentinel.nodes=fstst-group0001-001-sentinel.yy.com:20013,fstst-group0001-002-sentinel.yy.com:20013,fstst-group0001-003-sentinel.yy.com:20013
gameecology.group3.redis.database=4
gameecology.group3.redis.timeout=5s
gameecology.group3.redis.lettuce.pool.max-active=25
gameecology.group3.redis.lettuce.pool.max-idle=25
gameecology.group3.redis.lettuce.pool.time-between-eviction-runs=60s

gameecology.group4.redis.sentinel.master=ge_activity_redis_test_001
gameecology.group4.redis.sentinel.nodes=fstst-group0001-001-sentinel.yy.com:20013,fstst-group0001-002-sentinel.yy.com:20013,fstst-group0001-003-sentinel.yy.com:20013
gameecology.group4.redis.database=5
gameecology.group4.redis.timeout=5s
gameecology.group4.redis.lettuce.pool.max-active=25
gameecology.group4.redis.lettuce.pool.max-idle=25
gameecology.group4.redis.lettuce.pool.time-between-eviction-runs=60s

gameecology.group101.redis.sentinel.master=hdpt_pika_test_001
gameecology.group101.redis.sentinel.nodes=fstst-group0001-001-sentinel.yy.com:20046,fstst-group0001-002-sentinel.yy.com:20046,fstst-group0001-003-sentinel.yy.com:20046
gameecology.group101.redis.database=0
gameecology.group101.redis.timeout=5s
gameecology.group101.redis.lettuce.pool.max-active=25
gameecology.group101.redis.lettuce.pool.max-idle=25
gameecology.group101.redis.lettuce.pool.time-between-eviction-runs=60s

########### Gameecology Bridge START ###############################################################
#thrift Gameecology Bridge server config, \u8D85\u65F6\u5355\u4F4D\uFF1A \u79D2, added by guoliping/2019-08-26
thrift_bridge_gameecologyproxy_client_registry=s2s://yzbb_gameecology_proxy_bridge_test:<EMAIL>
thrift_bridge_gameecologyproxy_client_timeout=300
thrift_bridge_gameecologyproxy_client_threads=10
########### Gameecology Bridge END ###############################################################

#thrift jiaoyou proxy client
thrift_jiaoyou_proxy_client_registry=s2s://fts_gameecology_bridge_test:<EMAIL>
thrift_jiaoyou_proxy_client_s2sname=fts_gameecology_bridge_test
thrift_jiaoyou_proxy_client_timeout=300
thrift_jiaoyou_proxy_client_thread=10

thrift_bridge_gamebabyactivity_client_registry=s2s://yzbb_gamebaby_activity_bridge_test:<EMAIL>
thrift_bridge_gamebabyactivity_client_s2sname=yzbb_gamebaby_activity_bridge_test
thrift_bridge_gamebabyactivity_client_timeout=300
thrift_bridge_gamebabyactivity_client_threads=10

thrift_fts_chpopular_client_s2s_name=fts_ch_popular_test

turnoverService_client_registry=s2s://ge_access_s2s_acct:<EMAIL>
turnoverService_client_s2sname=to_service_pre
turnoverContract_client_s2sname=to_contract_pre
turnoverGiftbagStat_client_s2sname=to_finance_pre
turnoverService_client_timeout=10
turnoverService_client_threads=10
turnoverService_http_host=https://turnover-pre.yy.com

thrift_fts_zhuiya_recommond_client_s2s_name=fts_zhuiya_recommend_test_v2

lightup.weideng.thrift.urls=thrift://**************:1077
lightup.weideng.thrift.timeout=10

#\u6D3B\u52A8\u4E2D\u53F0ranking\u8FDB\u7A0Bs2s Name
hdztRanking_client_s2sname=hdzt_ranking${group}_test${historyS2SSuffix:}
#\u6D3B\u52A8\u4E2D\u53F0award\u8FDB\u7A0Bs2s Name
thrift.client.hdztaward.s2sname=hdzt_award${group}_test${historyS2SSuffix:}

# kafka hdzt
#------------------------------------------------------------------------------------------
kafka.hdzt.ranking.updating.topic=hdzt${group}_ranking_updating_topic
kafka.hdzt.ranking.events.topic=hdzt${group}_ranking_events_topic
kafka.hdzt.award.issueNotice.topic=hdzt${group}_award_issueNotice_topic
kafka.hdzt.geact.consumer.group=hdzt${group}_ge_consumer_group
###########################################################################################

thrift_anti_cheat_gn_client_registry=s2s://ge_access_s2s_acct:<EMAIL>
thrift_anti_cheat_gn_client_s2sname=ysec_anti_cheat_gn_test
thrift_anti_cheat_gn_client_timeout=3
thrift_anti_cheat_gn_client_threads=3

#for ysec fts_props_agent thrift client settings, added by guoliping/2020-06-02, \uFFFD\uFFFD\uFFFD\uFFFD \u05A3\uFFFD\uFFFD \uFFFD\uFFFD\uFFFD\uFFFD\u05A7\uFFFD\uFFFD
thrift_fts_props_agent_client_registry=s2s://ge_access_s2s_acct:<EMAIL>
thrift_fts_props_agent_client_s2sname=fts_props_agent_test

ftsBaseInfoBridge_client_registry=s2s://ge_access_s2s_acct:<EMAIL>
ftsBaseInfoBridge_client_s2sname=fts_base_info_bridge_test
ftsBaseInfoBridge_client_timeout=10
ftsBaseInfoBridge_client_threads=10

hdztRanking_client_registry=s2s://ge_access_s2s_acct:<EMAIL>
hdztRanking_client_timeout=10
hdztRanking_client_threads=10

thrift.client.hdztaward.registry=s2s://ge_access_s2s_acct:<EMAIL>

actPwSupportService_client_registry=s2s://ge_access_s2s_acct:<EMAIL>
actPwSupportService_client_s2sname=peiwan_thrift_test
actPwSupportService_client_timeout=3
actPwSupportService_client_threads=10

########## zhuiwan_prize_issue config start###########
thrift_zhuiwan_prize_issue_client_registry=s2s://ge_access_s2s_acct:<EMAIL>
thrift_zhuiwan_prize_issue_client_timeout=30
thrift_zhuiwan_prize_issue_client_threads=10
thrift_zhuiwan_prize_issue_client_s2s_name=zhuiya_hdzt_thrift_test

thrift_skillcard_client_registry=s2s://ge_access_s2s_acct:<EMAIL>
thrift_skillcard_client_timeout=30
thrift_skillcard_client_threads=10
thrift_skillcard_client_s2s_name=skillcard_server_thrift_test
########### zhuiwan_prize_issue config end###########

########## fts_compere_group config start###########
thrift_fts_compere_group_client_registry=s2s://ge_access_s2s_acct:<EMAIL>
thrift_fts_compere_group_client_timeout=30
thrift_fts_compere_group_client_threads=10
thrift_fts_compere_group_client_s2s_name=fts_compere_group_test
########### fts_compere_group config end###########

thrift_fts_party_grade_client_registry=s2s://ge_access_s2s_acct:<EMAIL>
thrift_fts_party_grade_client_s2sname=fts_party_grade_test

########## fts_group_center config start###########
thrift_fts_group_center_client_registry=s2s://ge_access_s2s_acct:<EMAIL>
thrift_fts_group_center_client_timeout=30
thrift_fts_group_center_client_threads=10
thrift_fts_group_center_client_s2s_name=fts_group_center_test
########### fts_group_center end###########

########## fts_sensitive_word config start###########
thrift_fts_sensitive_word_client_registry=s2s://ge_access_s2s_acct:<EMAIL>
thrift_fts_sensitive_word_client_timeout=30
thrift_fts_sensitive_word_client_threads=10
thrift_fts_sensitive_word_client_s2s_name=fts_sensitive_test
########### fts_sensitive_word end###########

thrift_fts_supplement_client_s2s_name=fts_supplement_test

########### fts_recommend_data_base start #########
thrift.client.fts_recmd_data.registry=s2s://ge_access_s2s_acct:<EMAIL>
thrift.client.fts_recmd_data.s2sname=fts_recommend_data_base_test
thrift.client.fts_recmd_data.timeout=30
thrift.client.fts_recmd_data.threads=10
########### fts_recommend_data_base end #########

#########fts_room_manager start #########
thrift.client.fts_room_manager.registry=s2s://ge_access_s2s_acct:<EMAIL>
thrift.client.fts_room_manager.s2sname=fts_room_manager_test
thrift.client.fts_room_manager.timeout=30
thrift.client.fts_room_manager.threads=10
#########fts_room_manager end #########

########## fts_live_helper config start###########
thrift_fts_live_helper_client_registry=s2s://ge_access_s2s_acct:<EMAIL>
thrift_fts_live_helper_client_timeout=30
thrift_fts_live_helper_client_threads=10
thrift_fts_live_helper_client_s2s_name=fts_live_helper_test
########### fts_live_helper end###########

zhuiwanCommonList_client_registry=s2s://ge_access_s2s_acct:<EMAIL>
zhuiwanCommonList_client_s2sname=common_list_test
zhuiwanCommonList_client_timeout=10
zhuiwanCommonList_client_threads=10

########\u00BD\u00BB\u00D3\u00D1kafka############
kafka.jiaoyou.wx.server=kafkafs0014-test001.yy.com:8103,kafkafs0014-test002.yy.com:8103,kafkafs0014-test003.yy.com:8103,kafkafs0014-test004.yy.com:8103,kafkafs0014-test005.yy.com:8103

kafka.jiaoyou.sz.server=kafkafs0014-test001.yy.com:8103,kafkafs0014-test002.yy.com:8103,kafkafs0014-test003.yy.com:8103,kafkafs0014-test004.yy.com:8103,kafkafs0014-test005.yy.com:8103

kafka.jiaoyou-wx.producer.bootstrap-servers=kafkafs0014-test001.yy.com:8103,kafkafs0014-test002.yy.com:8103,kafkafs0014-test003.yy.com:8103,kafkafs0014-test004.yy.com:8103,kafkafs0014-test005.yy.com:8103
kafka.jiaoyou-wx.producer.acks=1

kafka.jiaoyou-sz.producer.bootstrap-servers=kafkafs0014-test001.yy.com:8103,kafkafs0014-test002.yy.com:8103,kafkafs0014-test003.yy.com:8103,kafkafs0014-test004.yy.com:8103,kafkafs0014-test005.yy.com:8103
kafka.jiaoyou-sz.producer.acks=1

########\u00BD\u00BB\u00D3\u00D1\u00CB\u00CD\u00C0\u00F1\u00CA\u00C2\u00BC\u00FE kafka############
kafka.jiaoyou.props.wx.server=kafkafs0014-test001.yy.com:8103,kafkafs0014-test002.yy.com:8103,kafkafs0014-test003.yy.com:8103,kafkafs0014-test004.yy.com:8103,kafkafs0014-test005.yy.com:8103
kafka.jiaoyou.props.wx.topic=propsUseInfoCross
kafka.jiaoyou.props.sz.server=kafkafs0014-test001.yy.com:8103,kafkafs0014-test002.yy.com:8103,kafkafs0014-test003.yy.com:8103,kafkafs0014-test004.yy.com:8103,kafkafs0014-test005.yy.com:8103
kafka.jiaoyou.props.sz.topic=propsUseInfoCross

########\u00BD\u00BB\u00D3\u00D1\u00B8\u00C7\u00D5\u00C2\u00CA\u00C2\u00BC\u00FE kafka############
kafka.jiaoyou.seal.wx.server=kafkafs0014-test001.yy.com:8103,kafkafs0014-test002.yy.com:8103,kafkafs0014-test003.yy.com:8103,kafkafs0014-test004.yy.com:8103,kafkafs0014-test005.yy.com:8103
kafka.jiaoyou.seal.wx.topic=propsSealInfoCross
kafka.jiaoyou.seal.sz.server=kafkafs0014-test001.yy.com:8103,kafkafs0014-test002.yy.com:8103,kafkafs0014-test003.yy.com:8103,kafkafs0014-test004.yy.com:8103,kafkafs0014-test005.yy.com:8103
kafka.jiaoyou.seal.sz.topic=propsSealInfoCross

########\u00BD\u00BB\u00D3\u00D1\u00D6\u00F7\u00B3\u00D6\u00BF\u00AA\u00B2\u00A5\u00CA\u00C2\u00BC\u00FE kafka############
kafka.jiaoyou.push_live.wx.server=kafkafs0014-test001.yy.com:8103,kafkafs0014-test002.yy.com:8103,kafkafs0014-test003.yy.com:8103,kafkafs0014-test004.yy.com:8103,kafkafs0014-test005.yy.com:8103
kafka.jiaoyou.push_live.wx.topic=msgPushCompereLiveNotifyCross
kafka.jiaoyou.push_live.sz.server=kafkafs0014-test001.yy.com:8103,kafkafs0014-test002.yy.com:8103,kafkafs0014-test003.yy.com:8103,kafkafs0014-test004.yy.com:8103,kafkafs0014-test005.yy.com:8103
kafka.jiaoyou.push_live.sz.topic=msgPushCompereLiveNotifyCross

########\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u04B6\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u00BC\uFFFD kafka############
kafka.jiaoyou.fight_end.wx.server=kafkafs0014-test001.yy.com:8103,kafkafs0014-test002.yy.com:8103,kafkafs0014-test003.yy.com:8103,kafkafs0014-test004.yy.com:8103,kafkafs0014-test005.yy.com:8103
kafka.jiaoyou.fight_end.wx.topic=appChannelFightEndInfoCross
kafka.jiaoyou.fight_end.sz.server=kafkafs0014-test001.yy.com:8103,kafkafs0014-test002.yy.com:8103,kafkafs0014-test003.yy.com:8103,kafkafs0014-test004.yy.com:8103,kafkafs0014-test005.yy.com:8103
kafka.jiaoyou.fight_end.sz.topic=appChannelFightEndInfoCross

kafka.jiaoyou.cross.producer.bootstrap-servers=kafkafs0014-test001.yy.com:8103,kafkafs0014-test002.yy.com:8103,kafkafs0014-test003.yy.com:8103,kafkafs0014-test004.yy.com:8103,kafkafs0014-test005.yy.com:8103

########\u00D3\u00AA\u00CA\u00D5\u00B3\u00E4\u00D6\u00B5\u00D6\u00A7\u00B8\u00B6\u00BD\u00E1\u00B9\u00FB\u00CD\u00A8\u00D6\u00AA kafka############
kafka.turnover.charge.server=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102

kafka.zhuiwan.producer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.zhuiwan.producer.acks=1

# kafka zhuiwan
kafka.zhuiwan.consumer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.zhuiwan.consumer.auto-offset-reset=latest

######## \u00C5\u00E3\u00CD\u00E6http\u00B7\u00FE\u00CE\u00F1\u00C5\u00E4\u00D6\u00C3############
peiwan.http_host=https://peipei-test.yy.com
peiwan.http_salt=P5Dr1hhpKtCUSDBSRPdh

## Yrpc
## https://git.yy.com/midwares/yrpc/yrpc-java-new/-/blob/master/dubbo-docs/v2/demo.nythrift.boot.md
dubbo.application.name=gameecology_java
dubbo.scan.base-packages=com.yy.gameecology.activity.worker.server.yrpc
dubbo.registries.yrpc-reg.id=yrpc-reg
dubbo.registries.yrpc-reg.address=s2s://test-wudang-meta.yy.com
dubbo.registries.yrpc-reg.username=${s2s.name}
dubbo.registries.yrpc-reg.password=${s2s.key}

# thrift reg
dubbo.registries.thrift-reg.id=thrift-reg
dubbo.registries.thrift-reg.address=s2s://test-wudang-meta.yy.com
dubbo.registries.thrift-reg.username=${s2s.hdzk.server.name}
dubbo.registries.thrift-reg.password=${s2s.hdzk.server.key}
dubbo.protocols.attach_nythrift.name=attach_nythrift
dubbo.protocols.attach_nythrift.port=30211

## mobserv cast
## https://git.yy.com/fanyuwen/yrpc-java-new/-/blob/master/dubbo-docs/v2/demo.cast.boot.md
mobserv.s2sname=mobsrv
mobserv.registryIds=yrpc-reg
mobserv.appId=60035
mobserv.connections=10
mobserv.timeout=3000
mobserv.retries=1

kafka.ge.ranking.events.outer.topic=ge_ranking_events_outer_topic_${group}

kafka.hdzk.wzry.game.events.topic=hdzk${group}_wzry_game_events_topic

kafka.hdzk.common.events.topic=hdzk${group}_common_events_topic

#micrometer config
management.metrics.tags.application=hdzk_activity${group}
management.metrics.tags.env=test
#management.server.port=8081
#management.endpoints.web.exposure.include=prometheus
#management.endpoints.enabled-by-default=false
#management.endpoint.prometheus.enabled=true

#fostress config
fostress.ack-mode=kafka
fostress.bootstrap-servers=kafkafs0014-test001.yy.com:8109,kafkafs0014-test002.yy.com:8109,kafkafs0014-test003.yy.com:8109,kafkafs0014-test004.yy.com:8109,kafkafs0014-test005.yy.com:8109,kafkafs0014-test006.yy.com:8109,kafkafs0014-test007.yy.com:8109
fostress.me=gameecology_activity${group}
fostress.center=fostress_center_server
fostress.reg-address=s2s://test-wudang-meta.yy.com
fostress.reg-username=${s2s.name}
fostress.reg-password=${s2s.key}

yboot.metrics.appName=hdzk_activity
yboot.metrics.serviceName=hdzk_activity_test_${group}

yboot.thread-pool.pools.general.core-threads=20
yboot.thread-pool.pools.general.max-threads=150
yboot.thread-pool.pools.general.queues=10000
yboot.thread-pool.pools.general.prefix=general-pool
yboot.thread-pool.pools.general.alive=60000
yboot.thread-pool.pools.general.rejected-class=java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy

yboot.thread-pool.pools.important.core-threads=50
yboot.thread-pool.pools.important.max-threads=150
yboot.thread-pool.pools.important.queues=10000
yboot.thread-pool.pools.important.prefix=important-pool
yboot.thread-pool.pools.important.alive=60000
yboot.thread-pool.pools.important.rejected-class=java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy

yboot.thread-pool.pools.sys_important.core-threads=150
yboot.thread-pool.pools.sys_important.max-threads=150
yboot.thread-pool.pools.sys_important.queues=30000
yboot.thread-pool.pools.sys_important.prefix=sys-important-pool
yboot.thread-pool.pools.sys_important.alive=60000
yboot.thread-pool.pools.sys_important.rejected-class=java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy

yboot.thread-pool.pools.delay_queue_consumer.core-threads=20
yboot.thread-pool.pools.delay_queue_consumer.max-threads=150
yboot.thread-pool.pools.delay_queue_consumer.queues=0
yboot.thread-pool.pools.delay_queue_consumer.prefix=delay-queue-pool
yboot.thread-pool.pools.delay_queue_consumer.alive=60000
yboot.thread-pool.pools.delay_queue_consumer.rejected-class=java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy

yboot.thread-pool.pools.method_log.core-threads=5
yboot.thread-pool.pools.method_log.max-threads=100
yboot.thread-pool.pools.method_log.queues=10000
yboot.thread-pool.pools.method_log.prefix=method-log-pool
yboot.thread-pool.pools.method_log.alive=60000

yboot.thread-pool.pools.online_channel.core-threads=30
yboot.thread-pool.pools.online_channel.max-threads=200
yboot.thread-pool.pools.online_channel.queues=1000
yboot.thread-pool.pools.online_channel.prefix=online-channel-pool
yboot.thread-pool.pools.online_channel.alive=60000
yboot.thread-pool.pools.online_channel.rejected-class=java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy

yboot.thread-pool.pools.eagle_report.core-threads=30
yboot.thread-pool.pools.eagle_report.max-threads=100
yboot.thread-pool.pools.eagle_report.queues=20000
yboot.thread-pool.pools.eagle_report.prefix=eagle-report-pool
yboot.thread-pool.pools.eagle_report.alive=60000

yboot.thread-pool.pools.kafka_msg_handler.core-threads=8
yboot.thread-pool.pools.kafka_msg_handler.max-threads=16
yboot.thread-pool.pools.kafka_msg_handler.queues=20000
yboot.thread-pool.pools.kafka_msg_handler.prefix=kafka-msg-handler-pool
yboot.thread-pool.pools.kafka_msg_handler.alive=60000
yboot.thread-pool.pools.kafka_msg_handler.rejected-class=java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy

yboot.thread-pool.pools.cmpt_build_layer.core-threads=30
yboot.thread-pool.pools.cmpt_build_layer.max-threads=60
yboot.thread-pool.pools.cmpt_build_layer.queues=5000
yboot.thread-pool.pools.cmpt_build_layer.prefix=cmpt-build-layer-pool
yboot.thread-pool.pools.cmpt_build_layer.alive=60000
yboot.thread-pool.pools.cmpt_build_layer.rejected-class=java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy

yboot.thread-pool.pools.bro_layer_schedule.core-threads=10
yboot.thread-pool.pools.bro_layer_schedule.max-threads=20
yboot.thread-pool.pools.bro_layer_schedule.queues=1000
yboot.thread-pool.pools.bro_layer_schedule.prefix=bro-layer-schedule-pool
yboot.thread-pool.pools.bro_layer_schedule.alive=60000
yboot.thread-pool.pools.bro_layer_schedule.rejectedClass=java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy
yboot.thread-pool.pools.bro_layer_schedule.type=scheduled

yboot.thread-pool.pools.aggregation_schedule.core-threads=10
yboot.thread-pool.pools.aggregation_schedule.max-threads=20
yboot.thread-pool.pools.aggregation_schedule.queues=1000
yboot.thread-pool.pools.aggregation_schedule.prefix=aggregation-schedule-pool
yboot.thread-pool.pools.aggregation_schedule.alive=60000
yboot.thread-pool.pools.aggregation_schedule.rejectedClass=java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy
yboot.thread-pool.pools.aggregation_schedule.type=scheduled

yy.env=test
yy.app-name=${spring.application.name}
yy.webdb.enable=true
yy.webdb.auth-user=1223003332
yy.webdb.auth-key=b09b7bf5_d243
# core pool size
yy.webdb.pool-size=20

yy.cul.enable=true
yy.cul.auth-user=1223003332
yy.cul.auth-key=b09b7bf5_d243
yy.cul.pool-size=20

## &offset=0&size=100
skill.card.white.list.request.url=https://common-list-test.yy.com/list/common/sid/list?business=zhuiwan&module=peiwan_plugin_rank
audit.mms.text.bulk.url=https://twapi-test.yy.com/skynet/bulk/api

thrift_zhuiya_client_registry=s2s://ge_access_s2s_acct:<EMAIL>
thrift_zhuiya_client_timeout=30
thrift_zhuiya_client_threads=10
thrift_zhuiya_client_s2s_name=zhuiya_server_thrift_test

saibaoService_client_registry=s2s://ge_access_s2s_acct:<EMAIL>
saibaoService_client_s2sname=game_gateway
saibaoService_client_timeout=10
saibaoService_client_threads=10

kafka.zhuiya.producer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.zhuiya.producer.acks=1



# kafka consumer refactor 20230707

kafka.activity.group-id=gameecology_activity_${group}

kafka.gamebaby.server=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.gamebaby.group=gameecology_act_${group}

kafka.hdpt.wx.server=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.hdpt.wx.producer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.hdpt.wx.producer.acks=1
kafka.hdpt.wx.group=gameecology_act_${group}

kafka.hdpt.wx-xdc.server=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.hdpt.wx-xdc.producer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.hdpt.wx-xdc.producer.acks=1
kafka.hdpt.wx-xdc.group=gameecology_act_${group}

kafka.jiaoyou.sz.consumer.bootstrap-servers=kafkafs0014-test001.yy.com:8103,kafkafs0014-test002.yy.com:8103,kafkafs0014-test003.yy.com:8103,kafkafs0014-test004.yy.com:8103,kafkafs0014-test005.yy.com:8103
kafka.jiaoyou.sz.consumer.auto-offset-reset=latest

kafka.jiaoyou.wx.consumer.bootstrap-servers=kafkafs0014-test001.yy.com:8103,kafkafs0014-test002.yy.com:8103,kafkafs0014-test003.yy.com:8103,kafkafs0014-test004.yy.com:8103,kafkafs0014-test005.yy.com:8103
kafka.jiaoyou.wx.consumer.auto-offset-reset=latest

kafka.turnover.consumer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.turnover.consumer.auto-offset-reset=latest

kafka.gamebaby.consumer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.gamebaby.consumer.auto-offset-reset=latest

kafka.hdpt.wx.consumer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.hdpt.wx.consumer.auto-offset-reset=latest

kafka.subchannel.chat.consumer.bootstrap-servers=dmq-test.yy.com:9099
kafka.subchannel.chat.consumer.auto-offset-reset=latest

kafka.hdpt.wx-xdc.consumer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.hdpt.wx-xdc.consumer.auto-offset-reset=latest

kafka.jiaoyou.wx.group=group_friend_combo_end_event_act_${group}
kafka.jiaoyou.sz.group=group_friend_combo_end_event_act_${group}

kafka.jiaoyou.props.wx.group=friend_props_event_gameecology_activity_${group}
kafka.jiaoyou.props.sz.group=friend_props_event_gameecology_activity_${group}

kafka.jiaoyou.seal.wx.group=friend_seal_event_gameecology_activity_${group}
kafka.jiaoyou.seal.sz.group=friend_seal_event_gameecology_activity_${group}

kafka.jiaoyou.push_live.wx.group=friend_push_live_event_gameecology_activity_${group}
kafka.jiaoyou.push_live.sz.group=friend_push_live_event_gameecology_activity_${group}

kafka.jiaoyou.fight_end.wx.group=friend_fight_end_event_gameecology_activity_${group}
kafka.jiaoyou.fight_end.sz.group=friend_fight_end_event_gameecology_activity_${group}
kafka.jiaoyou.compere-online.wx.group=friend_compere_online_hdzk_activity_${group}
kafka.jiaoyou.compere-online.sz.group=friend_compere_online_hdzk_activity_${group}
skillcard_room_server.s2sname=skillcard_server_yrpc

online.channel.s2s=online_channel_server_test
currency.s2s=zhuiya_currency_server

# yrpc - service discovery
dubbo.consumer.parameters.eagle.appname=${MY_PROJECT_NAME:${eagle_eye_appname}}
dubbo.consumer.parameters.eagle.progress=yrpc-client-test
dubbo.consumer.parameters.rpclog.thresholdMillis=500
dubbo.consumer.filter=eagle
dubbo.consumer.loadbalance=smart
dubbo.consumer.lbrouters=room,isp

dubbo.provider.parameters.rpclog.thresholdMillis=500
dubbo.provider.parameters.eagle.appname=${MY_PROJECT_NAME:${eagle_eye_appname}}
dubbo.provider.filter=eagle

dubbo.registries.consumer-reg.id=consumer-reg
dubbo.registries.consumer-reg.address=s2s://test-wudang-meta.yy.com
dubbo.registries.consumer-reg.username=ge_access_s2s_acct
dubbo.registries.consumer-reg.password=a2425936e0ec81419fb841863370df32072bdd5a7ccc5a6d012d4383ae98e7a6a7fc45f807f1a319

# sa
thrift.service-agent.url=nythrift://${MY_HOST_IP}:12500

#xpush
xpush.batch-push-by-uid.uri=https://push-openapi-test.yy.com/push/batchPushByUid
