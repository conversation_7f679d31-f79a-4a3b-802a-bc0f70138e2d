#web server port, \u4E34\u65F6\u7528\u4E8E\u6D4B\u8BD5\uFF0C \u6D4B\u8BD5\u5B8C\u6BD5\u540E\u8981\u5173\u95ED\uFF01
web.server.port=7042


thrift_gameecologybridge_server_port=10222
thrift_gameecologybridge_server_timeout=3600
thrift_gameecologybridge_server_registry=s2s://hdzk_server_test7:<EMAIL>
thrift_gameecologybridge_server_t=yzbb_is_good

#java service sdk, \u5DF2\u63D0\u524D\u7533\u8BF7\u597D\uFF0C\u9700\u8981\u65F6\u53EF\u76F4\u63A5\u4F7F\u7528 - added by guoliping / 2019-10-23
svcsdk.appId=60448
svcsdk.s2sRegKey=1a418cf75eb3d8fae85daca15ee0f7d67bcd0722cba9112c
svcsdk.bTestEnv=false

#eagle eye report setting
eagle_eye_appname=gameecology_activity7_test
eagle_eye_srvname=activity7_test.gameecology.yy.com

# redis config
gameecology.redis.database=7

###########################################################################################
#hdzt kafka
kafka.hdzt-wx.consumer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.hdzt-wx.consumer.auto-offset-reset=latest
kafka.hdzt-wx.producer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.hdzt-wx.producer.acks=all

kafka.hdzt-sz.consumer.bootstrap-servers=kafkafs0014-test001.yy.com:8103,kafkafs0014-test002.yy.com:8103,kafkafs0014-test003.yy.com:8103,kafkafs0014-test004.yy.com:8103,kafkafs0014-test005.yy.com:8103
kafka.hdzt-sz.consumer.auto-offset-reset=latest
kafka.hdzt-sz.producer.bootstrap-servers=kafkafs0014-test001.yy.com:8103,kafkafs0014-test002.yy.com:8103,kafkafs0014-test003.yy.com:8103,kafkafs0014-test004.yy.com:8103,kafkafs0014-test005.yy.com:8103
kafka.hdzt-sz.producer.acks=all
###################################kafka end###############################################################
#udb sa
thrift.service-agent-new.url=thrift://${MY_HOST_IP}:12500;thrift://${MY_HOST_IP}:12500;thrift://${MY_HOST_IP}:12500

# kafka svc call back
yy.kafka.svc.gateway.wx.consumer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
yy.kafka.svc.gateway.wx.consumer.auto-offset-reset=latest
yy.kafka.svc.gateway.wx.consumer.topic=svc_platform_gateway_callback_geact_topic_7
yy.kafka.svc.gateway.wx.consumer.group-id=group_svc_gateway_callback_gameecology_activity_7

# mysql datasource for db gameecology
mysql.gameecology.jdbcUrl=jdbc:mysql://***********:8066/hdzk7_down?useUnicode=true&characterEncoding=UTF8&zeroDateTimeBehavior=convertToNull&socketTimeout=20000&connectTimeout=20000
mysql.gameecology.username=udb_data@zwyz_mysql_test
mysql.gameecology.driver-class-name=com.mysql.jdbc.Driver
mysql.gameecology.maxPoolSize=10

s2s.name=hdzk_activity7_test
s2s.key=851a88478b6153521cba417810c1168d32bfc6880625545dada12b7b9cdf2f5d8068cd83924a7a28

s2s.hdzk.server.name=hdzk_server_test7
s2s.hdzk.server.token=yzbb_is_good