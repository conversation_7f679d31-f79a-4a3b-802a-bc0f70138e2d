#web server port, \u4E34\u65F6\u7528\u4E8E\u6D4B\u8BD5\uFF0C \u6D4B\u8BD5\u5B8C\u6BD5\u540E\u8981\u5173\u95ED\uFF01
web.server.port=7037

thrift_gameecologybridge_server_port=30211
thrift_gameecologybridge_server_timeout=300
thrift_gameecologybridge_server_registry=


#java service sdk, \u5DF2\u63D0\u524D\u7533\u8BF7\u597D\uFF0C\u9700\u8981\u65F6\u53EF\u76F4\u63A5\u4F7F\u7528 - added by guoliping / 2019-10-23
svcsdk.appId=62446
svcsdk.s2sRegKey=1a418cf75eb3d8fab239ac8e59abc34ac2d116e3c4d11c87
svcsdk.bTestEnv=false

#eagle eye report setting
eagle_eye_appname=gameecology_activity2_test
eagle_eye_srvname=activity2_test.gameecology.yy.com

# redis config
gameecology.redis.database=12


###########################################################################################
#hdzt kafka
kafka.hdzt-wx.consumer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.hdzt-wx.consumer.auto-offset-reset=latest
kafka.hdzt-wx.producer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.hdzt-wx.producer.acks=all

kafka.hdzt-sz.consumer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.hdzt-sz.consumer.auto-offset-reset=latest
kafka.hdzt-sz.producer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.hdzt-sz.producer.acks=all
###################################kafka end###############################################################
#udb sa
thrift.service-agent-new.url=thrift://${MY_HOST_IP}:12500;thrift://${MY_HOST_IP}:12500;thrift://${MY_HOST_IP}:12500

# kafka svc call back
yy.kafka.svc.gateway.wx.consumer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
yy.kafka.svc.gateway.wx.consumer.auto-offset-reset=latest
yy.kafka.svc.gateway.wx.consumer.topic=svc_platform_gateway_callback_geact_topic_2
yy.kafka.svc.gateway.wx.consumer.group-id=group_svc_gateway_callback_gameecology_activity_2

# mysql datasource for db gameecology
mysql.gameecology.jdbcUrl=jdbc:mysql://***********:8066/hdzk2_west?useUnicode=true&characterEncoding=UTF8&zeroDateTimeBehavior=convertToNull&socketTimeout=20000&connectTimeout=20000
mysql.gameecology.username=udb_data@zwyz_mysql_test
mysql.gameecology.driver-class-name=com.mysql.jdbc.Driver
mysql.gameecology.maxPoolSize=10

s2s.name=hdzk_activity2_test
s2s.key=851a88478b615352de113d8391a9595f470cd6242c65b676cad37706b56d3d0eff7fd40a5277db28

s2s.hdzk.server.name=hdzk_server_test2
s2s.hdzk.server.token=yzbb_is_good