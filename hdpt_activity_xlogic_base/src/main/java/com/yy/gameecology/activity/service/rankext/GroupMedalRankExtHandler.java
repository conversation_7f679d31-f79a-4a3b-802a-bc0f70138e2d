package com.yy.gameecology.activity.service.rankext;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.yy.apphistory.y2022.act2022051001.bean.GrowMedalInfo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.rank.RankItemBase;
import com.yy.gameecology.activity.bean.rank.RankItemUserAnchor;
import com.yy.gameecology.activity.bean.rank.UserRankItem;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RankingInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * desc:用户成长体系勋章扩展信息
 *
 * @createBy 曾文帜
 * @create 2022-07-15 18:37
 **/
@Component
public class GroupMedalRankExtHandler implements RankExtHandler {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private final static String GET_DATA_URL = "activity6-ge.yy.com/act2022046003/batchUserMedalInfos";


    private static final Long GROW_ACT_ID = 2022046003L;

    private static final  Long GROW_INDEX = 500L;


    private final static Map<Long, String> GROW_LEVEL_IMG = ImmutableMap.of(
            1L, "https://activitysys.bs2cdn.yy.com/16-01.png",
            2L, "https://activitysys.bs2cdn.yy.com/16-02.png",
            3L, "https://activitysys.bs2cdn.yy.com/16-03.png",
            4L, "https://activitysys.bs2cdn.yy.com/16-04.png");


    @Override
    public List<String> supportKeys() {
        return Arrays.asList(
                "2022081001_23_100",
                "2022081001_22_100",
                "2022081001_24_100",
                "2022081001_25_100",

                "2022061001_21_100",
                "2022061001_22_100",
                "2022061001_23_100",
                "2022061001_31_101",
                "2022061001_32_101",
                "2022061001_33_101",

                "2022091001_64_100",
                "2022091001_66_100",
                "2022091001_67_100",
                "2022101001_81_100",
                "2022101001_82_100",
                "2022101001_83_100");
    }


    @Override
    public List<Object> addViewExt(GetRankReq rankReq, RankingInfo rankingInfo, List<Rank> ranks, List<Object> objectList) {
        if (CollectionUtils.isEmpty(objectList)) {
            return objectList;
        }
        long actId = rankReq.getActId();
        boolean isCp = objectList.get(0) instanceof RankItemUserAnchor;
        if (isCp) {
            List<String> uidStr = objectList.stream().map(rank -> ((RankItemUserAnchor) rank))
                    .map(RankItemUserAnchor::getUserRankItem).map(UserRankItem::getKey).collect(Collectors.toList());
            Map<String, GrowMedalInfo> medalInfos = batchGrowMedalInfos(actId, String.join(",", uidStr));
            objectList.stream().map(rank -> ((RankItemUserAnchor) rank)).map(RankItemUserAnchor::getUserRankItem)
                    .forEach(rank -> {
                        if (rank.getViewExt() == null) {
                            rank.setViewExt(Maps.newHashMap());
                        }
                        GrowMedalInfo medalInfo = medalInfos.get(rank.getKey());
                        if (medalInfo != null) {
                            rank.getViewExt().put("growMedalLevel", medalInfo.getGrowMedalLevel() + "");
                            rank.getViewExt().put("growMedalUrl", medalInfo.getGrowMedalUrl());
                            rank.getViewExt().put("growMinMedalUrl", medalInfo.getGrowMinMedalUrl());
                        }
                    });
        } else {
            List<String> uidStr = objectList.stream().map(rank -> ((RankItemBase) rank)).map(RankItemBase::getKey).collect(Collectors.toList());
            Map<String, GrowMedalInfo> medalInfos = batchGrowMedalInfos(actId, String.join(",", uidStr));
            objectList.stream().map(rank -> ((RankItemBase) rank))
                    .forEach(rank -> {
                        if (rank.getViewExt() == null) {
                            rank.setViewExt(Maps.newHashMap());
                        }
                        GrowMedalInfo medalInfo = medalInfos.get(rank.getKey());
                        if (medalInfo != null) {
                            rank.getViewExt().put("growMedalLevel", medalInfo.getGrowMedalLevel() + "");
                            rank.getViewExt().put("growMedalUrl", medalInfo.getGrowMedalUrl());
                            rank.getViewExt().put("growMinMedalUrl", medalInfo.getGrowMinMedalUrl());
                        }
                    });
        }


        return objectList;
    }


    public Map<String, GrowMedalInfo> batchGrowMedalInfos(Long actId, String uidStr) {

        RestTemplate restTemplate = new RestTemplate();

        String url = "https://" + (SysEvHelper.isDeploy() ? GET_DATA_URL : "test-" + GET_DATA_URL);
        JSONObject request = new JSONObject();
        request.put("actId", GROW_ACT_ID);
        request.put("index", GROW_INDEX);
        request.put("uidStr", uidStr);

        HttpEntity httpEntity = new HttpEntity(request.toJSONString(), new HttpHeaders());

        ResponseEntity<Response> entity = restTemplate.postForEntity(url, httpEntity, Response.class);
        if (SysEvHelper.isDev()) {
            log.info("batchGrowMedalInfos uidStr:{} result:{}", uidStr, JSON.toJSONString(entity));
        }
        if (entity.getStatusCodeValue() != HttpStatus.OK.value()) {
            return Maps.newHashMap();
        }
        List<GrowMedalInfo> growMedalInfos = JSON.parseArray(JSON.toJSONString(entity.getBody().getData()), GrowMedalInfo.class);
        for (GrowMedalInfo growMedalInfo : growMedalInfos) {
            if (growMedalInfo.getGrowMedalLevel() == null) {
                continue;
            }
            growMedalInfo.setGrowMinMedalUrl(GROW_LEVEL_IMG.get(growMedalInfo.getGrowMedalLevel()));
        }
        return growMedalInfos.stream().collect(Collectors.toMap(GrowMedalInfo::getMember, Function.identity()));
    }

}
