package com.yy.gameecology.activity.service;

import cn.hutool.extra.template.TemplateConfig;
import cn.hutool.extra.template.TemplateEngine;
import cn.hutool.extra.template.TemplateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.ScheduledExt;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.SendGiftEvent;
import com.yy.gameecology.activity.bean.SettleViewConfig;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.client.thrift.*;
import com.yy.gameecology.activity.client.yrpc.DanmakuActivityClient;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.bean.ChannelBaseInfo;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.client.WebdbSinfoClient;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.client.WebdbThriftClientForSa;
import com.yy.gameecology.common.client.WebdbUinfoClient;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeActAttrConst;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.support.BaseBridgeThriftClient;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.java.webdb.WebdbChannelInfo;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.gameecology_bridge.StringRequest;
import com.yy.thrift.gameecology_bridge.StringResponse;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import com.yy.thrift.zhuiwan_newfamily.FamilyBasicInfo;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Nullable;
import javax.annotation.PostConstruct;
import java.io.UnsupportedEncodingException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2019/8/26
 */
@Component
public class CommonService {

    private static final Logger log = LoggerFactory.getLogger(CommonService.class);

    private static final int TWO_HUNDRED = 200;

    private static final int THREE_HUNDRED = 300;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private BridgeGamebabyActivityThriftClient bridgeGamebabyActivityThriftClient;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private SecuserinfoThriftClient secuserinfoThriftClient;
    @Autowired
    private WebdbThriftClient webdbThriftClient;

    @Autowired
    private WhiteListService whiteListService;

    @Lazy
    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private CulClient culClient;

    @Autowired
    private WebdbUinfoClient webdbUinfoClient;

    @Autowired
    private WebdbSinfoClient webdbSinfoClient;

    @Autowired
    private DanmakuActivityClient danmakuActivityClient;

    @Autowired
    private WebdbThriftClientForSa webdbThriftClientForSa;

    @Autowired
    private ZhuiwanChannelClient zhuiwanChannelClient;

    @Autowired
    private ZhuiWanPrizeIssueServiceClient zhuiWanPrizeIssueServiceClient;

    @Value("${skill.card.white.list.request.url}")
    private String skillCardWhiteListRequestUrl;

    @Autowired
    private ZhuiwanRoomInfoClient zhuiwanRoomInfoClient;

    private List<ChannelInfo> skillCardChannelList = new ArrayList<>();

    /**
     * 活动时间偏移量, 方便测试
     */
    public static final String ACT_VIRT_TIME_OFFSET = "act_virt_time_offset_v2";
    public static final String YYLOGO = "yylogo";
    public static final String NICK = "nick";
    public static final String YYNO = "yyno";


    @Autowired
    private OnlineChannelService onlineChannelService;


    /**
     * 当前环境是否是灰度环境
     *
     * @return
     */
    public boolean isGrey(Long actId) {
        return isActivityOneFlag(actId, GeActAttrConst.ACTIVITY_GREY_STATUS, Const.ONE);
    }

    /**
     * 获取活动扩展配置
     */
    public String getActivityConfigExt(long actId, String name, String defVal) {
        String value = cacheService.getActAttrValue(actId, name);
        return (StringUtil.isBlank(value)) ? StringUtil.trim(defVal) : value.trim();
    }

    /**
     * 判断标志位是否为 1
     */
    public boolean isActivityOneFlag(long actId, String name, int defVal) {
        return isActivityEqual(actId, name, String.valueOf(defVal), "1");
    }

    /**
     * 判断参数值是否等于期望值
     */
    public boolean isActivityEqual(long actId, String name, String defVal, String expectedValue) {
        String value = getActivityConfigExt(actId, name, defVal);
        return value.equals(expectedValue);
    }

    /**
     * @return true == 灰度测试的时候，在白名单内，允许往下写操作。例如非通过发奖中台发奖（发奖中台已有控制） 和广播、单播
     */
    public boolean checkWhiteList(long actId, RoleType roleType, String roleValue) {
        if (isGrey(actId)) {
            return hdztRankingThriftClient.checkWhiteList(actId, roleType, roleValue);
        }

        return true;
    }

    /**
     * 批量检查角色的白名单，只有全部在白名单才通过
     * @param actId
     * @param roleList
     * @return
     */
    public boolean checkWhiteList(long actId, List<Pair<RoleType, String>> roleList) {
        if (isGrey(actId)) {
            for (Pair<RoleType, String> role : roleList) {
                if (!hdztRankingThriftClient.checkWhiteList(actId, role.getKey(), role.getValue())) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * Description: 获取活动的模拟时间
     *
     * @return
     */
    public Date getNow(long actId) {
        Date now = new Date();
        if (!SysEvHelper.isDeploy() || isGrey(actId)) {
            String actVirtTime = hdztRankingThriftClient.getTimeString(actId);
            if (StringUtils.hasLength(actVirtTime)) {
                LocalDateTime virt = LocalDateTime.parse(actVirtTime, DateUtil.YYYY_MM_DD_HH_MM_SS);
                now = DateUtil.toDate(virt);
            }
        }
        return now;
    }

    public long getTime(long time, long actId) {
        if (!SysEvHelper.isDeploy() || isGrey(actId)) {
            String actVirtTime = hdztRankingThriftClient.getTimeString(actId);
            if (StringUtils.hasLength(actVirtTime)) {
                LocalDateTime virt = LocalDateTime.parse(actVirtTime, DateUtil.YYYY_MM_DD_HH_MM_SS);
                return DateUtil.toDate(virt).getTime();
            }
        }

        return time;
    }

    /**
     * 获取活动的模拟时间
     *
     * @param actId
     * @return
     */
    public LocalDateTime getNowDateTime(long actId) {
        return LocalDateTime.parse(DateUtil.format(getNow(actId)), DateUtil.YYYY_MM_DD_HH_MM_SS);
    }

    /**
     * 活动全局设置
     * <p>
     * 活动是否在结算中
     *
     * @param actId 活动id
     * @return true代表在结算中
     */
    public boolean inSettle(long actId) {
        return inSettle(actId, 0, 0, "");
    }


    /**
     * 是否在结算中
     * <p>
     * 实现逻辑：如果 doubleCheck== true 配置命中了，尝试读取redis key ,同时满足的时候才展示结算中
     *
     * @param actId   活动id
     * @param rankId  榜单id
     * @param phaseId 阶段id
     * @param dayCode 日期，日榜用到 yyyy-MM-dd,如果不填则不匹配
     * @return true 结算中
     */
    public boolean inSettle(long actId, long rankId, long phaseId, String dayCode) {
        Date now = getNow(actId);
        long nowTime = now.getTime();
        List<SettleViewConfig> settleViewConfigs = cacheService.getSettleViewConfig(actId);
        if (CollectionUtils.isEmpty(settleViewConfigs)) {
            return false;
        }
        List<SettleViewConfig> actConfigs = settleViewConfigs.stream()
                .filter(x -> x.getRankId() == rankId && x.getPhaseId() == phaseId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(actConfigs)) {
            return false;
        }
        for (SettleViewConfig config : actConfigs) {
            if (config.getStartTime() == null || config.getEndTime() == null) {
                continue;
            }
            if (nowTime < config.getStartTime().getTime() || nowTime > config.getEndTime().getTime()) {
                continue;
            }
            if (StringUtil.isEmpty(dayCode) || dayCode.equals(config.getDayCode())) {
                //需要redis也设置了才算是结算中
                if (config.isDoubleCheck()) {
                    String settleStatus = stringRedisTemplate.opsForValue().get(Const.addActivityPrefix(actId, "settle_status"));
                    return "1".equals(settleStatus);
                } else {
                    return true;
                }
            }
        }

        return false;
    }


    public @Nullable
    String getActAttr(long actId, String key) {
        Map<String, String> map = cacheService.getActAttrMap().get(actId);
        return map != null ? map.get(key) : null;
    }

    public List<ChannelInfo> listFamilySsid2(long familyId) {
        return zhuiwanRoomInfoClient.listFamilyValidRoom(familyId);
    }

    /**
     * 通用read
     *
     * @param rid
     * @param fid
     * @param data
     * @param seq
     * @param extjson
     * @param respType
     * @param <T>
     * @return
     */
    public <T> T readGamebaby(int rid, int fid, Object data, String seq, String extjson, Class<T> respType) {
        try {
            StringRequest req = new StringRequest();
            req.setSeq(StringUtil.nonNull(seq));
            req.setRid(rid);
            req.setFid(fid);
            req.setData(JSON.toJSONString(data));
            req.setIp(SystemUtil.getIp());
            req.setTs(System.currentTimeMillis() / 1000);
            req.setExtjson(StringUtil.nonNull(extjson));
            req.setSource(Const.GE_SOURCE.GAMEECOLOGY_ACTIVITY);
            StringResponse resp = readWithRetry(req, 0, bridgeGamebabyActivityThriftClient, "readGamebaby");
            if (respType != null && resp != null && StringUtils.hasLength(resp.getData())) {
                return JSON.parseObject(resp.getData(), respType);
            }
        } catch (Exception e) {
            log.error("[readGamebaby] err:{}", e.getMessage(), e);
        }
        return null;
    }

    @Cached(timeToLiveMillis = 30000)
    public <T> T readGamebabyCache(int rid, int fid, Object data, String seq, String extjson, Class<T> respType) {
        try {
            StringRequest req = new StringRequest();
            req.setSeq(StringUtil.nonNull(seq));
            req.setRid(rid);
            req.setFid(fid);
            req.setData(JSON.toJSONString(data));
            req.setIp(SystemUtil.getIp());
            req.setTs(System.currentTimeMillis() / 1000);
            req.setExtjson(StringUtil.nonNull(extjson));
            req.setSource(Const.GE_SOURCE.GAMEECOLOGY_ACTIVITY);
            StringResponse resp = readWithRetry(req, 0, bridgeGamebabyActivityThriftClient, "readGamebaby");
            if (respType != null && resp != null && StringUtils.hasLength(resp.getData())) {
                return JSON.parseObject(resp.getData(), respType);
            }
        } catch (Exception e) {
            log.error("[readGamebaby] err:{}", e.getMessage(), e);
        }
        return null;
    }

    private StringResponse readWithRetry(StringRequest req, int retry, BaseBridgeThriftClient client, String busi) {
        StringResponse resp = client.read(req);
        final long result = resp == null ? 999 : resp.getResult();
        final boolean match = result != 0 && (result < TWO_HUNDRED || result >= THREE_HUNDRED);
        if (match) {
            if (retry > 1) {
                log.error("[{}] fail resp:{}", busi, resp);
            } else {
                return readWithRetry(req, retry + 1, client, busi);
            }
        }
        return resp;
    }

    /**
     * 请更换使用culClient.queryUserChannel
     *
     * @param uid
     * @return
     */
    @Cached(timeToLiveMillis = 5000)
    public UserCurrentChannel getUserCurrentChannel(long uid) {
        try {
            ChannelInfoVo channelInfoVo = culClient.queryUserChannel(uid);
            if (channelInfoVo != null) {
                UserCurrentChannel result = new UserCurrentChannel();
                result.setIntime(channelInfoVo.getTimestamp());
                result.setSubsid(channelInfoVo.getSsid());
                result.setTopsid(channelInfoVo.getSid());

                return result;
            }
        } catch (Exception e) {
            log.warn("[getUserCurrentChannel] err:{}", e.getMessage(), e);
        }
        return null;
    }

    public UserCurrentChannel getNoCacheUserCurrentChannel(long uid) {
        try {
            ChannelInfoVo channelInfoVo = culClient.queryUserChannel(uid);
            if (channelInfoVo != null) {
                UserCurrentChannel result = new UserCurrentChannel();
                result.setIntime(channelInfoVo.getTimestamp());
                result.setSubsid(channelInfoVo.getSsid());
                result.setTopsid(channelInfoVo.getSid());

                return result;
            }
        } catch (Exception e) {
            log.warn("[getUserCurrentChannel] err:{}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 失败多次重试，防止网络抖动等的影响
     */
    public UserCurrentChannel getNoCacheUserCurrentChannel(long uid, long tryTimes) {
        for (int i = 0; i < tryTimes; i++) {
            UserCurrentChannel ucc = getNoCacheUserCurrentChannel(uid);
            if (ucc != null) {
                return ucc;
            }
            if (i < tryTimes - 1) {
                SysEvHelper.waiting(500);
            }
        }
        return null;
    }

    @Cached(timeToLiveMillis = 30000)
    public List<Long> getMicOrderList(long sid, long ssid) {
        return getMicOrderListInfo(sid, ssid);
    }

    public List<Long> getMicOrderListInfo(long sid, long ssid) {
        return culClient.queryOnMicList(sid, ssid);
    }

    public List<Long> getMicOrderListInfoNew(long sid, long ssid) {
        try {
            return culClient.queryOnMicList(sid, ssid);
        } catch (Exception e) {
            log.warn("getChannelMicOrderList err:{}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    public Map<Long, List<Long>> batchQueryOnMicListNew(long sid, Set<Long> subSids) {
        try {
            return culClient.batchQueryOnMicList(sid, subSids);
        } catch (Exception e) {
            log.warn("getChannelMicOrderList err:{}", e.getMessage(), e);
        }
        return Maps.newHashMap();
    }

    @Cached(timeToLiveMillis = 60000)
    public String getNickName(long uid, boolean base64) {
        WebdbUserInfo userInfo = webdbUinfoClient.getUserInfo(uid);
        String nick = userInfo == null || StringUtil.isBlank(userInfo.getNick()) ? "神秘用户" : userInfo.getNick();
        return base64 ? Base64Utils.encodeToString(nick.getBytes()) : nick;
    }

    @Cached(timeToLiveMillis = 60000)
    public UserBaseInfo getUserInfo(long uid, boolean isBase64) {
        UserBaseInfo info = new UserBaseInfo();
        info.setUid(uid);

        WebdbUserInfo userInfo = webdbUinfoClient.getUserInfo(uid);
        if (userInfo != null) {
            info.setLogo(userInfo.getAvatar());
            String nick = userInfo.getNick();
            info.setNick(isBase64 ? Base64.encodeBase64String(nick.getBytes()) : nick);
            info.setYyno(Convert.toLong(userInfo.getYyno()));
            final String avatar = WebdbUtils.getLogo(userInfo), hdAvatar = userInfo.getLogohd();
            info.setHdLogo(StringUtil.isBlank(hdAvatar) ? avatar : hdAvatar);
        } else {
            info.setLogo("https://s1.yy.com/guild/header/10001.jpg");
            String nick = "神秘用户";
            info.setNick(isBase64 ? Base64.encodeBase64String(nick.getBytes()) : nick);
        }


        return info;
    }

    @Cached(timeToLiveMillis = 60000)
    public Long getUidByYYNum(long yynum) {
        return webdbUinfoClient.getUidByYYNum(String.valueOf(yynum));

    }


    /**
     * 批量获取用户信息
     * com.yy.gameecology.common.cache.interceptor.MethodCacheInterceptor#getCacheKey(java.lang.String, java.lang.String, java.lang.Object[])
     * 里面已经实现了对 Collection 和 Map 的缓存key处理，可以工作了(需要自己了解是否合适用) - added by guolipng / 2022-04-29
     */
    public Map<Long, UserBaseInfo> batchGetUserInfos(List<Long> uids, boolean isBase64) {
        Map<Long, UserBaseInfo> result = Maps.newHashMap();
        Map<Long, WebdbUserInfo> userInfos = webdbUinfoClient.batchGetUserInfo(uids);
        for (Long uid : userInfos.keySet()) {
            WebdbUserInfo userInfo = userInfos.get(uid);
            UserBaseInfo info = new UserBaseInfo();
            info.setUid(uid);
            if (userInfo != null) {
                info.setLogo(userInfo.getAvatar());
                String nick = userInfo.getNick();
                info.setNick(isBase64 ? Base64.encodeBase64String(nick.getBytes()) : nick);
                info.setYyno(Convert.toLong(userInfo.getYyno()));
                if (StringUtil.isNotBlank(userInfo.getLogohd())) {
                    info.setHdLogo(userInfo.getLogohd());
                } else {
                    info.setHdLogo(userInfo.getAvatar());
                }
            } else {
                info.setLogo("https://s1.yy.com/guild/header/10001.jpg");
                String nick = "神秘用户";
                info.setNick(isBase64 ? Base64.encodeBase64String(nick.getBytes()) : nick);
            }
            result.put(uid, info);
        }
        return result;
    }

    @Cached(timeToLiveMillis = 300000)
    public long getAsid(long sid) {
        WebdbChannelInfo chanelInfo = webdbThriftClientForSa.getChannelInfo(sid);
        return chanelInfo == null ? sid : Convert.toLong(chanelInfo.getAsid(), sid);
    }

    @Cached(timeToLiveMillis = 300000)
    public long getSid(long asid) {
        WebdbChannelInfo chanelInfo = webdbSinfoClient.getChannelInfoByAsId(asid);
        return chanelInfo == null ? asid : Convert.toLong(chanelInfo.getSid(), asid);
    }

    @Cached(timeToLiveMillis = 300000)
    public ChannelBaseInfo getChannelInfo(long sid, boolean base64) {
        WebdbChannelInfo channelInfo = webdbThriftClient.getChannelInfo(sid);
        ChannelBaseInfo channelBaseInfo = new ChannelBaseInfo();
        channelBaseInfo.setSid(sid);
        long asid = sid, owUid = 0;
        String name = "神秘公会", logo = org.apache.commons.lang3.StringUtils.EMPTY;
        if (channelInfo != null) {
            asid = Convert.toLong(channelInfo.getAsid(), sid);
            owUid = Convert.toLong(channelInfo.getOwnerid());
            name = channelInfo.getName();
            logo = WebdbUtils.getLogo(channelInfo);
        }
        channelBaseInfo.setAsid(asid);
        channelBaseInfo.setOwUid(owUid);
        channelBaseInfo.setName(base64 ? Base64Utils.encodeToString(name.getBytes()) : name);
        channelBaseInfo.setLogo(logo);
        return channelBaseInfo;
    }

    public Map<Long, ChannelBaseInfo> getChannelInfos(List<Long> sids, boolean base64) {
        Map<Long, WebdbChannelInfo> channelInfos = webdbSinfoClient.batchGetTopChannelInfo(sids);
        if (MapUtils.isEmpty(channelInfos)) {
            return Collections.emptyMap();
        }

        Map<Long, ChannelBaseInfo> result = new HashMap<>(channelInfos.size());
        for (Map.Entry<Long, WebdbChannelInfo> entry : channelInfos.entrySet()) {
            long sid = entry.getKey();
            WebdbChannelInfo channelInfo = entry.getValue();
            ChannelBaseInfo channelBaseInfo = new ChannelBaseInfo();
            channelBaseInfo.setSid(sid);
            channelBaseInfo.setAsid(Convert.toLong(channelInfo.getAsid(), sid));
            channelBaseInfo.setOwUid(Convert.toLong(channelInfo.getOwnerid()));
            String name = Convert.toString(channelInfo.getName(), "神秘公会");
            channelBaseInfo.setName(base64 ? Base64Utils.encodeToString(name.getBytes()) : name);
            channelBaseInfo.setLogo(WebdbUtils.getLogo(channelInfo));

            result.put(sid, channelBaseInfo);
        }

        return result;
    }

    //@Cached(timeToLiveMillis = 300000)
    public RoomInfo getRoomInfoByRoomId(int roomId) {
        return zhuiwanRoomInfoClient.roomInfoByRoomId(roomId);
    }

    public RoomInfo getRoomInfoBySsid(long ssid) {
        return zhuiwanRoomInfoClient.roomInfoBySsid(ssid);
    }

    public List<RoomInfo> batchGetRoomInfoBySsids(List<Long> ssids) {
        return zhuiwanRoomInfoClient.listRoomInfoBySsid(ssids);
    }

    //@Cached(timeToLiveMillis = 300000)
    public FamilyBasicInfo getFamilyBasicInfo(long familyId) {
        return zhuiWanPrizeIssueServiceClient.getFamilyBasicInfo(familyId);
    }

    private JSONObject fillChannelInfo(String infoStr, long sid) {
        JSONObject map = JSON.parseObject(infoStr);
        if (map == null) {
            map = new JSONObject();
        }
        if (StringUtils.isEmpty(map.get(Const.WEBDB_CHANNEL_COLUMN_NAME.ASID))) {
            map.put(Const.WEBDB_CHANNEL_COLUMN_NAME.ASID, sid);
        }
        if (StringUtils.isEmpty(map.get(Const.WEBDB_CHANNEL_COLUMN_NAME.NAME))) {
            map.put(Const.WEBDB_CHANNEL_COLUMN_NAME.NAME, "神秘公会");
        }
        return map;
    }

    private JSONObject fillUserInfo(String value) {
        JSONObject map = JSON.parseObject(value);
        if (map == null) {
            map = new JSONObject();
        }
        if (StringUtils.isEmpty(map.get(NICK))) {
            map.put(NICK, "某大官人");
        }
        if (StringUtils.isEmpty(map.get(YYLOGO))) {
            map.put(YYLOGO, "https://s1.yy.com/guild/header/10001.jpg");
        }
        String logo = map.getString(YYLOGO);
        final String http = "http";
        if (!logo.startsWith(http)) {
            if (String.valueOf(NumberUtils.INTEGER_MINUS_ONE).equals(logo)) {
                map.put(YYLOGO, "https://s1.yy.com/guild/header/10001.jpg");
            } else {
                map.put(YYLOGO, "https://s1.yy.com/guild/header/" + logo + ".jpg");
            }
        }
        return map;
    }

    /**
     * 批量获取用户详细信息, map 的 key是 uid， value是对应用户的详细信息
     *
     * <AUTHOR>
     * @date 2018年9月4日 下午5:33:21
     */
    public Map<Long, WebdbUserInfo> batchYyUserInfo(List<Long> uids) {
        try {
            return webdbThriftClient.batchGetUserInfo(uids);
        } catch (Exception e) {
            log.error("batchYyUserInfo exception@" + JsonUtil.toJson(uids) + " -> " + e, e);
            return Maps.newHashMap();
        }
    }

    /**
     * 通过udb票据验证，获得yyuid, 验证成功返回正确的uid，验证失败返回 0 值
     *
     * @throws UnsupportedEncodingException
     * <AUTHOR>
     * @date 2018年3月29日 下午2:56:06
     */
    public long getYyuidByTicket(String ticket, String appid) throws TException {
        if (SysEvHelper.isLocal()) {
            // 先屏蔽 udb 票据验证，记得上线时去掉 - 方便本地调测
            if (StringUtil.isPureNumber(ticket)) {
                return Convert.toLong(ticket);
            }
        }
        //
        // try {
        // ticket = URLDecoder.decode(ticket, "UTF-8");
        // } catch (UnsupportedEncodingException e) {
        // // TODO Auto-generated catch block
        // throw new ActivityException(Code.E_PARAM_ILLEGAL);
        // }

        // 非法ticket不需要验证
        if (StringUtil.isPureNumber(ticket)) {
            throw new SuperException("用户验证失败！", 4008);
        }

        return secuserinfoThriftClient.getYyuidByTicket(ticket, appid);
    }

    public List<ChannelInfo> queryOnlineChannel(Template template) {
        return onlineChannelService.queryOnlineChannelNoCache(template);
    }

    public List<ChannelInfo> queryBroChannels(long actId, Template template) {
        List<ChannelInfo> onlineChannels = queryOnlineChannel(template);
        List<ChannelInfo> customChannels = whiteListService.getAllCustomerChannel(actId);
        //去重
        Set<String> onlineCh = onlineChannels.stream()
                .map(ch -> ch.getSid() + StringUtil.UNDERSCORE + ch.getSsid())
                .collect(Collectors.toSet());

        customChannels = customChannels.stream()
                .filter(ch -> !onlineCh.contains(ch.getSid() + StringUtil.UNDERSCORE + ch.getSsid()))
                .collect(Collectors.toList());

        onlineChannels.addAll(customChannels);
        return onlineChannels;
    }


    public Date getEventDate(SendGiftEvent event, long hdztId) {
        return !SysEvHelper.isDeploy() || isGrey(hdztId) ? getNow(hdztId)
                : event.getEventTime();
    }

    /**
     * 获取技能卡的白名单频道
     **/
    public List<ChannelInfo> getSkillCardAllChannel() {
        return skillCardChannelList;
    }

    /**
     * 是否是技能卡频道
     *
     * @param sid 公会id
     **/
    @Deprecated
    public boolean isSkillCardChannel(long sid) {
        long count = skillCardChannelList.stream().filter(channel -> channel.getSid() == sid).count();

        return count > 0;
    }

    public boolean isSkillCardChannel(long sid, long ssid) {
        long count = skillCardChannelList.stream().filter(channel -> channel.getSid() == sid && channel.getSsid() == ssid).count();
        return count > 0;
    }

    @PostConstruct
    @NeedRecycle(author = "wangdonghong", notRecycle = true)
    public void init() {
        reloadSkillCardChannel();
    }


    @Scheduled(fixedRate = 10_000)
    @ScheduledExt(historyRun = true)
    @NeedRecycle(author = "wangdonghong", notRecycle = true)
    public synchronized void reloadSkillCardChannel() {
        this.skillCardChannelList = queryOnlineChannel(Template.SkillCard);
    }

    private List<ChannelInfo> listChannel(int offset, int maxSize) {
        List<ChannelInfo> channelInfoList = zhuiwanChannelClient.batchGetOnlineChannels(offset, maxSize);
        log.info("batchGetOnlineChannels offset:{}, size:{}, resultSize:{}", offset, maxSize, channelInfoList.size());
        return channelInfoList;
    }

    /**
     * 如流通知的技术负责人
     */
    public List<String> getActRuliuNotifyUids(long actId){
        String technical = hdztRankingThriftClient.queryActTechnical(actId);
        return StringUtil.isBlank(technical) ? Lists.newArrayList() : Collections.singletonList(technical);
    }

    /**
     * 解析模板
     * @param templateContent 模板内容，占位符格式 ${teamMinMember}
     * @param para 替换参数，key就是占位符参数名，例如 teamMinMember
     * @return 输出模板
     */
    public String renderTemplate(String templateContent, Map<String, Object> para) {
        TemplateEngine engine = TemplateUtil.createEngine(new TemplateConfig());
        cn.hutool.extra.template.Template template = engine.getTemplate(templateContent);
        return template.render(para);
    }

    public String buildActRuliuMsg(long actId, boolean error, String title, String msg) {
        ActivityInfoVo hdztActivity = hdztRankingThriftClient.queryActivityInfo(actId);
        if(hdztActivity == null){
            hdztActivity = new ActivityInfoVo();
            hdztActivity.setActName("暂无活动信息");
        }

        return "### <font color=\"" + (error ? "red" : "green") + "\">【中控->" + title + (error ? "错误告警" : "通知") + "】</font>\n" + "#### [" + actId + "]" + hdztActivity.getActName() + "\n" +
                "\n" + msg + "\n";
    }

    /**
     * 活动是否已结束
     **/
    public boolean isActEnd(long actId) {
        ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityInfo(actId);
        if (activityInfoVo == null) {
            return true;
        }

        return activityInfoVo.getEndTime() < activityInfoVo.getCurrentTime();
    }

    /**
     * 活动是否已开始
     **/
    public boolean isActStart(long actId) {
        ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityInfo(actId);
        if (activityInfoVo == null) {
            return false;
        }

        return activityInfoVo.getBeginTime() <= activityInfoVo.getCurrentTime() && activityInfoVo.getEndTime() >= activityInfoVo.getCurrentTime();
    }

    public int getTurnoverAppId(int busiId) {
        BusiId value = BusiId.findByValue(busiId);
        int appId = 0;
        switch (value) {
            case GAME_BABY:
                appId = 36;
                break;
            case MAKE_FRIEND:
                appId = 2;
                break;
            case SKILL_CARD:
                appId = 34;
                break;
            default:break;
        }
        return appId;
    }


    public UserBaseInfo userInfoByYyno(Long yyno,Long loginUid) {
        UserBaseInfo userBaseInfo = null;
        if(yyno==null || yyno<=0){
            userBaseInfo = this.getUserInfo(loginUid, false);
        }else{
            Long uid = this.getUidByYYNum(yyno);
            if(uid!=null && uid>0){
                userBaseInfo = this.getUserInfo(uid, false);
            }
        }
        return userBaseInfo;

    }

    public boolean isDanmuChannel(long sid,long ssid){
        List<ChannelInfo> danmakuChannel = danmakuActivityClient.queryAllChannelInfo();
        log.info("isDanmuChannel {} {} {}", sid,ssid, JsonUtil.toJson(danmakuChannel));
        if (danmakuChannel == null || danmakuChannel.size() ==  0) {
            return false;
        }
        boolean exists = danmakuChannel.stream()
                .anyMatch(obj -> obj.getSid() == sid && obj.getSsid() == ssid);
        return exists ;
    }

    public boolean inDanmuChannel(long uid) {
        UserCurrentChannel userCurrentChannel = getNoCacheUserCurrentChannel(uid);
        if (userCurrentChannel == null) {
            return false;
        }
        return isDanmuChannel(userCurrentChannel.getTopsid(), userCurrentChannel.getSubsid());
    }

    /**
     * 关闭频道维度的挂件广播
     * 用于每个人显示都不一样的挂件场景，避免显示数据覆盖
     */
    public boolean isCloseChannelLayerBro(long actId) {
        return Const.ONESTR.equals(cacheService.getActAttrValue(actId, GeActAttrConst.CLOSE_LAYER_CHANNEL_BRO));
    }
}
