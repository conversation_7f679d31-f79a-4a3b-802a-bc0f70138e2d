package com.yy.gameecology.activity.bean.rank;

import com.yy.gameecology.common.utils.Convert;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/10/19 15:16
 **/
@Data
public class RoomRankItem extends PkRankItemBase {
    private Long roomId;
    private Long value;
    private String name;
    private String avatarInfo;

    private long sid;

    private long ssid;

    @Override
    public void setKey(String key) {
        this.roomId = Convert.toLong(key);
    }

    @Override
    public String getKey() {
        return this.roomId + "";
    }

    @Override
    public void setValue(Long value) {
        this.value = value;
    }
}
