package com.yy.gameecology.activity.worker.subscriber;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.bean.mq.AnchorShowChangeJyEvent;
import com.yy.gameecology.activity.service.layer.BroActLayerService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.MqConst;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2023/7/7 16:56
 **/
@ConditionalOnExpression(Const.EXPRESSION_NOT_HISTORY)
@Component
@Slf4j
public class JiaoyouAnchorShowChangeKafkaConsumer {
    @Autowired
    private BroActLayerService broActLayerService;

    @KafkaListener(containerFactory = "hdptWxKafkaContainerFactory", id = "jy_wx_kafka_anchorShowChange",
            topics = MqConst.JY_ANCHOR_SHOW_CHANGE_TOPIC,
            groupId = "${kafka.hdpt.wx.group}")
    public void onAnchorShowChangeFromWx(ConsumerRecord<String, String> consumerRecord) {
        onAnchorShowChangeMessage("wx", consumerRecord.value());
    }

    @KafkaListener(containerFactory = "hdptWxXdcKafkaContainerFactory", id = "jy_sz_kafka_anchorShowChange",
            topics = MqConst.JY_ANCHOR_SHOW_CHANGE_TOPIC,
            groupId = "${kafka.hdpt.wx.group}")
    public void onAnchorShowChangeFromSz(ConsumerRecord<String, String> consumerRecord) {
        onAnchorShowChangeMessage("xdc", consumerRecord.value());
    }

    private void onAnchorShowChangeMessage(String from, String payload) {
        log.info("onAnchorShowChangeMessage,from={}, msg:{}", from, payload);
        try {
            AnchorShowChangeJyEvent event = JSON.parseObject(payload, AnchorShowChangeJyEvent.class);

            // 更新浮层挂件
            Const.EXECUTOR_DELAY_GENERAL.schedule(() -> broActLayerService.invokeAllEffectActRefresh(event.getSid(), event.getSsid()), 8, TimeUnit.SECONDS);
        } catch (Exception ex) {
            log.error("onAnchorShowChangeMessage error,from={},message={}", from, payload, ex);
        }
    }
}
