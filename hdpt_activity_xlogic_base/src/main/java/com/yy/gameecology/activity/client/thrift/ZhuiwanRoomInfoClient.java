package com.yy.gameecology.activity.client.thrift;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.client.yrpc.RoomNoYrpcClient;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.consts.CacheTimeout;
import com.yy.protocol.pb.zhuiwan.roomno.RoomNo;
import com.yy.thrift.zhuiwan_room.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023.02.15 15:46
 */
@Component
public class ZhuiwanRoomInfoClient implements InitializingBean {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

//    @Reference(protocol = "nthrift_selector_compact", owner = "${thrift_skillcard_client_s2s_name}", registry = "consumer-reg", timeout = 30000, parameters = {"threads", "10"})
//    private RoomNoService.Iface proxy = null;

    @Autowired
    private RoomNoYrpcClient roomNoYrpcClient;


//    public RoomNoService.Iface getReadProxy() {
//        return proxy;
//    }

    @Override
    public void afterPropertiesSet() throws Exception {
//        var ret = roomInfoBySsid(2793959425L);
//        System.out.println(JSON.toJSONString(ret));
    }


    private List<RoomInfo> listRoomInfos(ListRoomInfoReq req) {
        try {
            //listRoomInfoResp = getReadProxy().listRoomInfo(req);
            RoomNo.ListRoomInfoReq.Builder reqBuilder = RoomNo.ListRoomInfoReq.newBuilder();
            if (CollectionUtils.isNotEmpty(req.getRoomIds())) {
                reqBuilder.addAllRoomIds(req.getRoomIds());
            }
            if (CollectionUtils.isNotEmpty(req.getSsids())) {
                reqBuilder.addAllSsids(req.getSsids());
            }
            RoomNo.ListRoomInfoResp roomInfoResp = roomNoYrpcClient.getReadProxy().queryRoomNo(reqBuilder.build());
            if (roomInfoResp != null && CollectionUtils.isNotEmpty(roomInfoResp.getRoomInfosList())) {
                return roomInfoResp.getRoomInfosList().stream().map(p -> {
                    RoomInfo roomInfo = new RoomInfo();
                    roomInfo.setRoomId(p.getRoomId());
                    roomInfo.setTitle(p.getTitle());
                    roomInfo.setCover(p.getCover());
                    roomInfo.setOw(p.getOw());
                    roomInfo.setSid(p.getSid());
                    roomInfo.setSsid(p.getSsid());
                    roomInfo.setFamilyId(p.getFamilyId());
                    roomInfo.setFamilyName(p.getFamilyName());
                    return roomInfo;
                }).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("listRoomInfo error, req:{}", req.toString(), e);
            throw e;
        }

        return Lists.newArrayList();
    }

    public RoomInfo roomInfoByRoomId(Integer roomId) {
        List<Integer> roomIds = Lists.newArrayList(roomId);
        List<RoomInfo> roomInfos = listRoomInfoByRoomId(roomIds);
        if (!CollectionUtils.isEmpty(roomInfos) && roomInfos.size() == 1) {
            return roomInfos.get(0);
        }
        return new RoomInfo();
    }

    @Cached(timeToLiveMillis = CacheTimeout.PW_PLUGIN_CHANNEL)
    public RoomInfo roomInfoBySsid(Long ssid) {
        List<Long> ssids = Lists.newArrayList(ssid);
        List<RoomInfo> roomInfos = listRoomInfoBySsid(ssids);
        if (!CollectionUtils.isEmpty(roomInfos) && roomInfos.size() == 1) {
            return roomInfos.get(0);
        }
        return new RoomInfo();
    }

    public List<RoomInfo> listRoomInfoBySsid(List<Long> ssid) {
        ListRoomInfoReq req = new ListRoomInfoReq();
        req.setSsids(ssid);
        return listRoomInfos(req);

    }

    public List<RoomInfo> listRoomInfoByRoomId(List<Integer> roomIds) {
        ListRoomInfoReq req = new ListRoomInfoReq();
        req.setRoomIds(roomIds);
        return listRoomInfos(req);
    }

    public Map<Integer, RoomInfo> roomInfoMapByRoomId(List<Integer> roomIds) {
        List<RoomInfo> roomInfos = listRoomInfoByRoomId(roomIds);
        if (roomInfos == null) {
            return Maps.newHashMap();
        }
        return roomInfos.stream().collect(Collectors.toMap(RoomInfo::getRoomId, Function.identity()));
    }

    public List<ChannelInfo> listFamilyValidRoom(long familyId) {
        List<ChannelInfo> channelInfos = Lists.newArrayList();

        RoomNo.ListFamilyValidRoomReq req = RoomNo.ListFamilyValidRoomReq.newBuilder().setFamilyId(familyId).build();

        try {
//            ListFamilyValidRoomResp roomResp = getReadProxy().listFamilyValidRoom(req);
//            if (roomResp != null && roomResp.getRooms() != null) {
//                channelInfos = roomResp.getRooms().stream().map(p -> {
//                    ChannelInfo channelInfo = new ChannelInfo();
//                    channelInfo.setSid(p.getBindSid());
//                    channelInfo.setSsid(p.getBindSsid());
//                    return channelInfo;
//                }).collect(Collectors.toList());
//            }

            var rsp = roomNoYrpcClient.getReadProxy().listFamilyValidRoom(req);
            if (rsp != null) {
                channelInfos = rsp.getRoomInfoList().stream().map(p -> {
                    ChannelInfo channelInfo = new ChannelInfo();
                    channelInfo.setSid(p.getSid());
                    channelInfo.setSsid(p.getSsid());
                    return channelInfo;
                }).collect(Collectors.toList());
            }

        } catch (Exception e) {
            log.error("listFamilyValidRoom error,familyId:{},e:{}", familyId, e.getMessage(), e);
        }

        return channelInfos;
    }
}
