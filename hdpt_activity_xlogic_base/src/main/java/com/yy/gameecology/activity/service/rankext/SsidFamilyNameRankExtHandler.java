package com.yy.gameecology.activity.service.rankext;

import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.rank.SubGuildRankItem;
import com.yy.gameecology.activity.client.thrift.FtsRecommendDataThriftClient;
import com.yy.gameecology.activity.client.thrift.ZhuiWanPrizeIssueServiceClient;
import com.yy.gameecology.activity.client.thrift.ZhuiwanRoomInfoClient;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RankingInfo;
import com.yy.thrift.zhuiwan_newfamily.FamilyBasicInfo;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 语音房添加厅对应的家族信息
 *
 * <AUTHOR>
 * @since 2022/10/19 10:50
 **/
@Component
public class SsidFamilyNameRankExtHandler implements RankExtHandler {
    private static Logger logger = LoggerFactory.getLogger(SsidFamilyNameRankExtHandler.class);

    @Autowired
    private ZhuiwanRoomInfoClient zhuiwanRoomInfoClient;

    @Autowired
    private FtsRecommendDataThriftClient ftsRecommendDataThriftClient;
    @Autowired
    private ZhuiWanPrizeIssueServiceClient zhuiWanPrizeIssueServiceClient;
    public static final String DEAFULT_FAMILY_ICON = "https://gamebaby.bs2.yy.com/channel.png";


    @Override
    public List<String> supportKeys() {
        return Arrays.asList(
                "2022115001_1_1",
                "2022115001_1_2",
                "2022115001_1_3",
                "2022115001_1_4",
                "2022115001_1_5",
                "2022115001_101_2"
        );
    }

    @Override
    public List<Object> addViewExt(GetRankReq rankReq, RankingInfo rankingInfo, List<Rank> ranks, List<Object> objectList) {
        Clock clock = new Clock();
        try {
            Map<String, Long> rankMemberToSsidMap = ranks.stream().collect(
                    Collectors.toMap(
                            Rank::getMember,
                            rank -> {
                                String rankMember = rank.getMember();
                                // 厅的榜单成员格式：{sid}_{ssid}
                                return Convert.toLong(rankMember.split("_")[1], 0);
                            }
                    )
            );
            List<String> memberIds = ranks.stream().map(Rank::getMember).collect(Collectors.toList());
            Map<String, String> pictureMap = ftsRecommendDataThriftClient.batchGetRecommendConfigPicture(memberIds, 501);

            List<Long> ssidList = new ArrayList<>(rankMemberToSsidMap.values());
            Map<Long, Long> ssidToFamilyIdMap = Maps.newHashMap();
            List<Long> familyIds = new ArrayList<>();
            for (Long ssid : ssidList) {
                // TODO 这里营收没提供批量查询,暂时使用本地缓存,本地缓存5分钟
                RoomInfo roomInfo = zhuiwanRoomInfoClient.roomInfoBySsid(ssid);
                if (roomInfo != null) {
                    ssidToFamilyIdMap.put(ssid, roomInfo.getFamilyId());
                    familyIds.add(roomInfo.getFamilyId());
                }
            }
            Map<Long, FamilyBasicInfo> familyBasicInfoMap = zhuiWanPrizeIssueServiceClient.batchGetFamilyBasicInfo(familyIds);

            objectList.stream().map(rank -> ((SubGuildRankItem) rank)).forEach(
                    rank -> {
                        if (rank.getViewExt() == null) {
                            rank.setViewExt(Maps.newHashMap());
                        }

                        String picture = pictureMap.getOrDefault(rank.getKey(), "");

                        String memberId = rank.getKey();
                        long ssid = rankMemberToSsidMap.getOrDefault(memberId, 0L);
                        long familyId = ssidToFamilyIdMap.getOrDefault(ssid, 0L);
                        FamilyBasicInfo family = familyBasicInfoMap.get(familyId);
                        if (family == null) {
                            rank.getViewExt().put("familyName", "");
                            rank.getViewExt().put("familyId", "0");
                        } else {
                            rank.getViewExt().put("familyName", family.getFamilyName());
                            rank.getViewExt().put("familyId", family.getFamilyId() + "");
                            if (StringUtils.isEmpty(picture)) {
                                picture = family.getCover();
                            }
                        }

                        if (StringUtils.isEmpty(picture)) {
                            picture = DEAFULT_FAMILY_ICON;
                        }
                        rank.setAvatarInfo(picture);
                    });

            logger.info("addViewExt done clock={},rankReq={}", clock.tag(), rankReq);
        } catch (Exception ex) {
            logger.error("addViewExt error,rankReq={},clock={}", rankReq, clock.tag(), ex);
        }

        return objectList;
    }
}
