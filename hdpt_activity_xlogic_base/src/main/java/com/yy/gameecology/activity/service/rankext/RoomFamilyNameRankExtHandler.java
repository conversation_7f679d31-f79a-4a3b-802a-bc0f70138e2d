package com.yy.gameecology.activity.service.rankext;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.rank.RoomRankItem;
import com.yy.gameecology.activity.client.thrift.ZhuiWanPrizeIssueServiceClient;
import com.yy.gameecology.activity.client.thrift.ZhuiwanRoomInfoClient;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.hdzj.element.component.RankBuilderComponent;
import com.yy.gameecology.hdzj.element.component.attr.RankBuilderComponentAttr;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RankingInfo;
import com.yy.thrift.zhuiwan_newfamily.FamilyBasicInfo;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* @author: yulianzhu
* @date: 2023.03.22
* @description: 房间榜单添加家族昵称
*/
@Component
public class RoomFamilyNameRankExtHandler implements RankExtHandler {
    private static Logger logger = LoggerFactory.getLogger(RoomFamilyNameRankExtHandler.class);

    @Autowired
    private ZhuiwanRoomInfoClient zhuiwanRoomInfoClient;

    @Autowired
    private RankBuilderComponent rankBuilderComponent;

    @Autowired
    private ZhuiWanPrizeIssueServiceClient zhuiWanPrizeIssueServiceClient;

    public static final String ROOM_FAMILY_NAME = "familyName";


    @Override
    public List<String> supportKeys() {
        return null;
    }

    @Override
    public List<Object> addViewExt(GetRankReq rankReq, RankingInfo rankingInfo, List<Rank> ranks, List<Object> objectList) {
        Clock clock = new Clock();
        if (CollectionUtils.isEmpty(ranks)) {
            return objectList;
        }

        final long actId = rankReq.getActId();
        RankBuilderComponentAttr attr = rankBuilderComponent.tryGetUniqueComponentAttr(actId);
        if(attr == null){
            return objectList;
        }

        if (CollectionUtils.isEmpty(attr.getFamilyNameRanks())) {
            return objectList;
        }

        boolean matched = attr.getFamilyNameRanks().stream().anyMatch(pair -> pair.getRankId() == rankReq.getRankId() && pair.getPhaseId() == rankReq.getPhaseId());
        if (!matched) {
            return objectList;
        }

        try {
            List<Integer> memberIds = ranks.stream().map(Rank::getMember).map(Convert::toInt).collect(Collectors.toList());
            List<RoomInfo> roomInfos = zhuiwanRoomInfoClient.listRoomInfoByRoomId(memberIds);
            List<Long> familyIds = roomInfos.stream().map(RoomInfo::getFamilyId).collect(Collectors.toList());
            Map<Integer, Long> roomId2FamilyId = roomInfos.stream().collect(Collectors.toMap(RoomInfo::getRoomId, RoomInfo::getFamilyId));

            Map<Long, FamilyBasicInfo> familyBasicInfoMap = zhuiWanPrizeIssueServiceClient.batchGetFamilyBasicInfo(familyIds);
            //TODO 只有房间的榜单才能传familyName 这个参数，否则下面类型转换会报错！！！！ 这里要优化下？
            objectList.stream().map(rank -> ((RoomRankItem) rank)).forEach(
                    rank -> {
                        if (rank.getViewExt() == null) {
                            rank.setViewExt(Maps.newHashMap());
                        }

                        Long familyId = roomId2FamilyId.get(Convert.toInt(rank.getKey()));

                        FamilyBasicInfo family = familyBasicInfoMap.get(familyId);
                        if (family == null) {
                            rank.getViewExt().put("familyName", "");
                            rank.getViewExt().put("familyId", "0");
                        } else {
                            rank.getViewExt().put("familyName", family.getFamilyName());
                            rank.getViewExt().put("familyId", family.getFamilyId() + "");
                        }
                    });
            logger.info("addViewExt done clock={},rankReq={}", clock.tag(), rankReq);
        } catch (Exception ex) {
            logger.error("addViewExt error,rankReq={},clock={},e:{}", JSON.toJSONString(rankReq), clock.tag(), ex.getCause(), ex);
        }
        return objectList;
    }
}
