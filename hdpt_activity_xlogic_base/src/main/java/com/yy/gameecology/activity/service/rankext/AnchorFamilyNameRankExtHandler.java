package com.yy.gameecology.activity.service.rankext;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.rank.BabyRankItem;
import com.yy.gameecology.activity.bean.rank.RoomRankItem;
import com.yy.gameecology.activity.bean.rank.UserRankItem;
import com.yy.gameecology.activity.client.thrift.TurnoverFamilyThriftClient;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RankingInfo;
import com.yy.thrift.turnover.TFamily;
import com.yy.thrift.zhuiwan_newfamily.FamilyBasicInfo;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class AnchorFamilyNameRankExtHandler implements RankExtHandler {

    @Autowired
    private TurnoverFamilyThriftClient turnoverFamilyThriftClient;

    private static Logger logger = LoggerFactory.getLogger(AnchorFamilyNameRankExtHandler.class);

    public static final String ANCHOR_FAMILY_NAME = "anchorFamilyName";
    @Override
    public List<String> supportKeys() {
        return null;
    }

    @Override
    public List<Object> addViewExt(GetRankReq rankReq, RankingInfo rankingInfo, List<Rank> ranks, List<Object> objectList) {
        Clock clock = new Clock();
        if (!rankReq.getExtQueryList().contains(ANCHOR_FAMILY_NAME)) {
            return objectList;
        }
        try {
            List<Long> memberIds = ranks.stream().map(Rank::getMember).map(Convert::toLong).collect(Collectors.toList());
            Map<Long, Long> uidFamilyId = turnoverFamilyThriftClient.batchQueryContractFamilyIds(memberIds);
            List<Long> familyIds = new ArrayList<>();
            for (Long k : uidFamilyId.keySet()) {
                familyIds.add(uidFamilyId.get(k));
            }
            Map<Long, TFamily> familyMap = turnoverFamilyThriftClient.batchGetFamily(familyIds);
            objectList.stream().map(rank -> ((BabyRankItem) rank)).forEach(
                    rank -> {
                        if (rank.getViewExt() == null) {
                            rank.setViewExt(Maps.newHashMap());
                        }

                        Long familyId = uidFamilyId.get(Convert.toLong(rank.getKey()));

                        TFamily family = familyMap.get(familyId);
                        if (family == null) {
                            rank.getViewExt().put("familyName", "");
                            rank.getViewExt().put("familyId", "0");
                        } else {
                            rank.getViewExt().put("familyName", family.getName());
                            rank.getViewExt().put("familyId", family.getId() + "");
                        }
                    });
            logger.info("addViewExt done clock={},rankReq={}", clock.tag(), rankReq);
        } catch (Exception ex) {
            logger.error("addViewExt error,rankReq={},clock={},e:{}", JSON.toJSONString(rankReq), clock.tag(), ex.getCause(), ex);
        }
        return objectList;

    }
}

