package com.yy.gameecology.activity.service.layer;

import com.yy.gameecology.activity.bean.actlayer.LayerBroadcastInfo;
import com.yy.gameecology.activity.bean.actlayer.LayerMemberItem;
import com.yy.gameecology.activity.client.thrift.FtsRecommendDataThriftClient;
import com.yy.gameecology.activity.client.thrift.ZhuiWanPrizeIssueServiceClient;
import com.yy.gameecology.activity.client.thrift.ZhuiwanRoomInfoClient;
import com.yy.gameecology.activity.service.rankext.SsidFamilyNameRankExtHandler;
import com.yy.gameecology.common.consts.LayerViewStatus;
import com.yy.gameecology.common.utils.Convert;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.zhuiwan_newfamily.FamilyBasicInfo;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import com.yy.thrift.zhuiwan_skillcard.ChannelSeatInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 技能卡业务的通用设置
 *
 * <AUTHOR>
 * @since 2022/10/18 16:37
 **/
@Service
@Slf4j
public class SkillCardLayerSupport implements LayerSupport {
    @Autowired
    private ZhuiWanPrizeIssueServiceClient zhuiWanPrizeIssueServiceClient;
    @Autowired
    private FtsRecommendDataThriftClient ftsRecommendDataThriftClient;

    @Autowired
    private ZhuiwanRoomInfoClient zhuiwanRoomInfoClient;

    @Override
    public long getActId() {
        return BusiId.SKILL_CARD.getValue();
    }

    @Override
    public LayerBroadcastInfo customBroadcastInTheEnd(LayerBroadcastInfo source) {
        if (source.getActBusiId() != (long) BusiId.SKILL_CARD.getValue()) {
            return source;
        }
        setAnchorSeatOrder(source);
        //前面已根据roomId设置了头像,这里不再需要setHallLogo(source);

        return source;
    }

    private void setAnchorSeatOrder(LayerBroadcastInfo source) {
        try {
            List<LayerMemberItem> anchors = source.getAnchorInfo();
            if (anchors == null) {
                return;
            }
            long sid = Convert.toLong(source.getExt().get("sid"), 0);
            long ssid = Convert.toLong(source.getExt().get("ssid"), 0);
            if (sid == 0 || ssid == 0) {
                return;
            }
            List<ChannelSeatInfo> channelSeatInfos = zhuiWanPrizeIssueServiceClient.queryChannelSeat(sid, ssid);
            if (CollectionUtils.isEmpty(channelSeatInfos)) {
                return;
            }
            Map<Long, ChannelSeatInfo> channelSeatInfoMap = channelSeatInfos.stream().collect(
                    Collectors.toMap(ChannelSeatInfo::getUid, seat -> seat));

            for (LayerMemberItem anchor : anchors) {
                long anchorId = Convert.toLong(anchor.getMemberId(), 0);
                ChannelSeatInfo seatInfo = channelSeatInfoMap.get(anchorId);
                if (seatInfo == null) {
                    continue;
                }
                anchor.setSort(seatInfo.getSeatOrder() - 1);
                anchor.getViewSupport().put("SeatOrder", seatInfo.getSeatOrder() - 1);
            }
        } catch (Exception ex) {
            log.error("setAnchorSeatOrder error", ex);
        }
    }

    private void setHallLogo(LayerBroadcastInfo source) {
        try {
            LayerMemberItem subChannelInfo = source.getSubChannelInfo();
            long sid = Convert.toLong(source.getExt().get("sid"), 0);
            long ssid = Convert.toLong(source.getExt().get("ssid"), 0);
            if (subChannelInfo == null || sid == 0 || ssid == 0) {
                return;
            }
            RoomInfo roomInfo = zhuiwanRoomInfoClient.roomInfoBySsid(ssid);
            if (roomInfo == null) {
                subChannelInfo.setViewStatus(LayerViewStatus.ELIMINATE_NOT_MISSION_110);
                return;
            }

            Map<String, String> pictureMap = ftsRecommendDataThriftClient.batchGetRecommendConfigPicture(Arrays.asList(subChannelInfo.getMemberId()), 501);
            String picture = pictureMap.get(subChannelInfo.getMemberId());
            if (StringUtils.isNotEmpty(picture)) {
                subChannelInfo.setLogo(picture);
                return;
            }

            long familyId = roomInfo.getFamilyId();
            FamilyBasicInfo familyBasicInfo = zhuiWanPrizeIssueServiceClient.getFamilyBasicInfo(familyId);
            if (familyBasicInfo != null) {
                picture = familyBasicInfo.getCover();
            }

            if (StringUtils.isEmpty(picture)) {
                picture = SsidFamilyNameRankExtHandler.DEAFULT_FAMILY_ICON;
            }
            subChannelInfo.setLogo(picture);
        } catch (Exception ex) {
            log.error("setAnchorSeatOrder error", ex);
        }
    }
}
