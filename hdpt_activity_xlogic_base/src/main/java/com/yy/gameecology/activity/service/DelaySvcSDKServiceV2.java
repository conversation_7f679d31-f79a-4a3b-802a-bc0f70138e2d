package com.yy.gameecology.activity.service;

import cn.hutool.core.lang.Assert;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.bean.svc.DownwardSvcMsg;
import com.yy.gameecology.activity.bean.svc.SvcType;
import com.yy.gameecology.activity.client.yrpc.DanmakuActivityClient;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.broadcast.Template;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DelaySvcSDKServiceV2 implements InitializingBean {

    private static final String DELAY_BROADCAST_PREFIX = "DELAY_SVC_MSG:%s";

    @Resource(name = "zpopByScoreScript")
    private DefaultRedisScript<List<GameecologyActivity.GameEcologyMsg>> zpopByScoreScript;

    @Autowired
    private ActRedisGroupDao actRedisDao;

    @Autowired
    private SvcSDKService svcSDKService;

    @Autowired
    private DanmakuActivityClient danmakuActivityClient;

    private StringRedisTemplate stringRedisTemplate;

    public static final RedisSerializer SVC_MSG_SERIALIZER = new RedisSerializer<DownwardSvcMsg>() {
        @Override
        public byte[] serialize(DownwardSvcMsg svcMsg) throws SerializationException {
            if (svcMsg == null) {
                return new byte[0];
            }

            try {
                return svcMsg.pack();
            } catch (IOException e) {
                throw new SerializationException("pack svc msg fail", e);
            }
        }

        @Override
        public DownwardSvcMsg deserialize(byte[] bytes) throws SerializationException {
            DownwardSvcMsg svcMsg = new DownwardSvcMsg();
            try {
                svcMsg.unpack(bytes);
            } catch (IOException e) {
                throw new SerializationException("unpack svc msg fail", e);
            }

            return svcMsg;
        }
    };

    @Override
    public void afterPropertiesSet() throws Exception {
        this.stringRedisTemplate = actRedisDao.getRedisTemplate(RedisConfigManager.DEFAULT_GROUP_CODE);
    }

    /**
     *
     * @param svcMsg msg to save
     * @param delay unit second
     */
    private void addToDelayQueue(DownwardSvcMsg svcMsg, long delay) {
        final byte[] data;
        try {
            data = svcMsg.pack();
        } catch (Exception e) {
            throw new RuntimeException("encode message fail:", e);
        }
        final byte[] keyBytes = String.format(DELAY_BROADCAST_PREFIX, SysEvHelper.getGroup()).getBytes(StandardCharsets.UTF_8);
        final long score = System.currentTimeMillis() + delay * 1000;
        stringRedisTemplate.execute((RedisCallback<Object>) connection -> connection.zAdd(keyBytes, score, data));
    }

    /**
     * 函数功能：单播
     * @param uid target uid
     * @param message message
     * @param delay unit second
     */
    public void unicastUid(long uid, GameecologyActivity.GameEcologyMsg message, long delay) {
        DownwardSvcMsg svcMsg = new DownwardSvcMsg();
        svcMsg.setSvcType(SvcType.unicast);
        svcMsg.setUid(uid);
        svcMsg.setMessage(message);
        addToDelayQueue(svcMsg, delay);
    }

    /**
     * 函数功能：子频道广播
     * @param sid target sid
     * @param ssid target ssid
     * @param message message
     * @param delay unit second
     */
    public void broadcastSub(long sid, long ssid, GameecologyActivity.GameEcologyMsg message, long delay) {
        DownwardSvcMsg svcMsg = new DownwardSvcMsg();
        svcMsg.setSvcType(SvcType.broadcast_sub);
        svcMsg.setSid(sid);
        svcMsg.setSsid(ssid);
        svcMsg.setMessage(message);
        addToDelayQueue(svcMsg, delay);
    }

    /**
     * 函数功能：顶级频道广播
     * @param sid target sid
     * @param message message
     * @param delay unit second
     */
    public void broadcastTop(long sid, GameecologyActivity.GameEcologyMsg message, long delay) {
        DownwardSvcMsg svcMsg = new DownwardSvcMsg();
        svcMsg.setSvcType(SvcType.broadcast_top);
        svcMsg.setSid(sid);
        svcMsg.setMessage(message);
        addToDelayQueue(svcMsg, delay);
    }

    /**
     * 函数功能：模板全服广播
     * @param template target template
     * @param message message
     * @param delay unit second
     */
    public void broadcastTemplate(Template template, GameecologyActivity.GameEcologyMsg message, long delay, int excludeDanmaku) {
        Assert.notNull(template);
        DownwardSvcMsg svcMsg = new DownwardSvcMsg();
        svcMsg.setSvcType(SvcType.broadcast_template);
        svcMsg.setTemplate(template);
        svcMsg.setMessage(message);
        svcMsg.setExcludeDanmaku(excludeDanmaku);
        addToDelayQueue(svcMsg, delay);
    }

    @Scheduled(cron = "1/2 * * * * ?")
    @NeedRecycle(author = "liqingyang", notRecycle = true)
    public void doBroadcastTemplate() {
        if (SysEvHelper.isLocal() || SysEvHelper.isHistory()) {
            log.info("local and history not run return");
            return;
        }
        String key = String.format(DELAY_BROADCAST_PREFIX, SysEvHelper.getGroup());
        String max = String.valueOf(System.currentTimeMillis());
        List<DownwardSvcMsg> msgs = stringRedisTemplate.execute(zpopByScoreScript, RedisSerializer.string(), SVC_MSG_SERIALIZER, List.of(key), "0", max, "5");
        if (CollectionUtils.isEmpty(msgs)) {
            return;
        }

        for (DownwardSvcMsg svcMsg : msgs) {
            if (!SysEvHelper.isDeploy()) {
                log.info("do downward svc msg:{}", svcMsg);
            }
            List<ChannelInfo> danmakuChannel = danmakuActivityClient.queryAllChannelInfo();
            Set<String> exclude = danmakuChannel.stream().map(channel -> channel.getSid() + StringUtil.UNDERSCORE + channel.getSsid()).collect(Collectors.toSet());
            switch (svcMsg.getSvcType()) {
                case unicast -> svcSDKService.unicastUid(svcMsg.getUid(), svcMsg.getMessage());
                case broadcast_top -> svcSDKService.broadcastTop(svcMsg.getSid(), svcMsg.getMessage());
                case broadcast_sub ->
                        svcSDKService.broadcastSub(svcMsg.getSid(), svcMsg.getSsid(), svcMsg.getMessage());
                case broadcast_template -> {
                    if (svcMsg.getExcludeDanmaku() == 0) {
                        svcSDKService.broadcastTemplate(svcMsg.getTemplate(), svcMsg.getMessage());
                    } else {
                        svcSDKService.broadcastTemplateExclude(svcMsg.getTemplate(), svcMsg.getMessage(), exclude);
                    }
                }
                default -> throw new IllegalArgumentException("svc type error");
            }
        }
    }

    /**
     * 函数功能：陪玩全频道广播
     */
    public void broadcastAllChanelsInPW(long actId, GameecologyActivity.GameEcologyMsg message, long delay) {
        throw new UnsupportedOperationException("暂不支持");
    }

    /**
     * 函数功能：技能卡全频道广播
     */
    public void broadcastAllChanelsInSkillCard(GameecologyActivity.GameEcologyMsg message, long delay) {
        throw new UnsupportedOperationException("暂不支持");
    }

    /**
     * 飞行任务广播
     *
     * @param template
     * @param message
     * @param sid
     * @param ssid
     */
    public void broadcastTemplate(Template template, GameecologyActivity.GameEcologyMsg message, long sid, long ssid, long delay) {
        throw new UnsupportedOperationException("暂不支持");
    }
}
