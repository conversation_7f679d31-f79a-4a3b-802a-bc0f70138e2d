package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSON;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.ScheduledExt;
import com.yy.gameecology.activity.bean.SettleViewConfig;
import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeActAttrConst;
import com.yy.gameecology.common.db.mapper.gameecology.GeActAttrMapper;
import com.yy.gameecology.common.db.model.gameecology.*;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2019/8/29
 */
@Component
@DependsOn("springBeanAwareFactory")
public class CacheService {

    private static final Logger log = LoggerFactory.getLogger(CacheService.class);

    private long count = 0;

    @Autowired
    private GeActAttrMapper geActAttrMapper;

    @Autowired
    private GameecologyDao gameecologyDao;

    private Map<Long, Map<String, String>> actAttrMap = new HashMap<>();

    //参与活动任务的成员配置 key actId_taskId_memberId
    private Map<String, ActTaskMember> actTaskMemberMap = Maps.newConcurrentMap();

    //参与活动任务的成员配置 key actId_memberId
    private Map<String, List<ActTaskMember>> actMemberTaskMemberMap = Maps.newConcurrentMap();

    //活动任务配置 key taskId
    private Map<Long, ActTask> actTaskMap = Maps.newConcurrentMap();

    //活动任务配置 key actId_hdztRankId_hdztPhaseId
    private Map<String, List<ActTask>> actTaskRankIdPhaseIdMap = Maps.newConcurrentMap();

    //活动任务子任务配置 key taskId
    private Map<Long, List<ActTaskItem>> actTaskItemMap = Maps.newConcurrentMap();

    //活动任务奖励配置 key taskId
    private Map<Long, List<ActTaskAward>> actTaskAwardMap = Maps.newConcurrentMap();

    //榜单展示配置
    private Map<Long, List<ActShowRankConfig>> actShowRankConfigMap = Maps.newLinkedHashMap();

    //挂件展示配置 第一层key actId 第二层key
    private Map<Long, Map<String, ActLayerViewDefine>> actLayerViewDefine = Maps.newLinkedHashMap();

    /**
     * 活动组件数据存储策略 key actId_cmptId_cmptIndex
     */
    private Map<String, ComponentStoragePolicy> componentStoragePolicyMap = Maps.newLinkedHashMap();

    public static final String LIMIT_NOTIFY_EVENT_OBJECTS = "com.yy.gameecology.activity.bean.ChannelChatTextInnerEvent,";


    @Cached(timeToLiveMillis = 900 * 1000L)
    public String hello(String name) {
        return name + "|" + DateUtil.today();
    }

    @PostConstruct
    @NeedRecycle(author = "liweizhou", notRecycle = true)
    public void init() {
        refresh();
    }


    @Scheduled(cron = "0/20 * * * * ?")
    @ScheduledExt(historyRun = true)
    @NeedRecycle(author = "gouliping", notRecycle = true)
    public void refresh() {
        Clock clock = new Clock();
        try {
            count++;
            // 让测试环境加快更新， 生产环境为20秒 x 3 = 60秒 = 1分钟
            final int three = 3;
            if (SysEvHelper.isDeploy() && (count % three != 1)) {
                return;
            }

            try {
                //Const.YZPM.reloadAll();
                Const.GEPM.reloadAll();
            } catch (Exception e) {
                e.printStackTrace();
            }

            refreshActAttrMap();

            intActTaskMemberCache();

            initActTaskCache();

            initActTaskItemCache();

            initActTaskAwardCache();

            initShowRankConfig();

            initActLayerViewDefine();

            // 活动组件相关
            initHdzjComponentMaps();
            initHdzjComponentUiMaps();
            initHdzjComponentAttrMaps();
            initHdzjActivityMap();
            initComponentStoragePolicyMap();

            log.info("refresh ok@counter:{} {}", count, clock.tag());
        } catch (Throwable t) {
            log.error("refresh exception@counter:{}, err:{} {}", count, t.getMessage(), clock.tag());
        }
    }

    private void refreshActAttrMap() {
        List<GeActAttr> geActAttrs = geActAttrMapper.selectByExample(null);
        actAttrMap = geActAttrs.stream().collect(Collectors.groupingBy(GeActAttr::getActId,
                Collectors.toMap(GeActAttr::getAttrName, GeActAttr::getAttrValue)));
    }

    public Map<Long, Map<String, String>> getActAttrMap() {
        return actAttrMap;
    }


    public List<SettleViewConfig> getSettleViewConfig(long actId) {
        String config = getActAttrValue(actId, GeActAttrConst.SETTLE_VIEW_CONFIG);
        if (StringUtil.isBlank(config)) {
            return null;
        }
        try {
            return JSON.parseArray(config, SettleViewConfig.class);
        } catch (Exception e) {
            log.error("getSettleViewConfig error,actId:{},e:{}", actId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 活动扩展配置
     */
    public String getActAttrValue(Long actId, String attrName) {
        return getActAttrValue(actId, attrName, "");
    }

    public String getActAttrValue(Long actId, String attrName, String defaultValue) {
        if (actAttrMap == null) {
            return defaultValue;
        }
        return actAttrMap.getOrDefault(actId, Maps.newConcurrentMap()).getOrDefault(attrName, defaultValue);
    }

    private void intActTaskMemberCache() {
        List<ActTaskMember> actTaskMembers = gameecologyDao.getAllActTaskMember();
        Map<String, ActTaskMember> tmpMap = Maps.newConcurrentMap();
        Map<String, List<ActTaskMember>> tmpActMemberTaskMemberMap = Maps.newConcurrentMap();
        for (ActTaskMember actTaskMember : actTaskMembers) {
            String key = actTaskMember.getActId() + "_" + actTaskMember.getTaskId() + "_" + actTaskMember.getMemberId();
            tmpMap.put(key, actTaskMember);

            String actMemberIdKey = actTaskMember.getActId() + "_" + actTaskMember.getMemberId();
            List<ActTaskMember> taskMembers = tmpActMemberTaskMemberMap.getOrDefault(actMemberIdKey, Lists.newArrayList());
            taskMembers.add(actTaskMember);
            tmpActMemberTaskMemberMap.put(actMemberIdKey, taskMembers);
        }

        actTaskMemberMap = tmpMap;
        actMemberTaskMemberMap = tmpActMemberTaskMemberMap;
    }

    private void initActTaskCache() {
        Map<Long, ActTask> tmpActTaskMap = Maps.newConcurrentMap();
        Map<String, List<ActTask>> tmpActTaskRankIdPhaseIdMap = Maps.newConcurrentMap();

        List<ActTask> actTasks = gameecologyDao.select(ActTask.class);
        for (ActTask actTask : actTasks) {
            tmpActTaskMap.put(actTask.getTaskId(), actTask);

            String key = actTask.getActId() + "_" + actTask.getHdztRankId() + "_" + actTask.getHdztPhaseId();
            List<ActTask> tasks = tmpActTaskRankIdPhaseIdMap.getOrDefault(key, Lists.newArrayList());
            tasks.add(actTask);
            tmpActTaskRankIdPhaseIdMap.put(key, tasks);
        }

        actTaskMap = tmpActTaskMap;
        actTaskRankIdPhaseIdMap = tmpActTaskRankIdPhaseIdMap;

    }

    private void initActTaskItemCache() {
        Map<Long, List<ActTaskItem>> tmpTaskItemMap = Maps.newConcurrentMap();
        List<ActTaskItem> actTaskItems = gameecologyDao.select(ActTaskItem.class);

        for (ActTaskItem taskItem : actTaskItems) {
            List<ActTaskItem> taskItems = tmpTaskItemMap.getOrDefault(taskItem.getTaskId(), Lists.newArrayList());
            taskItems.add(taskItem);
            tmpTaskItemMap.put(taskItem.getTaskId(), taskItems);
        }

        actTaskItemMap = tmpTaskItemMap;
    }

    private void initActTaskAwardCache() {
        Map<Long, List<ActTaskAward>> tmpActTaskAwardMap = Maps.newConcurrentMap();
        List<ActTaskAward> awards = gameecologyDao.select(ActTaskAward.class);
        for (ActTaskAward award : awards) {
            List<ActTaskAward> actTaskAwards = tmpActTaskAwardMap.getOrDefault(award.getTaskId(), Lists.newArrayList());
            actTaskAwards.add(award);
            tmpActTaskAwardMap.put(award.getTaskId(), actTaskAwards);
        }

        actTaskAwardMap = tmpActTaskAwardMap;
    }

    /**
     * 参与任务的成员信息配置
     */
    public ActTaskMember getActTaskMember(long actId, long taskId, String memberId) {
        String key = actId + "_" + taskId + "_" + memberId;
        return actTaskMemberMap.get(key);
    }

    /**
     * 参与任务的成员信息配置
     */
    public List<ActTaskMember> getActTaskMembers(long actId, String memberId) {
        String key = actId + "_" + memberId;
        return actMemberTaskMemberMap.get(key);
    }


    /**
     * 获取活动任务配置
     */
    public ActTask getActTaskConfig(long actId, long taskId, Integer status) {
        ActTask actTask = actTaskMap.get(taskId);
        if (actTask != null && actTask.getStatus().equals(status)) {
            return actTask;
        }
        return null;
    }

    /**
     * 获取活动任务配置
     */
    public List<ActTask> getActTaskConfigs(long actId, long hdztRankId, long hdztPhaseId) {
        String key = actId + "_" + hdztRankId + "_" + hdztPhaseId;
        return actTaskRankIdPhaseIdMap.get(key);
    }

    /**
     * 子任务配置
     * key: level任务等级 value: 需要完成的任务列表,返回的结果保证从低级到高级的顺序
     */
    public Map<Integer, List<ActTaskItem>> getActTaskItem(long taskId) {
        List<ActTaskItem> allTaskItems = actTaskItemMap.get(taskId);
        if (CollectionUtils.isEmpty(allTaskItems)) {
            return Maps.newHashMap();
        }

        Map<Integer, List<ActTaskItem>> levelTaskItemMap = Maps.newHashMap();
        for (ActTaskItem taskItem : allTaskItems) {
            List<ActTaskItem> levelTaskItems = levelTaskItemMap.getOrDefault(taskItem.getLevel(), Lists.newArrayList());
            levelTaskItems.add(taskItem);
            levelTaskItemMap.put(taskItem.getLevel(), levelTaskItems);
        }

        return levelTaskItemMap;
    }

    /**
     * 获取任务配置的奖励
     */
    public List<ActTaskAward> getTaskAward(long taskId, int roleType) {
        List<ActTaskAward> awards = actTaskAwardMap.get(taskId);
        if (CollectionUtils.isEmpty(awards)) {
            return Lists.newArrayList();
        }
        return awards.stream().filter(x -> x.getRoleType() == roleType).collect(Collectors.toList());
    }


    /**
     * 活动组件, 第一层KEY： act_id, 第二层KEY：cmpt_id|cmpt_use_inx
     */
    private Map<Long, Map<String, HdzjComponent>> hdzjComponentMaps = Maps.newLinkedHashMap();

    /**
     * 活动组件属性, 第一层KEY： act_id|cmpt_id|cmpt_use_inx, 第二层KEY：name，第二层值：value
     */
    private Map<String, Map<String, String>> hdzjComponentAttrMaps = Maps.newLinkedHashMap();

    /**
     * 活动组件UI, 第一层KEY： act_id|cmpt_id|cmpt_use_inx, 第二层KEY：name，第二层值：value
     */
    private Map<String, Map<String, String>> hdzjComponentUiMaps = Maps.newLinkedHashMap();


    /**
     * 初始化 hdzj_component 缓存数据
     * 使用 LinkedHashMap 保证结果顺序
     */
    private Map<Long, Map<String, HdzjComponent>> initHdzjComponentMaps() {
        Clock clock = new Clock();
        try {
            Map<Long, Map<String, HdzjComponent>> maps = Maps.newLinkedHashMap();
            List<HdzjComponent> list = gameecologyDao.select(HdzjComponent.class, null, "order by act_id, show_order, cmpt_id, cmpt_use_inx", false);
            if (list != null) {
                for (HdzjComponent item : list) {
                    Long actId = item.getActId();
                    Map<String, HdzjComponent> map = maps.computeIfAbsent(actId, k -> Maps.newLinkedHashMap());
                    map.put(makeKey(item.getCmptId(), item.getCmptUseInx()), item);
                }
            }
            this.hdzjComponentMaps = maps;
            log.info("initHdzjComponentMaps ok -> " + maps.size() + clock.tag());
            return maps;
        } catch (Throwable t) {
            log.error("initHdzjComponentMaps exception@err:{} {}", t.getMessage(), clock.tag(), t);
            return null;
        }
    }

    public Map<Long, Map<String, HdzjComponent>> getHdzjComponentMaps() {
        return hdzjComponentMaps;
    }

    public Map<String, HdzjComponent> getHdzjComponentMap(long actId) {
        return getHdzjComponentMaps().getOrDefault(actId, Maps.newLinkedHashMap());
    }

    public HdzjComponent getHdzjComponent(long actId, long cmptId, long cmptUseInx) {
        return getHdzjComponentMap(actId).get(makeKey(cmptId, cmptUseInx));
    }

    public Map<Long, HdzjComponent> getHdzjComponentMap(long actId, long cmptId) {
        Map<Long, HdzjComponent> result = Maps.newLinkedHashMap();
        for (HdzjComponent hdzjComponent : getHdzjComponentMap(actId).values()) {
            if (cmptId == hdzjComponent.getCmptId()) {
                result.put(hdzjComponent.getCmptUseInx(), hdzjComponent);
            }
        }
        return result;
    }

    /**
     * 初始化 hdzj_cmpt_ui 缓存数据
     * 使用 LinkedHashMap 保证结果顺序
     */
    private Map<String, Map<String, String>> initHdzjComponentUiMaps() {
        Clock clock = new Clock();
        try {
            Map<String, Map<String, String>> maps = Maps.newLinkedHashMap();
            List<HdzjComponentUi> list = gameecologyDao.select(HdzjComponentUi.class, null, "order by act_id, cmpt_id, cmpt_use_inx, name", false);
            if (list != null) {
                for (HdzjComponentUi item : list) {
                    String key = makeKey(item.getActId(), item.getCmptId(), item.getCmptUseInx());
                    Map<String, String> map = maps.computeIfAbsent(key, k -> Maps.newLinkedHashMap());
                    map.put(StringUtil.trim(item.getName()), StringUtil.trim(item.getValue()));
                }
            }
            this.hdzjComponentUiMaps = maps;
            log.info("initHdzjComponentUiMaps ok -> " + maps.size() + clock.tag());
            return maps;
        } catch (Throwable t) {
            log.error("initHdzjComponentUiMaps exception@err:{} {}", t.getMessage(), clock.tag(), t);
            return null;
        }
    }

    public Map<String, Map<String, String>> getHdzjComponentUiMaps() {
        return hdzjComponentUiMaps;
    }

    public Map<String, String> getHdzjComponentUiMap(Long actId, Long cmptId, Long cmptUseInx) {
        return getHdzjComponentUiMaps().getOrDefault(makeKey(actId, cmptId, cmptUseInx), Maps.newLinkedHashMap());
    }

    public String getHdzjComponentUiValue(Long actId, Long cmptId, Long cmptUseInx, String name) {
        return getHdzjComponentUiMap(actId, cmptId, cmptUseInx).getOrDefault(name, "");
    }

    /**
     * 初始化 hdzj_cmpt_attr 缓存数据
     * 使用 LinkedHashMap 保证结果顺序
     */
    private Map<String, Map<String, String>> initHdzjComponentAttrMaps() {
        Clock clock = new Clock();
        try {
            Map<String, Map<String, String>> maps = Maps.newLinkedHashMap();
            List<HdzjComponentAttr> list = gameecologyDao.select(HdzjComponentAttr.class, null, "order by act_id, cmpt_id, cmpt_use_inx, name", false);
            if (list != null) {
                for (HdzjComponentAttr item : list) {
                    String key = makeKey(item.getActId(), item.getCmptId(), item.getCmptUseInx());
                    Map<String, String> map = maps.computeIfAbsent(key, k -> Maps.newLinkedHashMap());
                    map.put(StringUtil.trim(item.getName()), StringUtil.trim(item.getValue()));
                }
            }
            this.hdzjComponentAttrMaps = maps;
            log.info("initHdzjComponentAttrMaps ok -> " + maps.size() + clock.tag());
            return maps;
        } catch (Throwable t) {
            log.error("initHdzjComponentAttrMaps exception@err:{} {}", t.getMessage(), clock.tag(), t);
            return null;
        }
    }

    public Map<String, Map<String, String>> getHdzjComponentAttrMaps() {
        return hdzjComponentAttrMaps;
    }

    public Map<String, String> getHdzjComponentAttrMap(Long actId, Long cmptId, Long cmptUseInx) {
        return getHdzjComponentAttrMaps().getOrDefault(makeKey(actId, cmptId, cmptUseInx), Maps.newLinkedHashMap());
    }

    public String getHdzjComponentAttrValue(Long actId, Long cmptId, Long cmptUseInx, String name) {
        return getHdzjComponentAttrMap(actId, cmptId, cmptUseInx).getOrDefault(name, "");
    }


    /**
     * 平台活动, KEY： hdzj_activity
     */
    private Map<Long, HdzjActivity> hdzjActivityMap = Maps.newLinkedHashMap();


    /**
     * 初始化 hdzj_activity 缓存数据
     * 使用 LinkedHashMap 保证结果顺序
     */
    private Map<Long, HdzjActivity> initHdzjActivityMap() {
        Clock clock = new Clock();
        try {
            Map<Long, HdzjActivity> map = Maps.newLinkedHashMap();
            List<HdzjActivity> list = gameecologyDao.select(HdzjActivity.class, null, "order by act_id", false);
            if (list != null) {
                for (HdzjActivity item : list) {
                    Long actId = item.getActId();
                    map.put(actId, item);
                }
            }
            this.hdzjActivityMap = map;
            log.info("initHdzjActivityMap ok -> " + map.size() + clock.tag());
            return map;
        } catch (Throwable t) {
            log.error("initHdzjActivityMap exception@err:{} {}", t.getMessage(), clock.tag(), t);
            return null;
        }
    }

    private void initComponentStoragePolicyMap() {
        Clock clock = new Clock();
        try {
            Map<String, ComponentStoragePolicy> map = Maps.newLinkedHashMap();
            List<ComponentStoragePolicy> list = gameecologyDao.select(ComponentStoragePolicy.class, null, "order by act_id", false);
            if (list != null) {
                for (ComponentStoragePolicy item : list) {
                    map.put(makeKey(item.getActId(), item.getCmptId(), item.getCmptUseInx()), item);
                }
            }
            this.componentStoragePolicyMap = map;
            log.info("initComponentStoragePolicyMap ok -> " + map.size() + clock.tag());
        } catch (Throwable t) {
            log.error("initComponentStoragePolicyMap exception@err:{} {}", t.getMessage(), clock.tag(), t);
        }
    }


    private void initShowRankConfig() {
        Clock clock = new Clock();
        try {
            ActShowRankConfig where = new ActShowRankConfig();
            where.setStatus(1);
            String afterWhere = " order by sort asc ";
            List<ActShowRankConfig> configs = gameecologyDao.select(ActShowRankConfig.class, where, afterWhere);
            Map<Long, List<ActShowRankConfig>> tmp = Maps.newLinkedHashMap();
            for (ActShowRankConfig config : configs) {
                List<ActShowRankConfig> configList = tmp.getOrDefault(config.getActId(), Lists.newArrayList());
                configList.add(config);
                tmp.put(config.getActId(), configList);
            }
            actShowRankConfigMap = tmp;

        } catch (Throwable t) {
            log.error("initShowRankConfig exception@err:{} {}", t.getMessage(), clock.tag(), t);
        }
    }

    private void initActLayerViewDefine() {

        Clock clock = new Clock();
        try {
            ActLayerViewDefine where = new ActLayerViewDefine();
            where.setStatus(1);
            String afterWhere = " order by sort asc ";
            List<ActLayerViewDefine> configs = gameecologyDao.select(ActLayerViewDefine.class, where, afterWhere);
            Map<Long, Map<String, ActLayerViewDefine>> tmp = Maps.newLinkedHashMap();
            for (ActLayerViewDefine config : configs) {
                Map<String, ActLayerViewDefine> configList = tmp.getOrDefault(config.getActId(), Maps.newLinkedHashMap());
                configList.put(config.getItemTypeKey(), config);
                tmp.put(config.getActId(), configList);
            }
            actLayerViewDefine = tmp;

        } catch (Throwable t) {
            log.error("initActLayerViewDefine exception@err:{} {}", t.getMessage(), clock.tag(), t);
        }
    }

    public Map<String, ActLayerViewDefine> getActLayerViewDefine(Long actId) {
        return actLayerViewDefine.getOrDefault(actId, Maps.newLinkedHashMap());
    }

    public boolean containsLayerViewDefine(Long actId) {
        return MapUtils.isNotEmpty(getActLayerViewDefine(actId));
    }

    @VisibleForTesting
    public void forceReloadActivityAndCmp() {
        initHdzjComponentMaps();
        initHdzjComponentUiMaps();
        initHdzjComponentAttrMaps();
        initHdzjActivityMap();
    }

    /**
     * 获取活动榜单展示配置
     *
     * @param actId 活动id
     */
    public List<ActShowRankConfig> getActShowRankConfig(Long actId) {
        return actShowRankConfigMap.get(actId);
    }


    public Map<Long, HdzjActivity> getHdzjActivityMap() {
        return hdzjActivityMap;
    }

    public HdzjActivity getHdzjActivity(long actId) {
        return hdzjActivityMap.get(actId);
    }

    /**
     * 获取组件存储策略
     */
    public ComponentStoragePolicy getComponentStoragePolicy(long actId, long cmptId, long cmptIndex) {
        return componentStoragePolicyMap.get(makeKey(actId, cmptId, cmptIndex));
    }


    public static String makeKey(Long n1, Long n2) {
        return n1 + "|" + n2;
    }

    public static String makeKey(Long n1, Long n2, Long n3) {
        return n1 + "|" + n2 + "|" + n3;
    }

    public static String makeKey(Long n1, Long n2, Long n3, Long n4) {
        return n1 + "|" + n2 + "|" + n3 + "|" + n4;
    }

    public static String makeKey(Long n1, Long n2, Long n3, Long n4, Long n5) {
        return n1 + "|" + n2 + "|" + n3 + "|" + n4 + "|" + n5;
    }
}
