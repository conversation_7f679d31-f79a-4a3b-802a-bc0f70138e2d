package com.yy.gameecology.activity.service.rankext;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.rank.*;
import com.yy.gameecology.common.db.model.gameecology.ComponentWhitelist;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.element.component.RankBuilderComponent;
import com.yy.gameecology.hdzj.element.component.WhitelistComponent;
import com.yy.gameecology.hdzj.element.component.attr.RankBuilderComponentAttr;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RankingInfo;
import com.yy.thrift.zhuiwan_newfamily.FamilyBasicInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.swing.text.html.Option;
import java.lang.reflect.Field;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * desc:昵称白名单替换
 *
 * <AUTHOR>
 * @date 2024-01-02 11:56
 **/
@Component
public class WhiteListNickExtHandler implements RankExtHandler {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private RankBuilderComponent rankBuilderComponent;

    @Autowired
    private WhitelistComponent whitelistComponent;

    @Override
    public long getOrder() {
        return RankExtHandler.WHITE_LIST_NICK_ORDER;
    }

    @Override
    public List<String> supportKeys() {
        return null;
    }

    @Override
    public List<Object> addViewExt(GetRankReq rankReq, RankingInfo rankingInfo, List<Rank> ranks, List<Object> objectList) {
        if (CollectionUtils.isEmpty(objectList)) {
            return objectList;
        }
        RankBuilderComponentAttr attr = rankBuilderComponent.tryGetUniqueComponentAttr(rankReq.getActId());
        if (attr == null) {
            return objectList;
        }
        if (attr.getMemberNameCmptIndex() <= 0) {
            return objectList;
        }

        List<String> members = ranks.stream().map(Rank::getMember).toList();
        List<ComponentWhitelist> componentWhitelists = whitelistComponent.batchGetComponentWhitelist(attr.getActId(), (int) attr.getMemberNameCmptIndex(), members);

        try {
            for (Object rankItem : objectList) {

                if (rankItem instanceof RoomRankItem) {
                    String member = ((RoomRankItem) rankItem).getKey();
                    String replaceMember = getMemberName(member, componentWhitelists);
                    if (StringUtil.isNotBlank(replaceMember)) {
                        ((RoomRankItem) rankItem).setName(replaceMember);
                    }

                } else if (rankItem instanceof BabyRankItem) {
                    String member = ((BabyRankItem) rankItem).getKey();
                    String replaceMember = getMemberName(member,componentWhitelists);
                    if(StringUtil.isNotBlank(replaceMember)){
                        ((BabyRankItem) rankItem).setNick(replaceMember);
                    }

                } else if (rankItem instanceof SubGuildRankItem) {
                    String member = ((SubGuildRankItem) rankItem).getKey();
                    String replaceMember = getMemberName(member,componentWhitelists);
                    if(StringUtil.isNotBlank(replaceMember)){
                        ((SubGuildRankItem) rankItem).setName(replaceMember);
                    }

                } else if (rankItem instanceof GuildRankItem) {
                    String member = ((GuildRankItem) rankItem).getKey();
                    String replaceMember = getMemberName(member,componentWhitelists);
                    if(StringUtil.isNotBlank(replaceMember)){
                        ((GuildRankItem) rankItem).setName(replaceMember);
                    }

                } else if (rankItem instanceof UserRankItem) {
                    String member = ((UserRankItem) rankItem).getKey();
                    String replaceMember = getMemberName(member,componentWhitelists);
                    if(StringUtil.isNotBlank(replaceMember)){
                        ((UserRankItem) rankItem).setNick(replaceMember);
                    }

                }
            }
        } catch (Exception e) {
            log.error("WhiteListNickExtHandler replate name error,e:{},req:{},objectList:{}", e.getMessage(), JSON.toJSONString(rankReq), JSON.toJSONString(objectList), e);
        }

        return objectList;

    }

    private String getMemberName(String member, List<ComponentWhitelist> componentWhitelists) {
        Optional<ComponentWhitelist> whitelistOption = componentWhitelists.stream().filter(p -> p.getMember().equals(member)).findFirst();
        return whitelistOption.map(ComponentWhitelist::getConfigValue).orElse(null);
    }
}
