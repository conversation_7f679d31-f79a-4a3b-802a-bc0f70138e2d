package com.yy.gameecology.activity.service.rankext;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.rank.BabyRankItem;
import com.yy.gameecology.activity.bean.rank.RankItemUserAnchor;
import com.yy.gameecology.activity.bean.rank.RankValueItemBase;
import com.yy.gameecology.activity.bean.rank.UserRankItem;
import com.yy.gameecology.activity.bean.uniqcp.UniqCpRankItem;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.activity.service.HdztRankService;
import com.yy.gameecology.activity.service.HdztRankServiceUseOne;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.consts.RankExtParaKey;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.thrift.hdztranking.ActorInfoItem;
import com.yy.thrift.hdztranking.ActorQueryItem;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RankingInfo;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: yulianzhu
 * @date: 2023.02.17
 * @description: 我的上榜信息
 * status: -1->无任何榜单信息 1->第一名 2->在榜上 3->有榜单信息,但未在topN
 */
@Component
@AllArgsConstructor
public class MyRankOnTopExtHandler implements RankExtHandler {

    private static final long LAST_SUPPORT_ACT_ID = 2024121001;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private HdztRankService hdztRankService;

    @Autowired
    private CommonService commonService;

    public static final String MY_RANK_ON_TOP = "onTopExt";

    //理论上辅助榜应该和主榜相同的阶段ID, 所以只需要榜单ID cp_assist_rank_id_rankId
    public static final String CP_ASSIST_RANK_ID = "cp_assist_rank_id_%s";

    @Override
    public List<String> supportKeys() {
        return null;
    }

    /**
     * @param rankReq
     * @param rankingInfo
     * @param ranks
     * @param objectList  最后一个为指定的member , 空队列前面已经过滤
     * @return
     */
    @Override
    public List<Object> addViewExt(GetRankReq rankReq, RankingInfo rankingInfo, List<Rank> ranks, List<Object> objectList) {
        Long loginUid = rankReq.getLoginUid();
        if (loginUid == null || loginUid == 0 || !rankReq.getExtQueryList().contains(MY_RANK_ON_TOP)) {
            return objectList;
        }

        if (SysEvHelper.isHistory()) {
            return objectList;
        }
        boolean isCp = objectList.get(0) instanceof RankItemUserAnchor;
        if (isCp) {
            handleCPInfo(rankReq, loginUid, objectList);
        } else {
            if (rankReq.getActId() >= LAST_SUPPORT_ACT_ID) {
                throw new IllegalArgumentException("use 'pointedMember' parameter for not cp rank");
            }
            handleSingleInfo(rankReq, loginUid, objectList, ranks);
        }
        return objectList;
    }

    private void handleSingleInfo(GetRankReq rankReq, Long loginUid, List<Object> objectList, List<Rank> ranks) {
        // 正常的指定pointedMember的查询，不处理
        if (HdztRankServiceUseOne.isValidPointedMember(rankReq.getPointedMember())) {
            return;
        }
        int status = -1;
        RankValueItemBase lastMember;
        RankValueItemBase myInfo = null;

        long offsetScore = 0;
        int size = objectList.size();
        for (int i = 0; i < size; i++) {
            Object objItem = objectList.get(i);
            if (!(objItem instanceof RankValueItemBase)) {
                return;
            }

            RankValueItemBase rankItem = (RankValueItemBase) objectList.get(i);
            if (StringUtils.equals(rankItem.getKey(), String.valueOf(loginUid))) {
                if (i == 0) {
                    status = 1;
                    offsetScore = 0;
                } else {
                    lastMember = (RankValueItemBase) objectList.get(i - 1);
                    status = 2;
                    offsetScore = lastMember.getValue() - rankItem.getValue();
                }
                myInfo = rankItem;
                break;
            }
        }

        //未上榜
        if (status == -1) {
            lastMember = (RankValueItemBase) objectList.get(size - 1);
            myInfo = getSingleNotInRankResult(rankReq, loginUid, lastMember);
        } else {
            if (myInfo.getViewExt() == null) {
                myInfo.setViewExt(Maps.newHashMap());
            }
            // myInfo.getViewExt().put("rank", myInfo.getRank() + "");
            myInfo.getViewExt().put("status", status + "");
            myInfo.getViewExt().put("offsetScore", offsetScore + "");
        }

        //已在榜单上,将其加入队列后,后面逻辑会添加到pointedMember
        rankReq.setPointedMember(myInfo.getKey());
        objectList.add(myInfo);
    }

    private void handleCPInfo(GetRankReq rankReq, Long loginUid, List<Object> objectList) {
        int status = -1;
        RankItemUserAnchor lastMember;
        RankItemUserAnchor myInfo = null;

        long offsetScore = 0;
        for (int i = 0; i < objectList.size(); i++) {
            RankItemUserAnchor userAnchor = (RankItemUserAnchor) objectList.get(i);
            if (userAnchor.getUserRankItem().getUid().equals(loginUid)) {
                if (i == 0) {
                    status = 1;
                    offsetScore = 0;
                } else {
                    lastMember = (RankItemUserAnchor) objectList.get(i - 1);
                    status = 2;
                    offsetScore = lastMember.getScore() - userAnchor.getScore();
                }
                myInfo = userAnchor;
                break;
            } else if (userAnchor.getBabyRankItem().getUid().equals(loginUid)) {
                if (i == 0) {
                    status = 1;
                    offsetScore = 0;
                } else {
                    lastMember = (RankItemUserAnchor) objectList.get(i - 1);
                    status = 2;
                    offsetScore = lastMember.getScore() - userAnchor.getScore();
                }
                myInfo = userAnchor;
                break;
            }
        }
        //未上榜
        if (status == -1) {
            lastMember = (RankItemUserAnchor) objectList.getLast();
            myInfo = getCpNotInRankResult(rankReq, loginUid, lastMember);
        } else {
            if (myInfo.getViewExt() == null) {
                myInfo.setViewExt(Maps.newHashMap());
            }
            myInfo.getViewExt().put("status", status + "");
            myInfo.getViewExt().put("offsetScore", offsetScore + "");
        }

        //已在榜单上,将其加入队列后,后面逻辑会添加到pointedMember
        rankReq.setPointedMember(myInfo.getKey());
        objectList.add(myInfo);
    }

    /**
     * @param rankReq
     * @param loginUid
     * @param theLastMember 最后一个成员
     */
    private RankItemUserAnchor processCpNotInRank(GetRankReq rankReq, Long loginUid, RankItemUserAnchor theLastMember) {
        Map<String, String> ext = Maps.newHashMap();
        ext.put(RankExtParaKey.RANK_TYPE_HOVER_SRC_ID, String.valueOf(loginUid));
        //查询榜单获取分值 todo 查询辅助榜

        String assistRankIdKey = String.format(CP_ASSIST_RANK_ID, rankReq.getRankId());
        String assistRankId = commonService.getActAttr(rankReq.getActId(), assistRankIdKey);

        List<Rank> ranks = hdztRankingThriftClient.queryRankingCache(rankReq.getActId(), Convert.toLong(assistRankId),
                rankReq.getPhaseId(), rankReq.getDateStr(), 1, "", ext);

        UserBaseInfo userInfo = commonService.getUserInfo(loginUid, false);
        UserRankItem userRankItem = new UserRankItem();
        userRankItem.setNick(userInfo.getNick());
        userRankItem.setAvatarInfo(userInfo.getLogo());

        RankItemUserAnchor userAnchor = new RankItemUserAnchor();
        Map<String, String> viewExt = Maps.newHashMap();
        //没有cp or 未上榜
        viewExt.put("status", "-1");

        long offsetScore;
        if (CollectionUtils.isEmpty(ranks)) {

            //todo 构建空的信息 done
            userAnchor.setUserRankItem(userRankItem);
            userAnchor.setRank(-1);
            userAnchor.setScore(0L);
            offsetScore = theLastMember.getScore();
            userAnchor.setKey(loginUid + "|0");
        } else {
            Rank rank = ranks.get(0);
            long score = rank.getScore();
            //构建cp信息
            //userAnchor.setRank(rank.getRank());
            userAnchor.setScore(score);
            userAnchor.setUserRankItem(userRankItem);
            userAnchor.setValue(score);

            long babyUid = Convert.toLong(rank.getMember());
            UserBaseInfo babyInfo = commonService.getUserInfo(babyUid, false);
            BabyRankItem babyRankItem = new BabyRankItem();
            babyRankItem.setNick(babyInfo.getNick());
            babyRankItem.setAvatarInfo(babyInfo.getLogo());
            babyRankItem.setValue(score);
            userAnchor.setBabyRankItem(babyRankItem);

            userAnchor.setKey(rank.getMember());
            offsetScore = theLastMember.getScore() - score;
        }
        viewExt.put("offsetScore", offsetScore + "");
        userAnchor.setViewExt(viewExt);
        return userAnchor;
    }

    private RankItemUserAnchor getCpNotInRankResult(GetRankReq rankReq, Long loginUid, RankItemUserAnchor theLastMember) {
        String dateStr = rankReq.getDateStr();
        if (dateStr == null) {
            dateStr = StringUtil.EMPTY;
        }

        String findSrcMember = rankReq.getFindSrcMember() == null ? StringUtils.EMPTY : rankReq.getFindSrcMember();
        UniqCpRankItem uniqCpRankItem = hdztRankService.queryUniqCpTopActorInfo(findSrcMember, rankReq.getActId(), rankReq.getRankId(), rankReq.getPhaseId(), dateStr, String.valueOf(loginUid));
        if (uniqCpRankItem == null) {
            return processCpNotInRank(rankReq, loginUid, theLastMember);
        }

        final int rank = uniqCpRankItem.getRank();
        final long score = uniqCpRankItem.getScore();
        RankItemUserAnchor rankItem = new RankItemUserAnchor();
        rankItem.setViewExt(new HashMap<>(3));
        rankItem.setRank(rank);
        rankItem.setScore(score);
        //最后一名的score
        long gap = theLastMember.getScore();
        long offsetScore = Math.abs(gap - score);

        rankItem.getViewExt().put("offsetScore", String.valueOf(rank == 1 ? 0 : offsetScore));
        UserInfoVo userInfoVo = userInfoService.getUserInfo(Lists.newArrayList(loginUid), Template.all).get(loginUid);
        if (rank > 0) {
            rankItem.getViewExt().put("status", "-1");

            String member = uniqCpRankItem.getMember();
            rankItem.setKey(member);
            if (StringUtils.isNotEmpty(member) && member.contains(StringUtil.VERTICAL_BAR)) {
                long userUid = Long.parseLong(StringUtils.split(member, StringUtil.VERTICAL_BAR)[0]);
                long anchorUid = Long.parseLong(StringUtils.split(member, StringUtil.VERTICAL_BAR)[1]);
                Map<Long, UserBaseInfo> userInfoMap = commonService.batchGetUserInfos(ImmutableList.of(userUid, anchorUid), false);
                UserBaseInfo userInfo = userInfoMap.get(userUid);
                if (userInfo != null) {
                    UserRankItem userRankItem = rankItem.getUserRankItem();
                    if (userRankItem == null) {
                        userRankItem = new UserRankItem();
                    }
                    userRankItem.setRank(rank);
                    userRankItem.setValue(score);
                    userRankItem.setUid(userUid);
                    userRankItem.setNick(userInfo.getNick());
                    userRankItem.setAvatarInfo(userInfo.getHdLogo());
                    userRankItem.setYyno(userInfo.getYyno());
                    userRankItem.setGeNobleGrade(userInfoVo == null ? "": userInfoVo.getEcologyNobleId());
                    userRankItem.setNobleGrade(userInfoVo == null ? "": userInfoVo.getNobleId());
                    rankItem.setUserRankItem(userRankItem);
                }

                UserBaseInfo anchorInfo = userInfoMap.get(anchorUid);
                if (anchorInfo != null) {
                    BabyRankItem babyRankItem = rankItem.getBabyRankItem();
                    if (babyRankItem == null) {
                        babyRankItem = new BabyRankItem();
                    }

                    babyRankItem.setRank(rank);
                    babyRankItem.setValue(score);
                    babyRankItem.setUid(anchorUid);
                    babyRankItem.setNick(anchorInfo.getNick());
                    babyRankItem.setAvatarInfo(anchorInfo.getHdLogo());
                    babyRankItem.setYyno(anchorInfo.getYyno());

                    rankItem.setBabyRankItem(babyRankItem);
                }
            }
        } else {
            //未上榜
            rankItem.getViewExt().put("status", "-1");
            rankItem.setKey(loginUid + StringUtil.VERTICAL_BAR + "0");
            UserBaseInfo userInfo = commonService.getUserInfo(loginUid, false);
            if (userInfo != null) {
                UserRankItem userRankItem = new UserRankItem();
                userRankItem.setRank(rank);
                userRankItem.setValue(score);
                userRankItem.setUid(loginUid);
                userRankItem.setNick(userInfo.getNick());
                userRankItem.setAvatarInfo(userInfo.getHdLogo());
                userRankItem.setYyno(userInfo.getYyno());
                userRankItem.setGeNobleGrade(userInfoVo == null ? "": userInfoVo.getEcologyNobleId());
                userRankItem.setNobleGrade(userInfoVo == null ? "": userInfoVo.getNobleId());
                rankItem.setUserRankItem(userRankItem);
            }
        }


        return rankItem;
    }


    private UserRankItem processSingleNotInRank(GetRankReq rankReq, Long loginUid, UserRankItem theLastMember) {

        Rank rank = hdztRankingThriftClient.queryPointedMembersRanking(rankReq.getActId(), rankReq.getRankId(),
                rankReq.getPhaseId(), rankReq.getDateStr(), String.valueOf(loginUid), Maps.newHashMap());

        UserBaseInfo userInfo = commonService.getUserInfo(loginUid, false);
        UserRankItem rankItem = new UserRankItem();
        rankItem.setNick(userInfo.getNick());
        rankItem.setAvatarInfo(userInfo.getLogo());

        Map<String, String> viewExt = rankItem.getViewExt();

        //没有分值 or 未上榜
        viewExt.put("status", "-1");

        long offsetScore;
        if (rank == null) {
            rankItem.setValue(0L);
            offsetScore = theLastMember.getValue();

            rankItem.setKey(loginUid + "");
        } else {
            long score = rank.getScore() < 0 ? 0 : rank.getScore();
            rankItem.setRank(rank.getRank());
            rankItem.setValue(score);
            rankItem.setKey(rank.getMember());
            offsetScore = theLastMember.getValue() - score;

        }
        viewExt.put("offsetScore", offsetScore + "");
        rankItem.setViewExt(viewExt);
        return rankItem;
    }

    private RankValueItemBase getSingleNotInRankResult(GetRankReq rankReq, Long loginUid, RankValueItemBase lastItem) {
        ActorQueryItem queryItem = new ActorQueryItem();
        queryItem.setRankingId(rankReq.getRankId());
        queryItem.setPhaseId(rankReq.getPhaseId());
        queryItem.setActorId(String.valueOf(loginUid));
        queryItem.setDateStr(rankReq.getDateStr());
        queryItem.setWithStatus(false);
        ActorInfoItem infoItem = hdztRankingThriftClient.queryActorRankingInfo(rankReq.getActId(), queryItem);
        UserBaseInfo userInfo = commonService.getUserInfo(loginUid, false);

        if (infoItem != null && userInfo != null && lastItem instanceof UserRankItem) {
            return buildUserRankItem(userInfo, infoItem, (UserRankItem) lastItem);
        }

        if (infoItem != null && userInfo != null && lastItem instanceof BabyRankItem) {
            return buildAnchorRankItem(userInfo, infoItem, (BabyRankItem) lastItem);
        }

        return processSingleNotInRank(rankReq, loginUid, (UserRankItem) lastItem);
    }

    private UserRankItem buildUserRankItem(UserBaseInfo userInfo, ActorInfoItem infoItem, UserRankItem lastItem) {
        UserRankItem rankItem = new UserRankItem();
        Map<String, String> viewExt = new HashMap<>(3);
        rankItem.setViewExt(viewExt);

        rankItem.setUid(userInfo.getUid());
        rankItem.setNick(userInfo.getNick());
        rankItem.setYyno(userInfo.getYyno());
        rankItem.setAvatarInfo(userInfo.getHdLogo());

        int rank = (int) infoItem.rank;
        rankItem.setRank(rank);
        rankItem.setValue(infoItem.score);
        viewExt.put("status", "-1");
        viewExt.put("offsetScore", String.valueOf(lastItem.getValue() - infoItem.score));

        return rankItem;
    }

    private BabyRankItem buildAnchorRankItem(UserBaseInfo userInfo, ActorInfoItem infoItem, BabyRankItem lastItem) {
        BabyRankItem rankItem = new BabyRankItem();
        Map<String, String> viewExt = new HashMap<>(3);
        rankItem.setViewExt(viewExt);

        rankItem.setUid(userInfo.getUid());
        rankItem.setNick(userInfo.getNick());
        rankItem.setYyno(userInfo.getYyno());
        rankItem.setAvatarInfo(userInfo.getHdLogo());

        int rank = (int) infoItem.rank;
        rankItem.setRank(rank);
        rankItem.setValue(infoItem.score);
        viewExt.put("status", "-1");
        viewExt.put("offsetScore", String.valueOf(lastItem.getValue() - infoItem.score));

        return rankItem;
    }
}
