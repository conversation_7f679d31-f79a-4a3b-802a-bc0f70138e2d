/**
 * RankingTimeStart.java / 2020年7月29日 下午3:21:50
 * <p>
 * Copyright (c) 2020, YY Inc. All Rights Reserved.
 * <p>
 * 郭立平[<EMAIL>]
 */
package com.yy.gameecology.activity.bean.hdzt;

/**
 * <AUTHOR>
 * @date 2020年7月29日 下午3:21:50
 */
public class RankingTimeEnd extends BaseEvent {

    public static final long URI = BaseEvent.RANKING_TIME_END;
    /**
     * 时间分榜: 0-不按时间再分, 1-按日再分，2-按小时再分
     **/
    private long timeKey;
    /**
     * 根据 timeKey 动态计算的结束时间，精确到秒（yyyy-MM-dd HH:mm:ss)
     **/
    private String endTime;

    public RankingTimeEnd() {
        super(URI);
    }

    public long getTimeKey() {
        return timeKey;
    }

    public void setTimeKey(long timeKey) {
        this.timeKey = timeKey;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}
