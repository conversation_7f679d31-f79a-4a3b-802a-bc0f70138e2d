package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.event.AppBannerEvent2;
import com.yy.gameecology.activity.bean.event.AppBannerLayout;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaConfig2;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaText;
import com.yy.gameecology.activity.client.yrpc.DanmakuActivityClient;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.consts.BroadcastType;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.bean.AwardRoll;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 通用广播Service
 *
 * @auther: guojunhui
 * @create: 2020/10/28 10:53
 */
@Service
public class CommonBroadCastService {
    private static Logger log = LoggerFactory.getLogger(CommonBroadCastService.class);

    @Autowired
    private SvcSDKService svcSDKService;

    @Autowired
    protected UserInfoService userInfoService;

    @Autowired
    protected CommonService commonService;


    @Autowired
    protected KafkaService kafkaService;

    @Autowired
    protected DanmakuActivityClient danmakuActivityClient;

    /**
     * 发送高光时刻动效
     *
     * @param roleType   角色类型, 1:主播 2:公会
     * @param nickName   昵称,roleType为1时必填
     * @param asid       签约频道(短号),roleType为2时必填
     * @param honorValue 荣耀值
     * @param template   业务模板
     */
    public void sendHighlightMomentMotion(int roleType, String nickName, long asid, String honorValue, Template template) {
        roleType = RoleType.ANCHOR.getValue() == roleType ? 1 : (RoleType.GUILD.getValue() == roleType ? 2 : roleType);
        GameecologyActivity.Act202011_HighlightMomentMotion.Builder banner = GameecologyActivity.Act202011_HighlightMomentMotion
                .newBuilder().setRoleType(roleType).setNickName(nickName).setAsid(asid).setHonorValue(honorValue);
        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.kAct202011_HighlightMomentMotion_VALUE)
                .setAct202011HighlightMomentMotion(banner).build();
        svcSDKService.broadcastTemplate(template, msg);
    }

    /**
     * 专属时刻弹窗
     *
     * @param receiveUids 弹窗接受者uids
     * @param actId       活动id，非陪玩HDZT_ID， 陪玩HDZT_ID_PW
     * @param nickName    主播昵称， popType=1时必传
     * @param asid        公会asid， popType=2时必传
     * @param awards      奖励
     * @param moment      专属时刻: 【赛程时刻】、【高光时刻】、【任务时刻】
     * @param popType     弹窗类型, 1:主播 2:公会
     */
    public void popExclusiveMoment(List<Long> receiveUids, long actId, String nickName, long asid, List<String> awards, String moment, int popType) {
        GameecologyActivity.Act202011_ExclusiveMomentPop.Builder pop = GameecologyActivity.Act202011_ExclusiveMomentPop.newBuilder()
                .setActId(actId)
                .setNickName(nickName)
                .setAsid(asid)
                .setMoment(moment)
                .setPopType(popType);
        for (String award : awards) {
            pop.addAward(award);
        }

        GameecologyActivity.GameEcologyMsg msp = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.kAct202011_ExclusiveMomentPop_VALUE)
                .setAct202011ExclusiveMomentPop(pop).build();
        for (Long uid : receiveUids) {
            svcSDKService.unicastUid(uid, msp);
        }
    }

    /**
     * 陪玩CP小时榜广播
     *
     * @param userNickName   用户昵称
     * @param anchorNickName 主播昵称
     * @param actId          活动id
     */
    public void pwCPHourRankResultBroadcast(String userNickName, String anchorNickName, Long actId) {
        GameecologyActivity.Act202010_PWHourRankResultBroadcast.Builder banner = GameecologyActivity.Act202010_PWHourRankResultBroadcast.newBuilder()
                .setUserNickName(userNickName)
                .setAnchorNickName(anchorNickName)
                .setActId(actId);
        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.kAct202010_PWHourRankResultBroadcast_VALUE)
                .setAct202010PWHourRankResultBroadcast(banner).build();
        svcSDKService.broadcastAllChanelsInPW(actId, msg);
    }

    /**
     * 发送锦鲤礼包中奖者广播
     *
     * @param actId      活动Id
     * @param nickName   中奖者昵称
     * @param reward     奖品名称
     * @param winnerType // 1:主播 2:用户
     * @param template   非空：单业务；空:全频道
     * @param isPeiWan   // true:陪玩，false:非陪玩
     */
    public void sendKoiWinnerBroadcast(Long actId, String nickName, String reward, int winnerType, Template template, boolean isPeiWan) {
        GameecologyActivity.Act202011_KoiWinnerBroadcast.Builder banner = GameecologyActivity.Act202011_KoiWinnerBroadcast.newBuilder()
                .setActId(actId)
                .setNickName(nickName)
                .setReward(reward)
                .setWinnerType(winnerType);
        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.kAct202011_KoiWinnerBroadcast_VALUE)
                .setAct202011KoiWinnerBroadcast(banner).build();
        if (isPeiWan) {
            svcSDKService.broadcastAllChanelsInPW(actId, msg);
        } else {
            if (template == null) {
                for (Template temp : Template.values()) {
                    svcSDKService.broadcastTemplate(temp, msg);
                }
            } else {
                svcSDKService.broadcastTemplate(template, msg);
            }
        }
    }

    /**
     * 通用中奖弹窗
     */
    public void awardNoticeUnicastWithRetryCheck(String seq, long actId, long uid, List<AwardRoll> awardRolls) {
        String noticeType = "awardNotice";
        Map<String, Object> noticeExt = Maps.newHashMap();
        noticeExt.put("awardList", awardRolls);
        String extInfo = JSON.toJSONString(noticeExt);
        RetryTool.withRetryCheck(actId, seq + ":notice_user", () -> {
            this.commonNoticeUnicast(actId, noticeType, noticeType, extInfo, uid);
        });
    }


    /**
     * 通用弹窗单播
     *
     * @param actId       活动id
     * @param noticeType  通知类型,需要和前端协定好
     * @param noticeValue 通知数据,需要和前端协定好
     * @param noticeExt   扩展数据,需要和前端协定好
     * @param uid         用户id
     **/
    public void commonNoticeUnicast(long actId, String noticeType, String noticeValue, String noticeExt, long uid) {
        GameecologyActivity.CommonNoticeResponse.Builder tips = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(actId)
                .setNoticeType(noticeType)
                .setNoticeValue(noticeValue)
                .setExtJson(noticeExt);

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(tips).build();
        svcSDKService.unicastUid(uid, msg);

        log.info("commonNoticeUnicast done@uid:{} msg:{}", uid, JsonFormat.printToString(tips.build()));
    }

    /**
     * 交友业务通用横幅广播
     *
     * @param actId    活动id
     * @param uid      用户id
     * @param score    分值
     * @param svgaUrl  svga地址
     * @param bannerId 横幅id,需要和前端协商好
     **/
    public void commonBannerBroadcastToMakeFriend(long actId, long uid, long score, String svgaUrl, long bannerId) {
        commonBannerBroadcastOfSub(actId, uid, score, svgaUrl, bannerId, Template.Jiaoyou);
    }

    public void commonBannerBroadcast(long actId, long uid, long score, String svgaUrl, long bannerId, long bannerType, Map<String, Object> jsonMap, Template template) {
        GameecologyActivity.BannerBroadcast.Builder bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId)
                .setBannerId(bannerId)
                .setAnchorUid(uid)
                .setAnchorNick("")
                .setAnchorLogo("")
                .setAnchorScore(score)
                .setBannerType(bannerType)
                .setJsonData(JSON.toJSONString(jsonMap));

        UserCurrentChannel channel = commonService.getNoCacheUserCurrentChannel(uid);
        if (channel != null) {
            bannerBroadcast.setSid(channel.getTopsid());
            bannerBroadcast.setSsid(channel.getSubsid());
            bannerBroadcast.setAsid(commonService.getAsid(channel.getTopsid()));
        }


        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();
        svcSDKService.broadcastTemplate(actId, template, msg);

        log.info("commonBannerBroadcast info@uid:{},value:{},msg:{}", uid, score, JsonFormat.printToString(msg));
    }

    public void commonBannerBroadcast(long actId, long uid, long score, String svgaUrl, long bannerId, long bannerType, Object jsonMap, List<ChannelInfo> channelInfos) {
        GameecologyActivity.BannerBroadcast.Builder bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId)
                .setBannerId(bannerId)
                .setAnchorUid(uid)
                .setAnchorNick("")
                .setAnchorLogo("")
                .setAnchorScore(score)
                .setBannerType(bannerType)
                .setJsonData(JSON.toJSONString(jsonMap));

        if (uid > 0) {
            UserCurrentChannel channel = commonService.getNoCacheUserCurrentChannel(uid);
            if (channel != null) {
                bannerBroadcast.setSid(channel.getTopsid());
                bannerBroadcast.setSsid(channel.getSubsid());
                bannerBroadcast.setAsid(commonService.getAsid(channel.getTopsid()));
            }
        }


        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();

        channelInfos.forEach(c -> {
            if (c.getSsid() > 0) {
                svcSDKService.broadcastSub(c.getSid(), c.getSsid(), msg);
            } else {
                svcSDKService.broadcastTop(c.getSid(), msg);
            }
        });


        log.info("commonBannerBroadcast info@uid:{},value:{},msg:{}", uid, score, JsonFormat.printToString(msg));
    }


    /**
     * broType 2-子频道广播 3-顶级频道下所有子厅广播/家族下面所有子厅
     */
    public List<ChannelInfo> getChannelInfos(long sid, long ssid, long familyId, long broType) {
        List<ChannelInfo> channelInfos = Lists.newArrayList();
        if (BroadcastType.SUB_CHANNEL == broType) {
            ChannelInfo channelInfo = new ChannelInfo();
            channelInfo.setSid(sid);
            channelInfo.setSsid(ssid);
            channelInfos.add(channelInfo);
            return channelInfos;
        }
        if (BroadcastType.TOP_CHANNEL == broType) {
            if (familyId > 0) {
                return  commonService.listFamilySsid2(familyId);
            } else {
                ChannelInfo channelInfo = new ChannelInfo();
                channelInfo.setSid(sid);
                channelInfo.setSsid(0);
                channelInfos.add(channelInfo);
                return channelInfos;
            }
        }

        throw new RuntimeException("not support broType:" + broType);
    }

    /**
     * @param familyId 家族ID，如果需要广播家族下的所有厅，则需要填写
     * @param broType  broType 2-子频道广播 3-顶级频道下所有子厅广播/家族下面所有子厅 4-全模板
     */
    public void commonBannerBroadcast(long sid, long ssid, long familyId, Template template, long broType,
                                      long actId, long uid, long score, long bannerId, long bannerType, Object jsonMap) {
     GameecologyActivity.BannerBroadcast.Builder bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId)
                .setBannerId(bannerId)
                .setAnchorUid(uid)
                .setAnchorNick("")
                .setAnchorLogo("")
                .setAnchorScore(score)
                .setBannerType(bannerType)
                .setJsonData(JSON.toJSONString(jsonMap));


        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();

        if (BroadcastType.SUB_CHANNEL == broType || BroadcastType.TOP_CHANNEL == broType) {
            List<ChannelInfo> channelInfos = getChannelInfos(sid, ssid, familyId, broType);
            Set<Long> distinctTopSid = Sets.newHashSet();
            Set<Long> distinctSubSid = Sets.newHashSet();
            channelInfos.forEach(c -> {
                if (BroadcastType.TOP_CHANNEL == broType && familyId <= 0) {
                    if (!distinctTopSid.contains(c.getSid())) {
                        svcSDKService.broadcastTop(c.getSid(), msg);
                        distinctTopSid.add(c.getSid());
                    } else {
                        log.warn("commonBannerBroadcast sid dup,skip sid:{}", c.getSid());
                    }
                } else {
                    if (!distinctSubSid.contains(c.getSsid())) {
                        svcSDKService.broadcastSub(c.getSid(), c.getSsid(), msg);
                        distinctSubSid.add(c.getSsid());
                    } else {
                        log.warn("commonBannerBroadcast sub sssid dup,sssid sid:{}", c.getSsid());
                    }
                }

            });
        } else if (BroadcastType.ALL_TEMPLATE == broType) {
            svcSDKService.broadcastTemplate(actId, template, msg);
        } else {
            throw new RuntimeException("not support broType:" + broType);
        }


        log.info("commonBannerBroadcast info@uid:{},value:{},msg:{}", uid, score, JsonFormat.printToString(msg));
    }

    public void commonBannerBroadcastAllTemplateExcludeDanmu(Template template,
                                      long actId, long uid, long score, long bannerId, long bannerType,  Object jsonMap) {
        GameecologyActivity.BannerBroadcast.Builder bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId)
                .setBannerId(bannerId)
                .setAnchorUid(uid)
                .setAnchorNick("")
                .setAnchorLogo("")
                .setAnchorScore(score)
                .setBannerType(bannerType)
                .setJsonData(JSON.toJSONString(jsonMap));


        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();

        List<ChannelInfo> danmakuChannel = danmakuActivityClient.queryAllChannelInfo();
        Set<String> exclude = danmakuChannel.stream().map(channel -> channel.getSid() + StringUtil.UNDERSCORE + channel.getSsid()).collect(Collectors.toSet());
        svcSDKService.broadcastTemplateExclude(template, msg, exclude);

        log.info("commonBannerBroadcastAllTemplate info@uid:{},value:{},msg:{} exclude:{}", uid, score, JsonFormat.printToString(msg),exclude);
    }


    public void commonBannerBroadcastOfSub(long actId, long uid, long score, String svgaUrl, long bannerId, long bannerType, Map<String, Object> jsonMap) {
        GameecologyActivity.BannerBroadcast.Builder bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId)
                .setBannerId(bannerId)
                .setAnchorUid(uid)
                .setAnchorNick("")
                .setAnchorLogo("")
                .setAnchorScore(score)
                .setBannerType(bannerType)
                .setJsonData(JSON.toJSONString(jsonMap));

        UserCurrentChannel channel = commonService.getNoCacheUserCurrentChannel(uid);
        if (channel != null) {
            bannerBroadcast.setSid(channel.getTopsid());
            bannerBroadcast.setSsid(channel.getSubsid());
            bannerBroadcast.setAsid(commonService.getAsid(channel.getTopsid()));

            GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                    .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                    .setBannerBroadcast(bannerBroadcast).build();
            svcSDKService.broadcastSub(channel.getTopsid(), channel.getSubsid(), msg);

            log.info("commonBannerBroadcast info@uid:{},value:{},msg:{}", uid, score, JsonFormat.printToString(msg));
        } else {
            log.info("user not in channel,uid={}", uid);
        }
    }

    private void commonBannerBroadcastOfSub(long actId, long uid, long score, String svgaUrl, long bannerId, Template template) {
        String name;
        String logo;
        UserInfoVo userInfoVo = userInfoService.getUserInfo(Lists.newArrayList(uid)
                , com.yy.gameecology.common.bean.Template.getTemplateFromThriftEnum(template.getValue())).get(uid);
        if (userInfoVo != null) {
            name = userInfoVo.getNick();
            logo = userInfoVo.getAvatarUrl();
        } else {
            UserBaseInfo userInfo = commonService.getUserInfo(uid, false);
            name = userInfo.getNick();
            logo = userInfo.getLogo();
        }
        String jsonData = JSON.toJSONString(Collections.singletonMap("svgaUrl", svgaUrl));

        GameecologyActivity.BannerBroadcast.Builder bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId)
                .setBannerId(bannerId)
                .setAnchorUid(uid)
                .setAnchorNick(name)
                .setAnchorLogo(logo)
                .setAnchorScore(score)
                .setJsonData(jsonData);

        UserCurrentChannel channel = commonService.getNoCacheUserCurrentChannel(uid);
        if (channel != null) {
            bannerBroadcast.setSid(channel.getTopsid());
            bannerBroadcast.setSsid(channel.getSubsid());
            bannerBroadcast.setAsid(commonService.getAsid(channel.getTopsid()));
        }


        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();
        svcSDKService.broadcastTemplate(template, msg);

        log.info("commonBannerBroadcast info@uid:{},value:{},msg:{}", uid, score, JsonFormat.printToString(msg));
    }

    /**
     * app 通用单播顶部横幅
     *
     * @param actId
     * @param uid
     * @param busiId      810-语音房，500-交友，400-宝贝,参考com.yy.thrift.hdztranking.BusiId
     * @param bgUrl       横幅的背景图片
     * @param logUrl      横幅的图标图片
     * @param text        富文本消息，html格式，{uid:n} ：表示需要nick替换，{uid:an}：表示需要头像icon+nick，{img}：表示需要使用imgs list按顺序取出替换
     * @param imageList   text 里面的{img}，会按按顺序替换
     * @param nickUidList 需要多昵称的uid，俊哥那边会做替换处理
     */
    public void appUnicastCommonTopBanner(long actId, long uid, int busiId, String bgUrl, String logUrl, String text, List<String> imageList, List<Long> nickUidList) {

        //这是通用svga，需要替换背景和图标
        String svgaUrl = "https://gamebaby.bs2dl.yy.com/adminweb/sig6pxnhegmkhdasxe8hchqztbpdqebw.svga";

        AppBannerSvgaConfig2 broSvgaConfig = new AppBannerSvgaConfig2();
        AppBannerSvgaText appBannerSvgaText = new AppBannerSvgaText();
        appBannerSvgaText.setText(text);
        appBannerSvgaText.setImgs(imageList != null ? imageList : List.of());
        //svga内嵌文字
        broSvgaConfig.setContentLayers(List.of(Map.of("key_wenzi", appBannerSvgaText)));
        //svga内嵌图片
        broSvgaConfig.setImgLayers(List.of(Map.of("key_bg", bgUrl), Map.of("key_logo", logUrl)));

        broSvgaConfig.setJump(0);
        broSvgaConfig.setHeight(0);
        broSvgaConfig.setDuration(10);
        broSvgaConfig.setLoops(1);

        AppBannerLayout layout = new AppBannerLayout();
        layout.setType(0);
        broSvgaConfig.setLayout(layout);
        broSvgaConfig.setSvgaURL(svgaUrl);

        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(actId, UUID.randomUUID().toString()
                , BroadCastHelpService.toAppBroBusiness(busiId), AppBannerEvent2.BC_TYPE_USER, 0, 0, "", List.of());
        appBannerEvent.setPushUidlist(List.of(uid));
        appBannerEvent.setContentType(3);//顶部横幅
        appBannerEvent.setAppId(commonService.getTurnoverAppId(busiId));
        appBannerEvent.setSvgaConfig(broSvgaConfig);
        appBannerEvent.setUidList(nickUidList);

        kafkaService.sendAppBannerKafka(appBannerEvent);

    }


}
