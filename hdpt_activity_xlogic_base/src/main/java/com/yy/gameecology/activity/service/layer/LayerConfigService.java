package com.yy.gameecology.activity.service.layer;

import com.yy.gameecology.hdzj.element.component.ActLayerConfigComponent;
import com.yy.gameecology.hdzj.element.component.attr.ActLayerConfigComponentAttr;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2022-07-01 17:26
 **/
@Service
public class LayerConfigService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private static final int COMPONENT_INDEX = 1;

    @Autowired
    private ActLayerConfigComponent actLayerConfigComponent;

    public ActLayerConfigComponentAttr getLayerAttrConfig(long actId) {
        return getLayerAttrConfig(actId, true);
    }

    public ActLayerConfigComponentAttr getLayerAttrConfig(long actId, boolean mustGet) {
        ActLayerConfigComponentAttr attr = actLayerConfigComponent.getComponentAttr(actId, COMPONENT_INDEX);
        if (mustGet) {
            Assert.notNull(attr, String.format("未找到挂件组件配置,actId:%s,cmptId:%s,index:%s", actId, actLayerConfigComponent.getComponentId(), COMPONENT_INDEX));
        }
        return attr;
    }
}
