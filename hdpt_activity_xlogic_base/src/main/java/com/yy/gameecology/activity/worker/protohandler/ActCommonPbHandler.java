package com.yy.gameecology.activity.worker.protohandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.gameecology.activity.annotation.ProtoHandler;
import com.yy.gameecology.activity.bean.AnchorStartShowEvent;
import com.yy.gameecology.activity.bean.UserEnterTemplateEvent;
import com.yy.gameecology.activity.bean.UserRemainEvent;
import com.yy.gameecology.activity.bean.actlayer.LayerBroadcastInfo;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.mq.LayerRequestEvent;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.commons.ActAttrCont;
import com.yy.gameecology.activity.config.proto.ProtoHandlerContext;
import com.yy.gameecology.activity.service.ActPlayService;
import com.yy.gameecology.activity.service.CacheService;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.activity.service.SvcSDKService;
import com.yy.gameecology.activity.service.layer.ActLayerInfoService;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.hdzj.HdzjEventDispatcher;
import com.yy.gameecology.hdzj.bean.CommonPBOperateRequest;
import com.yy.gameecology.hdzj.bean.CommonPBOperateResp;
import com.yy.gameecology.hdzj.element.component.*;
import com.yy.gameecology.hdzj.element.history.ChannelArenaComponent;
import com.yy.gameecology.hdzj.element.history.NianshouChannelComponent;
import com.yy.gameecology.hdzj.element.history.attr.SuperWinnerComponentAttr;
import com.yy.gameecology.hdzj.element.history.SuperWinnerComponent;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.protocol.pb.GameecologyActivity.*;
import com.yy.protocol.pb.layer.LayerInfo;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.UUID;
import java.util.Map;

/**
 * <AUTHOR> 2020/7/22
 */
@Component
public class ActCommonPbHandler {

    private static final Logger log = LoggerFactory.getLogger(ActCommonPbHandler.class);

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ActLayerInfoService actLayerInfoService;

    @Autowired
    private SvcSDKService svcSDKService;

    @Autowired
    private WebdbThriftClient webdbThriftClient;

    @Autowired
    private TailLightComponent tailLightComponent;

    @Autowired
    private JyEnterChannelPopupComponent jyEnterChannelPopupComponent;

    @Autowired
    private HdzjEventDispatcher hdzjEventDispatcher;

    @Autowired
    private SuperWinnerComponent superWinnerComponent;



    @Autowired
    private CacheService cacheService;

    @ProtoHandler(PacketType.kAct202008_TailLightReq)
    public GameEcologyMsg tailLightReq(GameEcologyMsg msg) {
        long uid = ProtoHandlerContext.getUid();
        long sid = ProtoHandlerContext.getSid();
        long ssid = ProtoHandlerContext.getSsid();
        long actId = msg.getAct202008TailLightReq().getActId();
        boolean query = msg.getAct202008TailLightReq().getQuery();
        log.info("tailLightReq actId:{}，uid:{} sid:{} ssid:{},query:{}", actId, uid, sid, ssid, query);

        GameecologyActivity.Act202008_TailLightResp.Builder tailLightResp =
                tailLightComponent.tailLight(actId, uid, query);
        if (tailLightResp == null) {
            return null;
        }

        log.info("tailLightReq uid:{} resp:{}", uid, JsonFormat.printToString(tailLightResp.build()));

        return GameEcologyMsg.newBuilder()
                .setUri(PacketType.kAct202008_TailLightResp_VALUE)
                .setAct202008TailLightResp(tailLightResp)
                .build();
    }

    /**
     * 加一个进入模板事件
     *
     * @param msg
     * @return
     */
    @ProtoHandler(PacketType.kAct202103_InHouseTemplateEventReq)
    public void InHouseTemplateEventReq(GameEcologyMsg msg) {
        long uid = ProtoHandlerContext.getUid();
        long sid = ProtoHandlerContext.getSid();
        long ssid = ProtoHandlerContext.getSsid();
        String ip = ProtoHandlerContext.getIp();
        try {
            Act202103_InHouseTemplateEventReq act202103InHouseTemplateEventReq = msg.getAct202103InHouseTemplateEventReq();
            long templateId = act202103InHouseTemplateEventReq.getTemplateId();
            long busiId = act202103InHouseTemplateEventReq.getBusiId();
            String extJson = act202103InHouseTemplateEventReq.getExtjson();
            log.info("InHouseTemplateEventReq start uid:{} sid:{} ssid:{} templateId={} busiId={} ip={} extJson:{}", uid, sid, ssid, templateId, busiId, ip, extJson);
            UserEnterTemplateEvent userEnterTemplateEvent = new UserEnterTemplateEvent(uid, sid, ssid, busiId, templateId, extJson);
            userEnterTemplateEvent.setIp(ip);
            try {
                hdzjEventDispatcher.notify(uid, RoleType.USER, userEnterTemplateEvent, userEnterTemplateEvent.getSeq());
            } catch (Throwable e) {
                log.error("[InHouseTemplateEventReq notify] err message:{} userEnterTemplateEvent:{}", e.getMessage(), userEnterTemplateEvent, e);
            }

            try {
                jyEnterChannelPopupComponent.handleUserEnterChannel(uid, sid, ssid, busiId, templateId);
            } catch (Exception e) {
                log.error("jyEnterChannelPopupComponent handleUserEnterChannel fail", e);
            }
        } catch (Exception e) {
            log.warn("InHouseTemplateEventReq error", e);
        }
    }


    @ProtoHandler(PacketType.kAct202008_AnchorTipsReq)
    public void anchorTips(GameEcologyMsg msg) {
        long uid = ProtoHandlerContext.getUid();
        long sid = ProtoHandlerContext.getSid();
        long ssid = ProtoHandlerContext.getSsid();
        int bizId = msg.getAct202008AnchorTipsReq().getBizId();
        log.info("anchorTips uid:{} sid:{} ssid:{} bizId:{}", uid, sid, ssid, bizId);
        AnchorStartShowEvent anchorStartShowEvent = new AnchorStartShowEvent(uid, sid, ssid, bizId);
        try {
            hdzjEventDispatcher.notify(uid, RoleType.ANCHOR, anchorStartShowEvent, anchorStartShowEvent.getSeq());
        } catch (Throwable e) {
            log.error("[anchorTips notify] err message:{} anchorStartShowEvent:{}", e.getMessage(), anchorStartShowEvent, e);
        }
//        publisher.publishEvent(anchorStartShowEvent);

    }

    @ProtoHandler(PacketType.kAct202008_ActInfoReq)
    public GameEcologyMsg actInfo(GameEcologyMsg msg) {
        long uid = ProtoHandlerContext.getUid();
        long sid = ProtoHandlerContext.getSid();
        long ssid = ProtoHandlerContext.getSsid();

        long actId = msg.getAct202008ActInfoReq().getActId();
        log.info("kAct202008_ActInfoReq actInfo actId:{}, uid:{} sid:{} ssid:{}", actId, uid, sid, ssid);

        ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityInfo(actId);
        if (activityInfoVo == null) {
            return null;
        }
        if (activityInfoVo.getStatus() != 1) {
            return null;
        }

        GameecologyActivity.Act202008_ActInfo.Builder actInfo = GameecologyActivity.Act202008_ActInfo.newBuilder()
                .setActId(activityInfoVo.getActId())
                .setBeginTime(activityInfoVo.getBeginTime() / 1000)
                .setEndTime(activityInfoVo.getEndTime() / 1000)
                .setStartShowTime(activityInfoVo.getBeginTimeShow() / 1000)
                .setEndShowTime(activityInfoVo.getEndTimeShow() / 1000)
                .setCurrentTime(activityInfoVo.getCurrentTime() / 1000);

        return GameEcologyMsg.newBuilder()
                .setUri(PacketType.kAct202008_ActInfo_VALUE)
                .setAct202008ActInfo(actInfo)
                .build();

    }


    /**
     * 拉取挂件信息
     *
     * @param msg
     */
    @Report
    @ProtoHandler(PacketType.LayerBroadcastRequestUri)
    public void layerBroadcastRequest(GameEcologyMsg msg) {
        long uid = ProtoHandlerContext.getUid();
        long sid = ProtoHandlerContext.getSid();
        long ssid = ProtoHandlerContext.getSsid();
        long actId = msg.getLayerBroadcastRequest().getActId();

        if (SysEvHelper.isDev() || commonService.isGrey(actId)) {
            log.info("layerBroadcastRequest start,uid:{},sid:{},ssid:{}", uid, sid, ssid);
        }

        long busiId = webdbThriftClient.getBusiId(sid, ssid);
        if (busiId == BusiId.PEI_WAN.getValue()) {
            ssid = 0;
        }

        ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityInfo(actId);
        if (activityInfoVo == null) {
            return;
        }
        if (activityInfoVo.getStatus() != 1) {
            return;
        }

        String useEvent = cacheService.getActAttrValue(actId, ActAttrCont.USE_LAYER_REQUEST_EVENT);
        if (Const.ONESTR.equals(useEvent)) {
            eventForwardLayerInfoRequest(actId, sid, ssid, uid);
        } else {
            directResponseLayerInfo(actId, sid, ssid, uid);
        }

    }

    private void eventForwardLayerInfoRequest(long actId, long sid, long ssid, long uid) {
        LayerRequestEvent event = new LayerRequestEvent();
        event.setActId(actId);
        event.setUid(uid);
        event.setSid(sid);
        event.setSsid(ssid);

        hdzjEventDispatcher.notify(actId, event, UUID.randomUUID().toString(), true);
    }

    private void directResponseLayerInfo(long actId, long sid, long ssid, long uid) {
        LayerBroadcastInfo layerBroadcastInfo = null;
        Date now = commonService.getNow(actId);
        String from = "web";
        if (actLayerInfoService.inSettle(actId, now)) {
            layerBroadcastInfo = actLayerInfoService.buildLayerInfo(actId, sid, ssid, now, from);
        } else {
            //Sequence时序 也会缓存了旧的，所以这里保证老的消息不会覆盖新的
            layerBroadcastInfo = actLayerInfoService.getLayerInfo(actId, sid, ssid, from);
        }

        LayerInfo.LayerBroadcast layerInfo = layerBroadcastInfo.toBroadcastInfoPb();

        GameEcologyMsg gameEcologyMsg = GameEcologyMsg.newBuilder()
                .setUri(PacketType.LayerBroadcast_VALUE)
                .setLayerBroadcast(layerInfo)
                .build();

        if (SysEvHelper.isDev() || commonService.isGrey(actId)) {
            log.info("layerBroadcastRequest,uid:{},sid:{},ssid:{},response:{}", uid, sid, ssid, JSON.toJSONString(layerBroadcastInfo));
        }

        svcSDKService.unicastUid(uid, gameEcologyMsg);
    }

    @ProtoHandler(PacketType.kActUserRemainReqUri)
    public void userRemain(GameEcologyMsg msg) {
        long uid = ProtoHandlerContext.getUid();
        long sid = ProtoHandlerContext.getSid();
        long ssid = ProtoHandlerContext.getSsid();
        long actId = msg.getActUserRemainReq().getActId();
        long bizId = msg.getActUserRemainReq().getBusiId();
        long remainTime = msg.getActUserRemainReq().getRemainTime();
        log.info("anchorTips uid:{} sid:{} ssid:{} bizId:{}", uid, sid, ssid, bizId);
        UserRemainEvent userRemainEvent = new UserRemainEvent(actId, uid, sid, ssid, bizId, remainTime);
        try {
            hdzjEventDispatcher.notify(actId, uid, RoleType.USER, userRemainEvent, userRemainEvent.getSeq());
        } catch (Throwable e) {
            log.error("[userRemain notify] err message:{} userRemainEvent:{}", e.getMessage(), userRemainEvent, e);
        }
    }

    /**
     * 宝箱参与抽奖
     *
     * <AUTHOR> 2021/11/04
     */
    @ProtoHandler(GameecologyActivity.PacketType.kSuperWinnerDrawReqUri)
    public void superWinnerDraw(GameEcologyMsg reqMsg) {
        Clock clock = new Clock();
        long uid = ProtoHandlerContext.getUid();
        long sid = ProtoHandlerContext.getSid();
        long ssid = ProtoHandlerContext.getSsid();
        SuperWinnerDrawReq req = reqMsg.getSuperWinnerDrawReq();
        long actId = req.getActId();
        String boxId = reqMsg.getSuperWinnerDrawReq().getBoxId();
        String ip = ProtoHandlerContext.getIp();

        GameEcologyMsg.Builder geMsgBuilder = GameEcologyMsg.newBuilder();
        geMsgBuilder.setUri(PacketType.kSuperWinnerDrawRspUri_VALUE);

        SuperWinnerDrawRsp.Builder rspBuilder = SuperWinnerDrawRsp.newBuilder()
                .setActId(actId)
                .setBoxId(boxId)
                .setRetCode(99)
                .setRetMsg("系统繁忙，请稍后再试！");
        int ret = -1;
        try {
            ret = superWinnerComponent.attend(actId, sid, ssid, uid, boxId);
            rspBuilder.setRetCode(ret);
            rspBuilder.setRetMsg(SuperWinnerComponentAttr.getRetMsg(ret));
            GameEcologyMsg rspMsg = geMsgBuilder.setSuperWinnerDrawRsp(rspBuilder).build();
            svcSDKService.unicastUid(uid, rspMsg);
            log.info("superWinnerDraw done@uid:{} sid:{} ssid:{} ip:{} req:{}, rsp:{} {}", uid, sid, ssid, ip,
                    JsonFormat.printToString(req), JsonFormat.printToString(rspBuilder.build()), clock.tag());
        } catch (Throwable t) {
            GameEcologyMsg rspMsg = geMsgBuilder.setSuperWinnerDrawRsp(rspBuilder).build();
            svcSDKService.unicastUid(uid, rspMsg);
            log.error("superWinnerDraw excepiton@uid:{}, sid:{}, ssid:{}, ip:{}, req:{}, ret:{}, err:{} {}",
                    uid, sid, ssid, ip, JsonFormat.printToString(req), ret, t.getMessage(), clock.tag(), t);
        }
    }


    @Autowired
    private NianshouChannelComponent nianshouChannelComponent;

    @ProtoHandler(PacketType.kTreasureBoxDrawReqUri)
    public void treasureBoxDraw(GameEcologyMsg reqMsg) {
        Clock clock = new Clock();
        long uid = ProtoHandlerContext.getUid();
        long sid = ProtoHandlerContext.getSid();
        long ssid = ProtoHandlerContext.getSsid();
        TreasureBoxDrawReq req = reqMsg.getTreasureBoxDrawReq();
        long actId = req.getActId();
        String ip = ProtoHandlerContext.getIp();

        GameEcologyMsg.Builder geMsgBuilder = GameEcologyMsg.newBuilder();
        geMsgBuilder.setUri(PacketType.kTreasureBoxDrawRespUri_VALUE);

        TreasureBoxDrawResp.Builder rspBuilder = TreasureBoxDrawResp.newBuilder()
                .setActId(actId)
                .setRetCode(99)
                .setRetMsg("系统繁忙，请稍后再试！");
        try {
            String extjson = req.getExtjson();
            JSONObject jsonObject = JSON.parseObject(extjson);
            log.info("treasureBoxDraw uid:{},extjson:{}, channel:{}", uid, extjson, sid);

            int ret = nianshouChannelComponent.attend(actId, uid, jsonObject);
            String msg;
            switch (ret) {
                case -1:
                    msg = "未绑定手机号,不可参与打年兽!";
                    break;
                case -4:
                    msg = "每条年兽一人只能打一次!";
                    break;
                case -5:
                    msg = "抱歉!您的鞭炮不足,无法打年兽!";
                    break;
                case -6:
                    rspBuilder.setExtjson(jsonObject.getString("iconLevel"));
                    msg = "未对年兽造成伤害,下次再接再厉!";
                    break;
                case -7:
                    //重复请求,不处理
                    log.info("treasureBoxDraw done@uid:{} sid:{} ssid:{} extjson:{},ret: {} {}", uid, sid, ssid, extjson, ret, clock.tag());
                    return;
//                    break;
                case 1:
                    msg = "打到年兽啦,等待结果出炉!";
                    break;
                default:
                    msg = "未对年兽造成伤害,下次再接再厉!";
            }
            rspBuilder.setRetCode(ret);
            rspBuilder.setRetMsg(msg);


            GameEcologyMsg rspMsg = geMsgBuilder.setTreasureBoxDrawResp(rspBuilder).build();
            svcSDKService.unicastUid(uid, rspMsg);
            log.info("treasureBoxDraw done@uid:{} sid:{} ssid:{} extjson:{},ret: {} {}", uid, sid, ssid, extjson, ret, clock.tag());
        } catch (Exception t) {
            GameEcologyMsg rspMsg = geMsgBuilder.setTreasureBoxDrawResp(rspBuilder).build();
            svcSDKService.unicastUid(uid, rspMsg);
            log.error("treasureBoxDraw excepiton@uid:{} sid:{} ssid:{} ip:{} req:{} err:{} {}", uid, sid, ssid, ip, t.getMessage(), clock.tag(), t);
        }
    }


    /**
     * 通用请求
     */
    @ProtoHandler(PacketType.CommonOperateRequestUri)
    public GameEcologyMsg commonOperate(GameEcologyMsg msg) {
        Clock clock = new Clock();
        long uid = ProtoHandlerContext.getUid();
        long sid = ProtoHandlerContext.getSid();
        long ssid = ProtoHandlerContext.getSsid();
        String ip = ProtoHandlerContext.getIp();
        Map<Long, String> ext = ProtoHandlerContext.getExt();

        CommonPBOperateRequest request = new CommonPBOperateRequest(msg.getCommonOperateRequest());
        request.setOpUid(uid);
        request.setOpSid(sid);
        request.setOpSsid(ssid);
        request.setIp(ip);
        request.setExt(ext);

        try {
            CommonPBOperateResp resp = hdzjEventDispatcher.commonOperatePbRequest(request);
            if (resp == null) {
                return null;
            }

            log.info("commonOperate,msg:{},rsp:{},clock:{}", JSON.toJSONString(request), JSON.toJSONString(resp), clock.tag());

            return GameEcologyMsg.newBuilder()
                    .setUri(PacketType.CommonOperateRespUri_VALUE)
                    .setCommonOperateResp(resp.toPb(request))
                    .build();
        } catch (Exception e) {
            log.error("commonOperate error,msg:{},clock:{},e:{}", msg.getCommonOperateRequest().toString(), clock.tag(), e.getMessage(), e);
            return null;
        }
    }


}
