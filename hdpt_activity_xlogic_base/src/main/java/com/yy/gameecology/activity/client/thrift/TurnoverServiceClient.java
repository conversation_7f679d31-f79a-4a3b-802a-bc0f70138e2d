
package com.yy.gameecology.activity.client.thrift;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.utils.Clock;
import com.yy.thrift.turnover.*;
import com.yy.thrift.turnover.TTurnoverService.Iface;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
public class TurnoverServiceClient {

    private static Logger log = LoggerFactory.getLogger(TurnoverServiceClient.class);

    @Reference(protocol = "nythrift_compact", owner = "${turnoverService_client_s2sname}", registry = "consumer-reg", timeout = 3000, parameters = {"threads", "10"})
    public Iface proxy;

    @Reference(protocol = "nythrift_compact", owner = "${turnoverService_client_s2sname}", registry = "consumer-reg", timeout = 3000, parameters = {"threads", "10"}
            , retries = 2 , cluster = "failover")
    public Iface readProxy;

    public Iface getProxy() {
        return proxy;
    }

    public Iface getReadProxy() {
        return readProxy;
    }

    public List<TUserProps> getAllUserPropsByPropId(long uid, int propId, int appid) {
        Clock clock = new Clock();
        try {
            return getReadProxy().getAllUserPropsByPropId(uid, propId, appid);
        } catch (Exception e) {
            log.error("getAllUserPropsByPropId fail:", e);
        } finally {
            log.info("getAllUserPropsByPropId finish with tag:{}", clock.tag());
        }

        return Collections.emptyList();
    }

    @Report
    public TContract queryContractByAnchor(long uid, TAppId appid) {
        try {
            Clock clock = new Clock();
            TContract result = getReadProxy().queryContractByAnchor(uid, appid);
            log.info("queryContractByAnchor ok@uid:{}, appid:{}, liveUid:{}, sid:{}, owUid:{} {}", uid, appid,
                    result.getLiveUid(), result.getSid(), result.getOwUid(), clock.tag());
            return result;
        } catch (Exception e) {
            log.error("queryContractByAnchor error@uid:{}, appid:{}, err:{}", uid, appid, e.getMessage(), e);
            return null;
        }
    }

    @Report
    public Map<Long, TContract> batchQueryContractByAnchors(List<Long> uids, TAppId appId) {
        try {
            Clock clock = new Clock();
            Map<Long, TContract> result = getReadProxy().batchQueryContractByAnchors(uids, appId);

            return result;
        } catch (Exception e) {
            log.error("batchQueryContractByAnchors error@uids:{}, appid:{}, err:{}", uids, appId, e.getMessage(), e);
            return Maps.newHashMap();
        }
    }

    /**
     * 发放活动道具接口
     *
     * @return 1-发放成功， 非1都是某种情况的失败或异常
     */
    @Report
    public int addPropsActivity(long uid, int propId, int count, String addSeqId,
                                TAppId appid, String expand, long sid, long ssid, int addType, long activityId) {
        Clock clock = new Clock();
        try {
            int result = getProxy().addPropsActivity(uid, propId, count, addSeqId, appid, expand, sid, ssid, addType, activityId);
            log.info("addPropsActivity done@uid:{}, propId:{}, count:{}, addSeqId:{}, appid:{}, expand:{}, sid:{}, ssid:{}, addType:{}, activityId:{}, ret:{} {}",
                    uid, propId, count, addSeqId, appid, expand, sid, ssid, addType, activityId, result, clock.tag());
            return result;
        } catch (Exception e) {
            log.error("addPropsActivity exception@uid:{}, propId:{}, count:{}, addSeqId:{}, appid:{}, expand:{}, sid:{}, ssid:{}, addType:{}, activityId:{}, err:{} {}",
                    uid, propId, count, addSeqId, appid, expand, sid, ssid, addType, activityId, e.getMessage(), clock.tag(), e);
            int code = (e instanceof TServiceException) ? ((TServiceException) e).getCode() : 9999;
            // 订单序号已存在当作成功
            return code == -405 ? 1 : code;
        }
    }

    /**
     * 为用户发放代金券
     *
     * @return 1-发放成功， 非1都是某种情况的失败或异常
     */
    @Report
    public int issueCoupon(long uid, long couponId, TAppId appid, long activityId, String seqId, String desc, TCouponSrcType couponSrcType, int count, String expand) {
        Clock clock = new Clock();
        try {
            int result = getProxy().issueCoupon(uid, couponId, 0, appid, activityId, seqId, desc, couponSrcType, count, expand);
            log.info("issueCoupon done@uid:{}, couponId:{}, appid:{}, activityId:{}, seqId:{}, desc:{}, couponSrcType:{}, count:{}, expand:{}, ret:{} {}",
                    uid, couponId, appid, activityId, seqId, desc, couponSrcType, count, expand, result, clock.tag());
            return result;
        } catch (Exception e) {
            log.error("issueCoupon exception@uid:{}, couponId:{}, appid:{}, activityId:{}, seqId:{}, desc:{}, couponSrcType:{}, count:{}, expand:{}, err:{} {}",
                    uid, couponId, appid, activityId, seqId, desc, couponSrcType, count, expand, e.getMessage(), clock.tag(), e);
            int code = (e instanceof TServiceException) ? ((TServiceException) e).getCode() : 9999;
            // 订单序号已存在当作成功
            return code == -405 ? 1 : code;
        }
    }

    /**
     * 发放Y币接口
     *
     * @return 1-发放成功， 非1都是某种情况的失败或异常
     */
    @Report
    public int issueYbRewards(long uid, TAppId appid, long activityId, String seqId, long amount/*单位是分，不要搞错！！！*/) {
        Clock clock = new Clock();
        String extend = JSON.toJSONString(ImmutableMap.of("amount", amount));
        try {
            int result = getProxy().issueYbRewards(uid, appid, activityId, seqId, extend);
            log.info("issueYbRewards done@uid:{}, appid:{}, activityId:{}, seqId:{}, extend:{}, ret:{} {}", uid, appid,
                    activityId, seqId, extend, result, clock.tag());
            return result;
        } catch (Exception e) {
            log.error("issueYbRewards exception@uid:{}, appid:{}, activityId:{}, seqId:{}, extend:{}, err:{} {}", uid, appid,
                    activityId, seqId, extend, e.getMessage(), clock.tag(), e);
            int code = (e instanceof TServiceException) ? ((TServiceException) e).getCode() : 9999;
            // 订单序号已存在当作成功
            return code == -405 ? 1 : code;
        }
    }


    /**
     * 为用户发放水晶 注意：这个接口需要开通测试环境和正式环境的ip白名单
     *
     * @param uid          中奖用户的uid
     * @param activityId   活动来源的id号
     * @param currencyType 货币类型
     * @param amount       发放白水晶的数量
     * @param appid        业务类型
     * @param seqId        由调用方传入的流水号，用来区别唯一的一次调用，多次调用传同一个seqId只会写一次记录
     * @param expand       扩展字段
     * @param uid
     * @param activityId
     * @param currencyType
     * @param amount
     * @param appid
     * @param seqId
     * @param expand
     * @return 0：超过时效，活动已过时；1：发放成功；2：当日发放的白水晶总量已达上限，发放失败；3：当日个人发放的白水晶总量已达上限，发放失败
     * ；
     * code：错误码；message：错误原因 错误码：
     * <ul>
     * <li>-1: 参数错误</li>
     * <li>-22: 账户不存在</li>
     * <li>-405: 订单序号已存在</li>
     * <li>-600: 该活动不存在</li>
     * <li>-601: 当天发放记录不存在</li>
     * </ul>
     */
    @Report
    public int issueCurrencyNew(long uid, long activityId, TCurrencyType currencyType, long amount, TAppId appid, String seqId, String expand) {
        Clock clock = new Clock();
        try {
            int result = getProxy().issueCurrencyNew(uid, activityId, currencyType, amount, appid, seqId, expand);
            log.info("issueCurrencyNew done@uid:{}, activityId:{},currencyType:{},amount:{},appid:{} seqId:{}, extend:{}, ret:{},clock:{}"
                    , uid, activityId, currencyType, amount, appid, seqId, expand, result
                    , clock.tag());
            return result;
        } catch (Exception e) {
            log.error("issueCurrencyNew error@uid:{}, activityId:{},currencyType:{},amount:{},appid:{} seqId:{}, extend:{}, ret:{},clock:{},e:{}"
                    , uid, activityId, currencyType, amount, appid, seqId, expand
                    , clock.tag(), e.getMessage(), e);
            int code = (e instanceof TServiceException) ? ((TServiceException) e).getCode() : 9999;

            final int code9999 = 9999;
            if (code == code9999 && e.getCause() instanceof TServiceException) {
                code = ((TServiceException) e.getCause()).getCode();
            }

            // 订单序号已存在当作成功
            return code == -405 ? 1 : code;
        }
    }

    /**
     * 获取主持周星榜
     *
     * @param propId 0：返回所有周星礼物榜单
     * @param type   0：主持榜 1：超级天团榜
     * @param year   2021
     * @param week   iso对应的周
     * @param size
     * @return
     */
    @Report
    public List<TCurrentWeekPropRank> queryWeekStarRanking(int propId, int year, int week, int type, int size) {
        Clock clock = new Clock();
        try {
            List<TCurrentWeekPropRank> weekPropRanks = getReadProxy().queryWeekStarRanking(propId, year, week, type, size);
            log.info("queryWeekStarRanking done@propId:{}, year:{},week:{},type:{},size:{},clock:{}"
                    , propId, year, week, type, size, clock.tag());
            return weekPropRanks;
        } catch (Exception e) {
            log.error("queryWeekStarRanking error@propId:{},year:{},week:{},type:{},size:{},clock:{},e:{}"
                    , propId, year, week, type, size, clock.tag(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 查找主播当前周星数据
     *
     * @param uid
     * @param type 0：主持榜 1：超级天团榜
     * @return
     */
    @Report
    public List<TDatingAnchorRank> queryAnchorWeekStarRankingList(long uid, int type) {
        Clock clock = new Clock();
        try {
            List<TDatingAnchorRank> weekPropRanks = getReadProxy().queryAnchorWeekStarRankingList(uid, type);
            log.info("queryAnchorWeekStarRankingList done@uid:{},type:{},clock:{}"
                    , uid, type, clock.tag());
            return weekPropRanks;
        } catch (Exception e) {
            log.error("queryAnchorWeekStarRankingList error@uid:{},type:{},clock:{},e:{}"
                    , uid, type, clock.tag(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取具体分组主持周星榜
     *
     * @param propId 0：返回所有周星礼物榜单
     * @param year   2021
     * @param week   iso对应的周
     * @param group
     * @param size   * @return
     */
    @Report
    public List<TCurrentWeekPropRank> queryWeekStarRankingByGroup(int propId, int year, int week, int group, int size) {
        Clock clock = new Clock();
        try {
            List<TCurrentWeekPropRank> weekPropRanks = getReadProxy().queryWeekStarRankingByGroup(propId, year, week, group, size);
            log.info("queryWeekStarRankingByGroup done@propId:{}, year:{},week:{},type:{},size:{},clock:{}"
                    , propId, year, week, group, size, clock.tag());
            return weekPropRanks;
        } catch (Exception e) {
            log.error("queryWeekStarRankingByGroup error@propId:{},year:{},week:{},type:{},size:{},clock:{},e:{}"
                    , propId, year, week, group, size, clock.tag(), e.getMessage(), e);
            return null;
        }
    }

    @Report
    public List<TDatingWeekGift> queryWeekStarConfig(long startTime, long endTime) {
        Clock clock = new Clock();
        try {
            List<TDatingWeekGift> tDatingWeekGifts = getReadProxy().getDatingWeekGift(startTime, endTime);
            log.info("queryWeekStarConfig done@startTime:{}, endTime:{},clock:{}"
                    , startTime, endTime, clock.tag());
            return tDatingWeekGifts;
        } catch (Exception e) {
            log.error("queryWeekStarConfig error@startTime:{}, endTime:{},clock:{},e:{}"
                    , startTime, endTime, clock.tag(), e.getMessage(), e);
            return null;
        }
    }

    @Report
    public TConsumeProductResult consumeProductNew(TConsumeProductRequest consumeProductRequest) {
        TConsumeProductResult tConsumeProductResult = null;
        try {
            tConsumeProductResult = getProxy().consumeProductNew(consumeProductRequest);
            if (tConsumeProductResult == null || tConsumeProductResult.getCode() != 0) {
                log.error("consumeProductNew error,req:{},rsp:{}", consumeProductRequest, tConsumeProductResult);
            }
            if (tConsumeProductResult == null) {
                log.info("consumeProductNew return null,Param:{}", consumeProductRequest.toString());
            }
        } catch (Exception e) {
            log.error("consumeProductNew error :" + e);
        }
        return tConsumeProductResult;
    }

    @Report
    public int offlineActivityProps(int appId, int propId, int reuseMoneyGun, String tag) throws TException {
        return getReadProxy().offlineActivityProps(appId, propId, reuseMoneyGun, tag);
    }
}
