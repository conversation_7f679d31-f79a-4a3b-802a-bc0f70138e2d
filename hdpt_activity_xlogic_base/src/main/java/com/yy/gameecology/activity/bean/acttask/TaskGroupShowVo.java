package com.yy.gameecology.activity.bean.acttask;

import lombok.Data;

import java.util.List;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-02-23 14:14
 **/
@Data
public class TaskGroupShowVo {
    /**
     * 分组编码
     */
    private String groupCode;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 任务组是否完成任务
     */
    private boolean complete;

    /**
     * 任务详情
     */
    private List<TaskShowVo> task;

    /**
     * 展示详细任务 true===展示 false===不展示
     */
    private boolean showDetails;

    /**
     * 展示排序
     */
    private int sort;


}
