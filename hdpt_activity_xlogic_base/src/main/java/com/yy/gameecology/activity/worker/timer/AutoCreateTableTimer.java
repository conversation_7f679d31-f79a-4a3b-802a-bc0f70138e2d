package com.yy.gameecology.activity.worker.timer;

import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.ScheduledExt;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.activity.service.CacheService;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.db.model.gameecology.AutoCreateTbConfig;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-01-17 11:42
 **/
@Component
public class AutoCreateTableTimer {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private GameecologyDao gameecologyDao;

    @Autowired
    private TimerSupport timerSupport;
    @Autowired
    private CacheService cacheService;

    /**
     * 自动创建表
     */
    @Scheduled(cron = "0 0/10 * * * ? ")
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @ScheduledExt(historyRun = false)
    @Report
    public void runAutoCreateTable() {
        List<ActivityInfoVo> activityInfoVos = hdztRankingThriftClient.queryEffectActInfos();
        if (CollectionUtils.isEmpty(activityInfoVos)) {
            log.warn("activityInfoVos is null");
            return;
        }

        timerSupport.work("runAutoCreateTable", 10, () -> {

            AutoCreateTbConfig where = new AutoCreateTbConfig();
            where.setState(1);
            List<AutoCreateTbConfig> configs = gameecologyDao.select(AutoCreateTbConfig.class, where);

            activityInfoVos.forEach(x -> {
                if (x.getStatus() != 1) {
                    return;
                }

                //活动通用表
                List<AutoCreateTbConfig> commonTables = configs.stream().filter(p -> p.getCmptId() == 0).toList();
                for (AutoCreateTbConfig config : commonTables) {
                    if (config.getConfigScript() == null) {
                        continue;
                    }
                    String sql = config.getConfigScript().replace("${actId}", Convert.toString(x.getActId()));
                    if (StringUtil.isEmpty(sql)) {
                        continue;
                    }
                    gameecologyDao.getJdbcTemplate().execute(sql);
                    log.info("runAutoCreateTable,actId:{},sql:{}", x.getActId(), sql);
                }

                //组件表
                List<AutoCreateTbConfig> cmptTables = configs.stream().filter(p -> p.getCmptId() > 0).toList();
                for (AutoCreateTbConfig config : cmptTables) {
                    var cmptConfig = cacheService.getHdzjComponentMap(x.getActId(), config.getCmptId());
                    if (MapUtils.isEmpty(cmptConfig)) {
                        continue;
                    }
                    if (config.getConfigScript() == null) {
                        continue;
                    }
                    String sql = config.getConfigScript().replace("${actId}", Convert.toString(x.getActId()));
                    if(StringUtil.isEmpty(sql)){
                        continue;
                    }
                    gameecologyDao.getJdbcTemplate().execute(sql);
                    log.info("runAutoCreateTable,actId:{},sql:{}", x.getActId(), sql);
                }
            });
        });

    }

}
