/**
 * SecuserinfoThriftClient.java / 2018年3月28日 下午5:34:53
 * <p>
 * Copyright (c) 2017, YY Inc. All Rights Reserved.
 * <p>
 * 郭立平[<EMAIL>]
 */

package com.yy.gameecology.activity.client.thrift;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.bean.RelateUidsVo;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.thrift.userinfo.*;
import com.yy.thrift.userinfo.userinfo_service.Iface;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 检查用户信息
 */
@Component
public class UserinfoThriftClient {

    private static final Logger log = LoggerFactory.getLogger(UserinfoThriftClient.class);

    @Reference(protocol = "nythrift", owner = "__", url = "${thrift.service-agent.url}", timeout = 3000, parameters = {"threads", "10"},  retries = 2,cluster = "failover")
    private Iface proxy = null;

    public static AuthorizeMsg getAuthorizeMsg() {
        AuthorizeMsg authorizeMsg = new AuthorizeMsg();
        authorizeMsg.setAuthKey(Const.APP_SECRET);
        authorizeMsg.setAuthUser(Const.APP_ID);

        HashMap<String, String> keyvalue = new HashMap<>(2);
        keyvalue.put("auth-type", "4");
        authorizeMsg.setKeyvalue(keyvalue);
        return authorizeMsg;
    }

    public static AuthorizeMsg getAuthorizeMsg(String appId, String secret) {
        AuthorizeMsg authorizeMsg = new AuthorizeMsg();
        authorizeMsg.setAuthKey(secret);
        authorizeMsg.setAuthUser(appId);

        HashMap<String, String> keyvalue = new HashMap<>(1);
        keyvalue.put("auth-type", "4");
        authorizeMsg.setKeyvalue(keyvalue);
        return authorizeMsg;
    }

    public Iface getProxy() {
        return proxy;
    }

    /**
     * 检查用户手机绑定情况
     *
     * @param uid    - 要检查的uid
     * @param strict - 严格模式
     * @return 明确情况返回明确值； strict=false 时系统自身错误也返回true
     */
    @Report
    public boolean checkUserMobile(long uid, boolean strict) {
        Clock clock = new Clock();
        try {
            BaseReq req = new BaseReq();
            req.setAuthmsg(UserinfoThriftClient.getAuthorizeMsg());
            req.setAppid(Const.APP_ID);
            req.setRequest(String.valueOf(uid));
            req.setReqtype(UserType.THRIFT_YYUID.getValue());
            BaseStrRes res = getProxy().lg_userinfo_sa_getMobileMask(req);
            int rescode = res.getRescode();

            if (rescode == ERRCODE.SUCCESS.getValue()) {
                log.info("checkUserMobile ok@true, uid:{}, ret:{}", uid, res);
                return true;
            } else if (rescode == ERRCODE.Not_Mobile.getValue() || rescode == ERRCODE.Not_Exist.getValue()) {
                log.info("checkUserMobile fail@false, uid:{}, ret:{}", uid, res);
                return false;
            } else {
                log.error("checkUserMobile wrong@{}, uid:{}, ret:{}", !strict, uid, res);
                return !strict;
            }
        } catch (Throwable t) {
            log.error("checkUserMobile exception@uid:{}, err:{} {}", uid, t.getMessage(), clock.tag(), t);
            return !strict;
        }
    }

    public String getMobileHash(long uid) {
        return getMobileMask(uid, "ha");
    }

    public String getMobileMask(long uid) {
        return getMobileMask(uid, null);
    }

    public String getMobileMask(long uid, String mobMaskType) {
        BaseReq req = new BaseReq();
        AuthorizeMsg authMsg = UserinfoThriftClient.getAuthorizeMsg();
        if (StringUtils.isNotEmpty(mobMaskType)) {
            authMsg.keyvalue.put("mobmask-type", mobMaskType);
        }
        req.setAuthmsg(authMsg);
        req.setAppid(Const.APP_ID);
        req.setRequest(String.valueOf(uid));
        req.setReqtype(UserType.THRIFT_YYUID.getValue());
        BaseStrRes res;
        try {
            res = getProxy().lg_userinfo_sa_getMobileMask(req);
        } catch (Exception e) {
            log.error("getMobileMask fail with uid:{} mobMaskType:{}", uid, mobMaskType, e);
            return null;
        }

        if (res == null || res.rescode != ERRCODE.SUCCESS.getValue()) {
            return null;
        }

        return res.result;
    }

    public String getIdHash(long uid) {
        return getIdMask(uid, "ha");
    }

    public String getIdMask(long uid) {
        return getIdMask(uid, null);
    }

    public String getIdMask(long uid, String idMaskType) {
        BaseReq req = new BaseReq();
        AuthorizeMsg authMsg = UserinfoThriftClient.getAuthorizeMsg();
        if (StringUtils.isNotEmpty(idMaskType)) {
            authMsg.keyvalue.put("masktype", idMaskType);
        }
        req.setAuthmsg(authMsg);
        req.setAppid(Const.APP_ID);
        req.setRequest(String.valueOf(uid));
        req.setReqtype(UserType.THRIFT_YYUID.getValue());

        NCertMaskInfoRes res;
        try {
            res = getProxy().lg_userinfo_sa_getCertMaskInfoByUid(req);
        } catch (Exception e) {
            log.error("getIdMask fail with uid:{} idMaskType:{}", uid, idMaskType, e);
            return null;
        }

        if (res == null || res.rescode != ERRCODE.SUCCESS.getValue()) {
            return null;
        }

        return res.MaskId;
    }

    /**
     * 填充用户头像
     */
    private void fillUserYylogo(Map<String, String> user) {
        String logo = Const.IMAGE.DEFAULT_USER_LOGO;
        String url = user.get("custom_logo");
        if (!StringUtil.isBlank(url)) {
            logo = url.trim();
        } else {
            long logoIndex = Convert.toLong(user.get("logo_index"), 0);
            if (logoIndex > 0) {
                final int index10100 = 10100, index10163 = 10163;
                if (!(logoIndex >= index10100 && logoIndex <= index10163)) {
                    logoIndex = 10001;
                }
                logo = "https://s1.yy.com/guild/header/" + logoIndex + ".jpg";
            }
        }
        user.put("yylogo", logo);

        // 移除这2个属性，保持和老的webdb client 一致
        user.remove("custom_logo");
        user.remove("logo_index");
    }

    /**
     * 填充用户字段
     *
     * <AUTHOR>
     * @date 2018年9月11日 上午11:17:04
     */
    private void fillValue4UserInfo(Map<String, String> user, List<String> userInfoColumns, StringList strings, int i) {
        String key = userInfoColumns.get(i);
        String value = strings.getStrList().get(i);
        user.put(key, value);
    }

    /**
     * 申请界面 https://shcp.yy.com/mvc/index  短信网关服务
     * 营销短信对接人：黄文超
     * sa对接人：周乐航
     */
    public int sendMarketSms(String seq, Long uid, String content) {
        if (SysEvHelper.isHistory()) {
            throw new RuntimeException("history not sendMarketSms ");
        }
        Clock clock = new Clock();
        String uidWhiteList = "," + Const.GEPM.getParamValue("prize_issue_client_send_sms_white_list", "50013181") + ",";
        //非生产环境，发送短信uid白名单不通过
        log.info("uidWhiteList:{}", uidWhiteList);
        if (!SysEvHelper.isDeploy()
                && !uidWhiteList.contains("," + uid + ",")) {
            log.warn("sendSms not in white list return,seq:{},uid:{},content:{}", seq, uid, content);
            return 0;
        }
        try {
            NSmsReq req = new NSmsReq();
            req.setAppid(Const.APP_ID);
            req.setAuthmsg(getAuthorizeMsg());
            req.setReqtype(UserType.THRIFT_YYUID.getValue());
            req.setRequest(Convert.toString(uid));
            req.setContent(content);
            BaseRes rsp = getProxy().lg_userinfo_sa_sendMarketSms(req);
            if (rsp != null && rsp.getRescode() == 4) {
                log.info("sendSms to uid cant find phone, {},{}", uid, rsp);
                return -1;
            }
            if (rsp == null || rsp.getRescode() != 0) {
                log.warn("sendSms error,req:{},rsp:{}", req, rsp);
                return -1;
            }
            log.info("sendSms done,seq:{},uid:{},content:{},ret:{},clock:{}", seq, uid, content, JSON.toJSONString(rsp), clock.tag());
            return rsp.getRescode();
        } catch (Exception e) {
            log.error("sendSms error,seq:{},uid:{},content:{},e:{},clock:{}", seq, uid, content, e.getMessage(), clock.tag(), e);
            return -1;
        }
    }

    public int sendMarketSms(String seq, Long uid, String content, String secret, String appid) {
        if (SysEvHelper.isHistory()) {
            throw new RuntimeException("history not sendMarketSms ");
        }
        Clock clock = new Clock();
        String uidWhiteList = "," + Const.GEPM.getParamValue("prize_issue_client_send_sms_white_list", "50013181") + ",";
        //非生产环境，发送短信uid白名单不通过
        if (!SysEvHelper.isDeploy()
                && !uidWhiteList.contains("," + uid + ",")) {
            log.warn("sendSms not in white list return,seq:{},uid:{},content:{}", seq, uid, content);
            return 0;
        }
        try {
            NSmsReq req = new NSmsReq();
            req.setAppid(appid);
            req.setAuthmsg(getAuthorizeMsg(appid, secret));
            req.setReqtype(UserType.THRIFT_YYUID.getValue());
            req.setRequest(Convert.toString(uid));
            req.setContent(content);
            BaseRes rsp = getProxy().lg_userinfo_sa_sendMarketSms(req);
            if (rsp != null && rsp.getRescode() == 4) {
                log.info("sendSms to uid cant find phone, {},{}", uid, rsp);
                return -1;
            }
            if (rsp == null || rsp.getRescode() != 0) {
                log.warn("sendSms error,req:{},rsp:{}", req, rsp);
                return -1;
            }
            log.info("sendSms done,seq:{},uid:{},content:{},ret:{},clock:{}", seq, uid, content, JSON.toJSONString(rsp), clock.tag());
            return rsp.getRescode();
        } catch (Exception e) {
            log.error("sendSms error,seq:{},uid:{},content:{},e:{},clock:{}", seq, uid, content, e.getMessage(), clock.tag(), e);
            return -1;
        }
    }

    public RelateUidsVo getRelateUids(Long uid) {
        if (uid == null || uid <= 0) {
            return RelateUidsVo.EMPTY;
        }
        try {
            BaseReq req = new BaseReq();

            req.setAppid(Const.APP_ID);
            req.setAuthmsg(getAuthorizeMsg());
            req.setReqtype(UserType.THRIFT_YYUID.getValue());
            req.setRequest(String.valueOf(uid));
            NRelateUidsRes res = proxy.lg_userinfo_sa_getRelateUids(req);
            if (res != null) {
                RelateUidsVo info = new RelateUidsVo();
                info.setRescode(res.getRescode());
                if (CollectionUtils.isNotEmpty(res.getIdrel_yyuids())) {
                    info.setIdRelUids(res.getIdrel_yyuids().stream().map(Long::parseLong).collect(Collectors.toSet()));
                }
                if (CollectionUtils.isNotEmpty(res.getMobrel_yyuids())) {
                    info.setMobRelUids(res.getMobrel_yyuids().stream().map(Long::parseLong).collect(Collectors.toSet()));
                }
                return info;
            } else {
                log.warn("getRelateUids bizError res is null, uid:{}", uid);
                return null;
            }

        } catch (Exception e) {
            log.warn("getRelateUids error uid:{} msg:{}", uid, e.getMessage(), e);
            return null;
        }
    }

    public RelateUidsVo getRelateUids(Long uid, long retry) {
        while (retry-- > 0) {
            try{
                RelateUidsVo result = this.getRelateUids(uid);
                if (result != null ) {
                    log.info("getRelateUids with retry ok, uid:{},retry:{}", uid,retry);
                    return result;
                } else {
                    log.error("getRelateUids error,uid:{},retry:{},rsp:{}", uid, retry, result);
                }
                if (retry > 0) {
                    Thread.sleep(500);
                }
            }catch (Exception e) {
                log.warn("getRelateUids exception,uid:{},msg:{}" ,uid,e.getMessage(), e);
            }

        }
        return null;
    }
}
