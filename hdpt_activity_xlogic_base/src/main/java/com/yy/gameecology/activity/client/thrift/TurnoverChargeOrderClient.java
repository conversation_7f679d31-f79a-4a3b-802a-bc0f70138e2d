
package com.yy.gameecology.activity.client.thrift;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.thrift.turnover_chargeorder.*;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class TurnoverChargeOrderClient {

    private static Logger log = LoggerFactory.getLogger(TurnoverChargeOrderClient.class);

    @Reference(protocol = "nythrift_compact", owner = "${turnoverService_client_s2sname}", registry = "consumer-reg", timeout = 3000, parameters = {"threads", "10"})
    public TChargeOrderService.Iface proxy = null;

    public TChargeOrderService.Iface getProxy() {
        return proxy;
    }

    /**
     * @param amount      金额(元)
     * @param payWay      TODO Zfb QrCode2===支付宝扫码(待支付网关新增)  ;  WeiXinQrCode===微信扫码
     * @param channelType 渠道
     * @param appid       2.交友 14.约战 36.宝贝
     * @param actionId    57.公会预选赛活动礼包
     * @param keyType     4.充值
     */
    @Report
    public TChargeResponseInfo payOrder(long uid, double amount, String userIp, TPayWay payWay, int channelType, TAppId appid, String expand, int actionId, TPayKeyType keyType) {

        Clock clock = new Clock();
        log.info("payOrder begin,uid:{},amount:{},userIp:{},payWay:{},channelType:{},appid:{},expand:{},actionId:{},keyType:{}", uid, amount, userIp, payWay, channelType, appid, expand, actionId, keyType);
        try {
            String returnUrl = StringUtil.EMPTY;
            String bankId = StringUtil.EMPTY;
            TChargeResponseInfo result = getProxy().payOrder(uid, amount, bankId, returnUrl, userIp, payWay, channelType, appid, expand, actionId, keyType);
            if (result == null || result.getCode() != 0) {
                log.error("payOrder error,uid:{},rsp:{}", uid, result);
            }
            log.info("payOrder ok,uid:{},amount:{},userIp:{},payWay:{},channelType:{},appid:{},expand:{},actionId:{},keyType:{},result:{},clock:{}"
                    , uid, amount, userIp, payWay, channelType, appid, expand, actionId, keyType, JSON.toJSONString(result), clock.tag());
            return result;
        } catch (Exception e) {
            log.error("payOrder error,uid:{},amount:{},userIp:{},payWay:{},channelType:{},appid:{},expand:{},actionId:{},keyType:{},clock:{},e:{}"
                    , uid, amount, userIp, payWay, channelType, appid, expand, actionId, keyType, clock.tag(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 退款
     */
    @Report
    public TChargeResponseInfo reversePayCharge(long uid, TAppId appid, long orderId, String description) {

        Clock clock = new Clock();
        log.info("reversePayCharge begin,uid:{},appid:{},orderId:{},description:{}", uid, appid, orderId, description);
        try {
            TChargeResponseInfo result = getProxy().reversePayCharge(uid, appid, orderId, description);
            if (result == null || result.getCode() != 0) {
                log.error("reversePayCharge error,uid:{},rsp:{}", uid, result);
            }
            log.info("reversePayCharge ok,uid:{},appid:{},orderId:{},description:{},result:{},clock:{}", uid, appid, orderId, description, JSON.toJSONString(result), clock.tag());

            return result;
        } catch (Exception e) {
            log.error("reversePayCharge error,uid:{},appid:{},orderId:{},description:{},clock:{},e:{}", uid, appid, orderId, description, clock.tag(), e.getMessage(), e);
            return null;
        }
    }
}
