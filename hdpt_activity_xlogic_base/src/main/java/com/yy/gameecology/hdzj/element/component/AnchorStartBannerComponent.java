package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSortedSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.service.BroadCastHelpService;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.AnchorStartBannerComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * @Author: CXZ
 * @Desciption: 开播横幅
 * @Date: 2021/4/15 16:46
 * @Modified:
 */
@Component
public class AnchorStartBannerComponent extends BaseActComponent<AnchorStartBannerComponentAttr> {
    @Autowired
    private CommonService commonService;
    @Autowired
    private BroadCastHelpService broadCastHelpService;
    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;


    private static final String ONLY_EXECUTE_KEY = "only_execute";

    private static final String ANCHOR_SHOW_DAY = "%s_anchor_start_show_%s";

    private static final String START_SHOW_BANNER_TASK = "anchor_start_show_banner_task";

    private static final Map<BusiId, String> BUSI_ID_SHOW_NAME_MAP =
            ImmutableMap.of(BusiId.YUE_ZHAN, "约战", BusiId.GAME_BABY, "宝贝", BusiId.MAKE_FRIEND, "交友", BusiId.PEI_WAN, "陪玩");


    @Override
    public Long getComponentId() {
        return ComponentId.ANCHOR_START_SHOW_BANNER;
    }

    /**
     * 监听榜单阶段结束事件，发放主播开播横幅奖励
     *
     * @param event
     * @param attr
     */
    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = true)
    public void releaseAnchorStartShowBannerByPhaseEnd(PhaseTimeEnd event, AnchorStartBannerComponentAttr attr) {
        if (event.getRankId() != attr.getRankId() || event.getPhaseId() != attr.getPhaseId()) {
            if (CollectionUtils.isEmpty(attr.getRankPhasePairs())) {
                return;
            }

            boolean match = attr.getRankPhasePairs().stream()
                    .anyMatch(pair -> pair.getRankId() == event.getRankId() && pair.getPhaseId() == event.getPhaseId());
            if (!match) {
                return;
            }
        }

        long actId = attr.getActId();
        final long rankId = event.getRankId(), phaseId = event.getPhaseId();
        Date endDate = DateUtil.getDate(event.getEndTime());
        String timeCode = TimeKeyHelper.getTimeCode(event.getTimeKey(), endDate);

        String groupCode = redisConfigManager.getGroupCode(event.getActId());
        //防止重复执行
        final String key = ONLY_EXECUTE_KEY + ":" + rankId + "_" + phaseId + "_" + timeCode;
        if (!actRedisDao.setNX(groupCode, makeKey(attr, key), DateUtil.getNowYyyyMMddHHmmss())) {
            return;
        }

        Map<Integer, Integer> rankAwardDayMap = attr.getRankAwardDayMap();
        //获取奖励的最大排名，一次获取所有需要奖励的名单
        int maxRank = rankAwardDayMap.entrySet().stream().filter(entry -> entry.getValue() > 0).map(Map.Entry::getKey).mapToInt(Integer::new).max().getAsInt();
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(actId, event.getRankId(), event.getPhaseId(), timeCode, maxRank, Maps.newHashMap());

        //奖励的开始时间是从阶段结束的下一秒
        long awardStartTime = endDate.getTime() + 1000L;

        Date startDate = DateUtil.getDate(attr.getStartDate());
        if (startDate != null) {
            awardStartTime = startDate.getTime();
        }
        for (Rank rank : ranks) {
            //奖励天数
            int awardDay = rankAwardDayMap.getOrDefault(rank.getRank(), 0);
            //不是奖励排名
            if (awardDay <= 0) {
                return;
            }

            List<EnrollmentInfo> enrollmentInfos = hdztRankingThriftClient
                    .queryEnrollmentInfoNocache(actId, Convert.toLong(RoleType.ANCHOR.getValue()), Collections.singletonList(rank.getMember()));
            //报名信息不唯一或者找不到
            if (enrollmentInfos.size() != 1) {
                log.error("releaseAnchorStartShowBannerByPhaseEnd ignore enrollmentInfos size error@uid:{} size:{} attr:{}"
                        , rank.getMember(), enrollmentInfos.size(), JSON.toJSONString(attr));
                continue;
            }
            //获取要奖励的文案模板
            String contest = attr.getRankAwardContextMap().getOrDefault(rank.getRank(), attr.getDefaultAwardTip());
            release(enrollmentInfos.get(0), awardDay, awardStartTime, contest, attr);
        }
        log.info("releaseAnchorStartShowBannerByPhaseEnd done -> event:{}, attr:{}", event, attr);
    }

    /**
     * 承接主播的开播事件
     *
     * @param anchorUid
     * @param busiId
     */
    public void notify(long anchorUid, BusiId busiId) {
        getActivityIds().stream()
                .map(this::getAllComponentAttrs).flatMap(Collection::stream)
                .filter(attr -> isNeedExecute(attr, commonService.getNow(attr.getActId())))
                .forEach(attr -> showStartBanner(attr, anchorUid, busiId));
        log.info("notify done@anchorUid:{} busiId:{}", anchorUid, busiId);
    }

    /**
     * 约战宝贝没有上麦通知，需要用轮询和时间段划分的方式广播
     */
    @Scheduled(cron = "0 */1 * * * ?")
    @NeedRecycle(author = "chenxiazhuan", notRecycle = true)
    public void notifyScheduled() {
        getActivityIds().stream()
                .map(this::getAllComponentAttrs).flatMap(Collection::stream)
                .forEach(attr -> notifyScheduled(BusiId.YUE_ZHAN, attr));
        getActivityIds().stream()
                .map(this::getAllComponentAttrs).flatMap(Collection::stream)
                .forEach(attr -> notifyScheduled(BusiId.GAME_BABY, attr));
        log.info("notifyScheduled done");
    }


    /**
     * 发放横幅奖励，保存在redis，等到主播上线广播
     * 暂时不做时间累加逻辑，只允许单次发放，下次可以优化
     *
     * @param enrollmentInfo
     * @param awardDay
     * @param startTime
     * @param contest
     * @param attr
     */
    private void release(EnrollmentInfo enrollmentInfo, int awardDay, final long startTime, String contest, AnchorStartBannerComponentAttr attr) {
        log.info("release with actId:{}, inx:{}, member:{}, startTime:{}, awardDay:{}", attr.getActId(), attr.getCmptUseInx(), enrollmentInfo.getMemberId(), startTime, awardDay);
        String groupCode = redisConfigManager.getGroupCode(enrollmentInfo.getActId());
        String key = makeKey(attr, START_SHOW_BANNER_TASK);
        long anchorUid = Convert.toLong(enrollmentInfo.getMemberId());
        String value = actRedisDao.hget(groupCode, key, String.valueOf(anchorUid));
        long currentStartTime = startTime;
        if (StringUtils.isNotEmpty(value)) {
            AnchorStartShowInfo anchorStartShowInfo = JSON.parseObject(value, AnchorStartShowInfo.class);
            currentStartTime = anchorStartShowInfo.getEndTime();
            log.info("get prev anchorStartShowInfo with endTime:{}", currentStartTime);
        }

        long endTime = currentStartTime + awardDay * DateUtil.DAY_SEC * 1000;
        String context = replaceText(contest, enrollmentInfo, attr.getRoleShowNameMap());
        AnchorStartShowInfo anchorStartShowInfo = new AnchorStartShowInfo();
        anchorStartShowInfo.setAnchorUid(anchorUid);
        anchorStartShowInfo.setBusiId(enrollmentInfo.getRoleBusiId());
        anchorStartShowInfo.setContest(context);
        anchorStartShowInfo.setStartTime(startTime);
        anchorStartShowInfo.setEndTime(endTime);

        actRedisDao.hset(groupCode, key, String.valueOf(anchorUid), JSON.toJSONString(anchorStartShowInfo));
    }

    /**
     * 奖励文案替换
     *
     * @param context
     * @param enrollmentInfo
     * @param roleShowName
     * @return
     */
    private String replaceText(String context, EnrollmentInfo enrollmentInfo, Map<Long, String> roleShowName) {
        long uid = Long.parseLong(enrollmentInfo.getMemberId());
        BusiId busiId = BusiId.findByValue((int) enrollmentInfo.getRoleBusiId());

        UserBaseInfo getUserBaseInfo = broadCastHelpService.getUserBaseInfo(uid, busiId);
        String roleName = roleShowName.getOrDefault(enrollmentInfo.getDestRoleId(), "主播");
        String name = broadCastHelpService.adjustString(getUserBaseInfo.getNick());
        context = context.replace("##uid##", enrollmentInfo.getMemberId());
        context = context.replace("##asid##", enrollmentInfo.getSignAsid() + "");
        context = context.replace("##name##", name);
        context = context.replace("##busiName##", BUSI_ID_SHOW_NAME_MAP.getOrDefault(busiId, ""));
        context = context.replace("##roleName##", roleName);
        return context;
    }

    /**
     * 从redis获取数据，定时器调用是否会太频繁，后面 可以优化成用jvm缓存，定时更新，发放更新
     *
     * @param uid
     * @param attr
     * @return
     */
    private AnchorStartShowInfo getAnchorShowAward(long uid, AnchorStartBannerComponentAttr attr) {
        String key = makeKey(attr, START_SHOW_BANNER_TASK);
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String infoString = actRedisDao.hget(groupCode, key, String.valueOf(uid));
        if (StringUtils.isBlank(infoString)) {
            return null;
        }
        return JSON.parseObject(infoString, AnchorStartShowInfo.class);
    }


    private List<AnchorStartShowInfo> getAnchorShowAwards(AnchorStartBannerComponentAttr attr) {

        String key = makeKey(attr, START_SHOW_BANNER_TASK);
        String goupCode = redisConfigManager.getGroupCode(attr.getActId());
        Map<Object, Object> allAnchorBannerInfos = actRedisDao.hGetAll(goupCode, key);
        if (CollectionUtils.isEmpty(allAnchorBannerInfos)) {
            return Lists.newArrayList();
        }
        return allAnchorBannerInfos.values().stream()
                .map(info -> JSON.parseObject(info.toString(), AnchorStartShowInfo.class))
                .collect(Collectors.toList());
    }

    /**
     * 广播主播的开播横幅
     *
     * @param attr
     * @param anchorUid
     */
    public void showStartBanner(AnchorStartBannerComponentAttr attr, long anchorUid, BusiId busiId) {
        long actId = attr.getActId();
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        AnchorStartShowInfo anchorStarShowInfo = getAnchorShowAward(anchorUid, attr);

        if (anchorStarShowInfo == null) {
            log.warn("showStartBanner ignore@not find anchor award banner,anchorUid:{}, busiId:{} attr:{}", anchorUid, busiId, JSON.toJSONString(attr));
            return;
        }
        Date now = commonService.getNow(actId);
        if (!isNeedExecute(busiId, anchorStarShowInfo, now)) {
            log.warn("showStartBanner ignore@anchor award banner overdue,anchorUid:{},anchorStarShowInfo:{},now:{}",
                    anchorUid, JSON.toJSONString(anchorStarShowInfo), DateUtil.format(now, DateUtil.DEFAULT_PATTERN));
            return;
        }

        //每日限制开播
        String day = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        //加busiid只是为了方便通过key观察
        String dayRecordKey = makeKey(attr, String.format(ANCHOR_SHOW_DAY, busiId, day));
        int dayLimitCount = attr.getDayLimitCount();
        List<Long> result = actRedisDao.hIncrWithLimit(groupCode, dayRecordKey, String.valueOf(anchorUid), 1, dayLimitCount);

        if (result.get(0) != 1) {
            log.warn("showStartBanner ignore@daily limit anchor:{},dayLimitCount:{}", anchorUid, dayLimitCount);
            return;
        }
        GameecologyActivity.Act202011_StartShowBanner.Builder startShow = GameecologyActivity.Act202011_StartShowBanner.newBuilder()
                .setAnchor(broadCastHelpService.getAnchorInfo(actId, anchorUid, true, busiId))
                .setActId(actId)
                .setGroup(anchorStarShowInfo.getContest());

        GameecologyActivity.GameEcologyMsg startShowBanner = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.kAct202011_StartShowBanner_VALUE)
                .setAct202011StartShowBanner(startShow).build();

        broadCastHelpService.broadcast(actId, busiId, attr.getRankBorType(), 0L, 0L, startShowBanner);
        log.info("showStartBanner done@anchorUid:{},context:{} attr:{}", anchorUid, anchorStarShowInfo.getContest(), JSON.toJSONString(attr));

    }

    /**
     * 配置级别是否需要执行，减少redis查询
     *
     * @param attr
     * @return
     */
    private boolean isNeedExecute(AnchorStartBannerComponentAttr attr, Date now) {
        Date startDate = DateUtil.getDate(attr.getStartDate(), DateUtil.DEFAULT_PATTERN);
        Date finallyDate = DateUtil.getDate(attr.getFinallyDate(), DateUtil.DEFAULT_PATTERN);
        return startDate.getTime() <= now.getTime() && finallyDate.getTime() > now.getTime();
    }


    /**
     * 判断条件多个地方用到，提取出来方便管理
     *
     * @param busiId
     * @param showTask
     * @param now
     * @return
     */
    private boolean isNeedExecute(BusiId busiId, AnchorStartShowInfo showTask, Date now) {
        return showTask.getBusiId() == busiId.getValue() && showTask.getStartTime() <= now.getTime() && showTask.getEndTime() > now.getTime();
    }

    /**
     * 轮询需要广播的主播是否在麦
     *
     * @param busiId
     * @param attr
     */
    private void notifyScheduled(BusiId busiId, AnchorStartBannerComponentAttr attr) {
        long actId = attr.getActId();
        Date now = commonService.getNow(actId);
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        if (!isNeedExecute(attr, now)) {
            return;
        }
        if (CollectionUtils.isEmpty(attr.getBroStartHours())) {
            return;
        }

        String lockKey = makeKey(attr, busiId + "_lock");
        timerSupport.work(lockKey, 30, () -> {
            int hour = DateUtil.getHours(now);
            ImmutableSortedSet<Integer> broStartHours = ImmutableSortedSet.copyOf(attr.getBroStartHours());
            Integer startHour = broStartHours.floor(hour);
            if (startHour == null) {
                log.error("notifyScheduled ignore@BroStartHours config error hour:{} config:{}", hour, JSON.toJSONString(attr));
                return;
            }
            List<AnchorStartShowInfo> anchorStartShowInfos = getAnchorShowAwards(attr);
            if (CollectionUtils.isEmpty(anchorStartShowInfos)) {
                log.warn("notifyScheduled ignore@allAnchorBannerInfos is null config:{}", JSON.toJSONString(attr));
                return;
            }

            //获取需要广播开播横幅的主播
            List<Long> anchorUids = anchorStartShowInfos.stream()
                    .filter(anchorInfo -> isNeedExecute(busiId, anchorInfo, now))
                    .map(AnchorStartShowInfo::getAnchorUid)
                    .collect(Collectors.toList());

            String day = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
            String dayHourRecordKey = makeKey(attr, String.format(ANCHOR_SHOW_DAY, busiId, day));

            for (Long anchorUid : anchorUids) {
                //本时段是否广播过
                String anchorHour = anchorUid + "_" + startHour;
                if (StringUtils.isNotBlank(actRedisDao.hget(groupCode, dayHourRecordKey, anchorHour))) {
                    log.warn("notifyScheduled ignore@this period has already been implemented. anchor:{} startHour:{} config:{} ", anchorHour, startHour, JSON.toJSONString(attr));
                    continue;
                }
                //检查需要广播的主播是否在麦上
                UserCurrentChannel userCurrentChannel = commonService.getNoCacheUserCurrentChannel(anchorUid);
                if (userCurrentChannel == null) {
                    log.warn("notifyScheduled ignore@not find anchor, anchor:{} now:{} config:{} ", anchorHour, now.toString(), JSON.toJSONString(attr));
                    continue;
                }
                //判断主播是否在麦上，宝贝要是首麦
                List<Long> channelMicOrderList = commonService.getMicOrderList(userCurrentChannel.getTopsid(), userCurrentChannel.getSubsid());
                final boolean match = !channelMicOrderList.contains(anchorUid) || (busiId == BusiId.GAME_BABY && !anchorUid.equals(channelMicOrderList.get(0)));
                if (match) {
                    log.warn("notifyScheduled ignore@anchor not in channel, anchor:{} now:{} sid:{} ssid:{} config:{} ",
                            anchorHour, now.toString(), userCurrentChannel.getTopsid(), userCurrentChannel.getSubsid(), JSON.toJSONString(attr));
                    continue;
                }
                if (!actRedisDao.hsetnx(groupCode, dayHourRecordKey, anchorHour, now.toString())) {
                    log.warn("notifyScheduled ignore@this period has already been implemented2. anchor:{} startHour:{} config:{} "
                            , anchorHour, startHour, JSON.toJSONString(attr));
                    continue;
                }
                showStartBanner(attr, anchorUid, busiId);
            }
        });
    }

    public static class AnchorStartShowInfo {
        private long anchorUid;
        private long busiId;
        private long startTime;
        private long endTime;
        private String contest;

        public long getAnchorUid() {
            return anchorUid;
        }

        public void setAnchorUid(long anchorUid) {
            this.anchorUid = anchorUid;
        }

        public long getBusiId() {
            return busiId;
        }

        public void setBusiId(long busiId) {
            this.busiId = busiId;
        }

        public long getStartTime() {
            return startTime;
        }

        public void setStartTime(long startTime) {
            this.startTime = startTime;
        }

        public long getEndTime() {
            return endTime;
        }

        public void setEndTime(long endTime) {
            this.endTime = endTime;
        }

        public String getContest() {
            return contest;
        }

        public void setContest(String contest) {
            this.contest = contest;
        }
    }


}
