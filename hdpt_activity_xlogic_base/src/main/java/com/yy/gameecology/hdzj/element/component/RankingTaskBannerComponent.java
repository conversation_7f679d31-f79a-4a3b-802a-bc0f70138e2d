package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.event.*;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.client.thrift.TurnoverFamilyThriftClient;
import com.yy.gameecology.activity.service.EnrollmentNewService;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.MemberInfoService;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.consts.BigDataScoreType;
import com.yy.gameecology.common.consts.BroadcastType;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.FstAppBroadcastType;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.BroadcastConfig;
import com.yy.gameecology.hdzj.bean.TaskBroadcastConfig;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.BlackListComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.RankingTaskBannerComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvagConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvgaTextConfig;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import com.yy.thrift.hdztranking.RoleType;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.HtmlUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 霸屏横幅、高光横幅，同时支持pc和app
 * 后续废除 public static final long SUPPER_APP_BANNER = 2049L、    public static final long RANKING_SCORE_BANNER = 2028; 统一使用这个组件
 */
@Component
@RestController
@RequestMapping("/cmpt/RankingTaskBannerComponent")
public class RankingTaskBannerComponent extends BaseActComponent<RankingTaskBannerComponentAttr> {

    private static final int INVOKE_APP = 1;
    private static final int INVOKE_PC = 2;
    private static final int INVOKE_BOTH = 3;


    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private BlackListComponent blackListComponent;

    @Autowired
    private MemberInfoService memberInfoService;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private EnrollmentNewService enrollmentNewService;


    @Autowired
    private TurnoverFamilyThriftClient turnoverFamilyThriftClient;


    @Override
    public Long getComponentId() {
        return ComponentId.RANKING_TASK_BANNER;
    }

    /**
     * 测试环境提供http接口 广播测试
     */
    @RequestMapping("/testBroAppBanner")
    public Response<String> testBroAppBanner(long actId, long index, long level, long rankId, long phaseId, String member, String subChannel, String subChannelRoleId) {
        if (SysEvHelper.isDeploy()) {
            if (!commonService.isGrey(actId)) {
                return Response.success("测试接口线上不执行");
            }
            if (!hdztRankingThriftClient.checkWhiteList(actId, RoleType.HALL, subChannel)) {
                return Response.success("测试成员不在白名单");
            }
        }

        TaskProgressChanged event = new TaskProgressChanged();
        event.setActId(actId);
        event.setRankId(rankId);
        event.setPhaseId(phaseId);
        event.setMember(member);
        event.setCurrTaskIndex(level);
        if (StringUtil.isNotBlank(subChannel)) {
            Map<Long, String> actor = Maps.newHashMap();
            actor.put(Convert.toLong(subChannelRoleId), subChannel);
            event.setActors(actor);
        }

        RankingTaskBannerComponentAttr attr = getComponentAttr(actId, index);

        onTaskChangeEvent(event, attr);

        return Response.success("调用成功");
    }

    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = false)
    public void onTaskChangeEvent(TaskProgressChanged event, RankingTaskBannerComponentAttr attr) {
        if(attr.getBroPlatform()==INVOKE_APP || attr.getBroPlatform()==INVOKE_BOTH){
            invokeOnTaskChangeEvent(event, attr, true);
        }

        if(attr.getBroPlatform()==INVOKE_PC || attr.getBroPlatform()==INVOKE_BOTH) {
            pcInvokeOnTaskChangeEvent(event, attr, true);
        }
    }





    public void invokeOnTaskChangeEvent(TaskProgressChanged event, RankingTaskBannerComponentAttr attr, boolean checkRankId) {
        long rankId = event.getRankId(), phaseId = event.getPhaseId();
        if (!attr.isMyDuty(rankId, phaseId) && checkRankId) {
            return;
        }
        log.info("processSupperAppBanner start seq:{}, member:{}, event:{}", event.getSeq(), event.getMember(), JSON.toJSONString(event));

        if (attr.getEffectsType() == 2) {
            broMp42App(event, attr, Maps.newHashMap());
            return;
        }

        String member = event.getMember();
        String subChannel = getSubChannel(event, attr);

        long level = 0;

        //找不到横幅配置，则不广播
        Map<Long, BannerConfig> level2svgaConfig = attr.getLevelBanner();
        if (MapUtils.isEmpty(level2svgaConfig)) {
            return;
        }

        for (long taskIndex = event.getStartTaskIndex() + 1; taskIndex <= event.getCurrTaskIndex(); taskIndex++) {
            if (level2svgaConfig.containsKey(taskIndex)) {
                level = taskIndex;
            }
        }

        if (level == 0) {
            log.warn("level2svgaConfig is null or level is invalid, level:{}", level);
            return;
        }

        invokeBroBanner(attr, event.getSeq(), level, member, subChannel, Maps.newHashMap());

    }

    /**
     * 可给外部调用触发霸屏高光
     */
    public void invokeBroBanner(RankingTaskBannerComponentAttr attr, String seq, long level, String member, String subChannel, Map<String, String> dynamicReplace) {
        Map<Long, BannerConfig> level2svgaConfig = attr.getLevelBanner();
        BannerConfig bannerConfig = level2svgaConfig.get(level);
        BannerSvagConfig svagConfig = attr.getBannerSvag().get(bannerConfig.getSvgaConfigCode());
        int roleId = attr.getRoleId();
        MemberInfo memberInfo = memberInfoService.getMemberInfo(Convert.toLong(attr.getBusiId()), RoleType.findByValue(roleId), member);


        AppBannerSvgaConfig2 broSvgaConfig = new AppBannerSvgaConfig2();
        //svga内嵌文字
        List<Map<String, AppBannerSvgaText>> broContentLayers = getSvagTextConfig(attr, svagConfig, level, roleId, member, memberInfo, dynamicReplace);
        broSvgaConfig.setContentLayers(broContentLayers);
        //svga内嵌图片
        List<Map<String, String>> broImgLayers = getSvgaImageConfig(bannerConfig.getSvgaConfigCode(), attr, svagConfig, memberInfo, level);
        broSvgaConfig.setImgLayers(broImgLayers);

        broSvgaConfig.setJump(bannerConfig.getJump());
        broSvgaConfig.setHeight(bannerConfig.getHeight());
        broSvgaConfig.setDuration(bannerConfig.getDuration());
        broSvgaConfig.setLoops(bannerConfig.getLoops());

        AppBannerLayout layout = new AppBannerLayout();
        layout.setType(bannerConfig.getLayoutType());
        if (StringUtil.isNotBlank(bannerConfig.getLayoutMargin())) {
            layout.setMargin(JSON.parseObject(bannerConfig.getLayoutMargin(), new TypeReference<Map<String, List<Integer>>>() {
            }));
        }
        broSvgaConfig.setLayout(layout);

        broSvgaConfig.setLevel(bannerConfig.getLevel());
        broSvgaConfig.setWhRatio(bannerConfig.getWhRatio());
        broSvgaConfig.setClickLayerName(svagConfig.getClickLayerName());
        broSvgaConfig.setSvgaURL(svagConfig.getSvgaURL());
        broSvgaConfig.setJumpSvgaURL(svagConfig.getJumpSvgaURL());
        broSvgaConfig.setMiniURL(svagConfig.getMiniURL());
        broSvgaConfig.setJumpMiniURL(svagConfig.getJumpMiniURL());

        boolean inBlackList = inBlackList(attr, subChannel, member);
        //广播范围，如在黑名单内只广播本频道
        int broType = inBlackList ? 1 : attr.getLevel2BroType().get(level);
        UserCurrentChannel currentChannel = getCurrenChannel(subChannel);
        checkBroData(attr.getActId(), broType, currentChannel);
        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), seq, attr.getLevel2business().get(level),
                broType, currentChannel.getTopsid(), currentChannel.getSubsid(), "",
                Lists.newArrayList());
//        Map<String, Map<String, List<String>>> ignoreVersions = ImmutableMap.of("yomi", ImmutableMap.of("iOS", Collections.singletonList("~1.22.0"), "android", Collections.singletonList("~1.22.15")));
//        appBannerEvent.setIgnoreVersions(ignoreVersions);
        setUidList(roleId, member, appBannerEvent);
        appBannerEvent.setContentType(bannerConfig.getContentType());
        appBannerEvent.setAppId(getTurnoverAppId(attr.getBusiId()));
        appBannerEvent.setSvgaConfig(broSvgaConfig);
        if (attr.getExcludeDanmaku() == 1) {
            kafkaService.sendAppBannerKafkaExcludeDanmuku(appBannerEvent);
        } else {
            kafkaService.sendAppBannerKafka(appBannerEvent);
        }
        log.info("processSupperAppBanner app done seq:{}, member:{} event:{}", seq, member, JSON.toJSONString(appBannerEvent));
    }

    private void broMp42App(TaskProgressChanged event, RankingTaskBannerComponentAttr attr, Map<String, String> dynamicReplace) {
        String member = event.getMember();
        String subChannel = getSubChannel(event, attr);

        long level = 0;

        //找不到横幅配置，则不广播
        Map<Long, BannerConfig> level2svgaConfig = attr.getLevelBanner();
        if (MapUtils.isEmpty(level2svgaConfig)) {
            return;
        }

        for (long taskIndex = event.getStartTaskIndex() + 1; taskIndex <= event.getCurrTaskIndex(); taskIndex++) {
            if (level2svgaConfig.containsKey(taskIndex)) {
                level = taskIndex;
            }
        }

        if (level == 0) {
            log.warn("broMp42App is null or level is invalid, level:{}", level);
            return;
        }

        BannerConfig bannerConfig = level2svgaConfig.get(level);
        //获取mp4 url
        BannerSvagConfig svagConfig = attr.getBannerSvag().get(bannerConfig.getSvgaConfigCode());
        int roleId = attr.getRoleId();
        MemberInfo memberInfo = memberInfoService.getMemberInfo(Convert.toLong(attr.getBusiId()), RoleType.findByValue(roleId), member);

        List<Map<String, String>> layerKeys = new ArrayList<>();
        List<Map<String, AppBannerSvgaText>> broContentLayers = getSvagTextConfig(attr, svagConfig, level, roleId, member, memberInfo, dynamicReplace);
        for (Map<String, AppBannerSvgaText> broContentLayer : broContentLayers) {
            for (String key : broContentLayer.keySet()) {
                Map<String, String> map = new HashMap<>();
                map.put(key, broContentLayer.get(key).getText());
                layerKeys.add(map);
            }
        }


        //svga内嵌图片
        List<Map<String, String>> broImgLayers = getMp4ImageConfig(bannerConfig.getSvgaConfigCode(), attr, svagConfig, memberInfo, level);
        for (Map<String, String> broImgLayer : broImgLayers) {
            for (String key : broImgLayer.keySet()) {
                Map<String, String> map = new HashMap<>();
                map.put(key, broImgLayer.get(key));
                layerKeys.add(map);
            }
        }

        AppBannerMp4Config mp4Config = new AppBannerMp4Config();
        mp4Config.setUrl(svagConfig.getSvgaURL());
        mp4Config.setLevel(bannerConfig.getLevel());
        mp4Config.setLayerExtKeyValues(layerKeys);


        boolean inBlackList = inBlackList(attr, subChannel, member);
        //广播范围，如在黑名单内只广播本频道
        int broType = inBlackList ? 1 : attr.getLevel2BroType().get(level);
        UserCurrentChannel currentChannel = getCurrenChannel(subChannel);
        checkBroData(attr.getActId(), broType, currentChannel);
        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), event.getSeq(), attr.getLevel2business().get(level),
                broType, currentChannel.getTopsid(), currentChannel.getSubsid(), "",
                Lists.newArrayList());

        setUidList(roleId, member, appBannerEvent);
        appBannerEvent.setContentType(bannerConfig.getContentType());
        appBannerEvent.setAppId(getTurnoverAppId(attr.getBusiId()));
        appBannerEvent.setMp4Config(mp4Config);
        if (attr.getExcludeDanmaku() == 1) {
            kafkaService.sendAppBannerKafkaExcludeDanmuku(appBannerEvent);
        } else {
            kafkaService.sendAppBannerKafka(appBannerEvent);
        }
        log.info("broMp42App app done seq:{}, member:{} event:{}", event.getSeq(), member, JSON.toJSONString(appBannerEvent));

    }


    /**
     * svga内嵌纯图片内容配置
     */
    private List<Map<String, String>> getSvgaImageConfig(String svgaConfigCode, RankingTaskBannerComponentAttr attr, BannerSvagConfig svagConfig, MemberInfo memberInfo, long level) {
        List<Map<String, String>> broImgLayers = Lists.newArrayList();
        //广播图片key替换
        if (MapUtils.isEmpty(attr.getSvgaImgLayers()) || !attr.getSvgaImgLayers().containsKey(svgaConfigCode)) {
            return broImgLayers;
        }
        Map<String, String> imgDynamicMap = new HashMap<>();
        if(attr.getLevelImgDynamicValue().containsKey(level)) {
            imgDynamicMap = attr.getLevelImgDynamicValue().get(level);
        }
        Map<String, String> imageMap = attr.getSvgaImgLayers().get(svgaConfigCode);
        for (String imageKey : imageMap.keySet()) {
            Map<String, String> broImgLayer = Maps.newHashMap();
            String image = imageMap.get(imageKey);
            String value = replaceImage(image, memberInfo);
            if(imgDynamicMap.containsKey(image)) {
                value = imgDynamicMap.get(image);
            }
            broImgLayer.put(imageKey, value);
            broImgLayers.add(broImgLayer);
        }

        return broImgLayers;
    }

    /**
     * svga内嵌纯图片内容配置
     */
    private List<Map<String, String>> getMp4ImageConfig(String svgaConfigCode, RankingTaskBannerComponentAttr attr, BannerSvagConfig svagConfig, MemberInfo memberInfo, long level) {
        List<Map<String, String>> broImgLayers = Lists.newArrayList();
        //广播图片key替换
        if (MapUtils.isEmpty(attr.getSvgaImgLayers()) || !attr.getSvgaImgLayers().containsKey(svgaConfigCode)) {
            return broImgLayers;
        }

        Map<String, String> imageMap = attr.getSvgaImgLayers().get(svgaConfigCode);
        for (String imageKey : imageMap.keySet()) {
            Map<String, String> broImgLayer = Maps.newHashMap();
            String image = imageMap.get(imageKey);
            String value = replaceImage(image, memberInfo);
            if (attr.getLevelTextDynamicValue() != null && attr.getLevelImgDynamicValue().containsKey(level)) {
                Map<String, String> replaceValue = attr.getLevelImgDynamicValue().get(level);
                for (String key : replaceValue.keySet()) {
                    value = value.replace(key, replaceValue.get(key));
                }
            }
            broImgLayer.put(imageKey, value);
            broImgLayers.add(broImgLayer);
        }

        return broImgLayers;
    }

    /**
     * svga 内嵌文字配置
     */
    private List<Map<String, AppBannerSvgaText>> getSvagTextConfig(RankingTaskBannerComponentAttr attr, BannerSvagConfig svagConfig, long level, long roleId, String member, MemberInfo memberInfo, Map<String, String> dynamicReplace) {
        List<Map<String, AppBannerSvgaText>> broContentLayers = Lists.newArrayList();
        String contentLayerCodes = svagConfig.getContentLayerCodes();
        if (StringUtil.isNotBlank(contentLayerCodes)) {
            String[] contentLayerCodeArr = contentLayerCodes.split(",");
            for (String contentLayerCode : contentLayerCodeArr) {
                Map<String, AppBannerSvgaText> broSvgaTextLayer = Maps.newHashMap();
                BannerSvgaTextConfig textConfig = attr.getSvgaText().get(contentLayerCode);
                if (textConfig == null) {
                    continue;
                }
                AppBannerSvgaText appBannerSvgaText = new AppBannerSvgaText();

                //配置动态替换文本
                String text = contextReplace(textConfig.getText(), roleId, member, memberInfo, textConfig.getNameCountLimit(), dynamicReplace);
                if (attr.getLevelTextDynamicValue() != null && attr.getLevelTextDynamicValue().containsKey(level)) {
                    Map<String, String> replaceValue = attr.getLevelTextDynamicValue().get(level);
                    for (String key : replaceValue.keySet()) {
                        text = text.replace(key, replaceValue.get(key));
                    }
                }
                appBannerSvgaText.setText(text);
                appBannerSvgaText.setNameCountLimit(textConfig.getNameCountLimit());
                appBannerSvgaText.setGravity(textConfig.getGravity());
                if (StringUtil.isNotBlank(textConfig.getImages())) {
                    appBannerSvgaText.setImgs(Lists.newArrayList(textConfig.getImages().split(",")));
                }
                if (StringUtil.isNotBlank(textConfig.getFontSize())) {
                    appBannerSvgaText.setFontSize(JSON.parseObject(textConfig.getFontSize(), Map.class));
                }
                broSvgaTextLayer.put(textConfig.getKey(), appBannerSvgaText);

                if (MapUtils.isNotEmpty(broSvgaTextLayer)) {
                    broContentLayers.add(broSvgaTextLayer);
                }
            }

        }

        return broContentLayers;
    }

    private BlackListComponentAttr getBlackListComponentAttr(RankingTaskBannerComponentAttr attr) {
        if (attr.getBlackListCmptUseIndex() != 0) {
            return blackListComponent.getComponentAttr(attr.getActId(), attr.getBlackListCmptUseIndex());
        }
        return null;
    }

    /**
     * 是否黑名单厅，黑名单厅只广播当前频道
     */
    private boolean inBlackList(RankingTaskBannerComponentAttr attr, String subChannel, String member) {
        BlackListComponentAttr blackListComponentAttr = getBlackListComponentAttr(attr);
        if (blackListComponentAttr == null) {
            return false;
        }
        if (StringUtil.isBlank(blackListComponentAttr.getBlackList())) {
            return false;
        }
        String blackList = blackListComponentAttr.getBlackList();
        boolean isBlackList = StringUtil.isNotBlank(subChannel) && blackList.contains(subChannel);
        if (isBlackList) {
            log.info("user in black list subchannel, memberId:{}", member);
        }
        return isBlackList;
    }

    private String getSubChannel(TaskProgressChanged event, RankingTaskBannerComponentAttr attr) {
        if (CollectionUtils.isEmpty(attr.getSubChannelRoleId()) || MapUtils.isEmpty(event.getActors())) {
            return null;
        }
        for (Long roleId : attr.getSubChannelRoleId()) {
            if (event.getActors().containsKey(roleId)) {
                return event.getActors().get(roleId);
            }
        }
        return null;
    }

    private String contextReplace(String context, long roleId, String member, MemberInfo memberInfo, int nameCountLimit, Map<String, String> dynamicReplace) {
        if (roleId == RoleType.GUILD.getValue() || roleId == RoleType.HALL.getValue() || roleId == RoleType.ROOM.getValue() || roleId == RoleType.FAMILY.getValue()) {
            String nick = memberInfo.getName();
            String asid = memberInfo.getAsid();
            nick = nick.length() < nameCountLimit ? nick : nick.substring(0, nameCountLimit) + "...";
            context = context.replace("{nick}", HtmlUtils.htmlEscape(nick));
            context = context.replace("{asid}", HtmlUtils.htmlEscape(Convert.toString(asid)));
        } else {
            long uid = Convert.toLong(member);
            context = context.replace("{nick}", String.format("{%s:n}", uid)).replace("{headerNick}", String.format("{%s:an}", uid));
        }

        if (MapUtils.isNotEmpty(dynamicReplace)) {
            for (String needReplace : dynamicReplace.keySet()) {
                context = context.replace(needReplace, HtmlUtils.htmlEscape(dynamicReplace.get(needReplace)));
            }
        }

        return context;
    }

    private String replaceImage(String context, MemberInfo memberInfo) {
        return context.replace("{header}", Convert.toString(memberInfo.getHdLogo()));
    }

    private UserCurrentChannel getCurrenChannel(String subChannel) {
        UserCurrentChannel currentChannel = new UserCurrentChannel();
        //如果读不到用户当前所在频道，则用事件透传的频道
        if (StringUtil.isNotBlank(subChannel)) {
            String[] sid_ssid = subChannel.split("_");
            if (sid_ssid.length == Const.TOW) {
                currentChannel = new UserCurrentChannel();
                currentChannel.setTopsid(Convert.toLong(sid_ssid[0]));
                currentChannel.setSubsid(Convert.toLong(sid_ssid[1]));
            }
        }
        return currentChannel;
    }

    private void checkBroData(long actId, int broType, UserCurrentChannel currentChannel) {
        if (broType == FstAppBroadcastType.SUB_CHANNEL) {
            if (currentChannel.getSubsid() <= 0 || currentChannel.getTopsid() <= 0) {
                log.error("子频道广播范围未指定子频道,actId:{}", actId);
                throw new RuntimeException("子频道广播范围未指定子频道");
            }
        }
        if (broType == FstAppBroadcastType.TOP_CHANNEL && currentChannel.getTopsid() <= 0) {
            log.error("顶级广播范围未指定顶级频道,actId:{}", actId);
            throw new RuntimeException("顶级广播范围未指定顶级频道");
        }
    }

    private void setUidList(long roleId, String member, AppBannerEvent2 appBannerEvent) {
        if (roleId == RoleType.ANCHOR.getValue() || roleId == RoleType.USER.getValue()) {
            long uid = Convert.toLong(member);
            appBannerEvent.setUid(uid);
            appBannerEvent.setUidList(Lists.newArrayList(uid));
        }
    }

    public static int getTurnoverAppId(int busiId) {
        BusiId value = BusiId.findByValue(busiId);
        int appId = 0;
        switch (value) {
            case GAME_BABY:
                appId = 36;
                break;
            case MAKE_FRIEND:
                appId = 2;
                break;
            case SKILL_CARD:
                appId = 34;
                break;
            default:
                break;
        }
        return appId;

    }


    public void pcInvokeOnTaskChangeEvent(TaskProgressChanged event, RankingTaskBannerComponentAttr attr, boolean checkRankId) {
        long rankId = event.getRankId(), phaseId = event.getPhaseId();
        if (!attr.isMyDuty(rankId, phaseId) && checkRankId) {
            return;
        }
        log.info("pcInvokeOnTaskChangeEvent start seq:{}, member:{}, event:{}", event.getSeq(), event.getMember(), JSON.toJSONString(event));
        long actId = event.getActId();
        // 提取奖励接收者
        int receiverInx = attr.getReceiverInx();
        String receiver = event.getMember().split("\\|")[receiverInx];
        long roleId = attr.getRoleId();

        Map<Long, List<TaskBroadcastConfig>> broConfig = getBroadcastConfig(roleId, attr);
        if (MapUtils.isEmpty(broConfig)) {
            log.warn("pcInvokeOnTaskChangeEvent broConfig not found,roleId:{},memberId:{},seq:{}", roleId, receiver, event.getSeq());
            return;
        }

        List<TaskBroadcastConfig> configs = getSubScoreBroConfig(broConfig, event, attr.isSelectHighestLevel());
        for (TaskBroadcastConfig config : configs) {
            log.info("invokeBro begin,actId:{},receiver:{},score:{},selectHighestLevel:{},config:{}", attr.getActId(), receiver, event.getRankScore(), attr.isSelectHighestLevel(), JSON.toJSONString(config));
            threadPoolManager.get(Const.IMPORTANT_POOL).execute(new Runnable() {
                @Override
                public void run() {
                    pcInvokeBro(event, attr, receiver, config);
                }
            });
        }
    }

    private void pcInvokeBro(TaskProgressChanged event, RankingTaskBannerComponentAttr attr, String memberId, TaskBroadcastConfig config) {
        //延迟广播
        if (config.getDelayMillSeconds() > 0) {
            log.info("pcInvokeBro delay,actId:{},memberId:{},score:{},config:{}", attr.getActId(), memberId, event.getRankScore(), JSON.toJSONString(config));
            SysEvHelper.waiting(config.getDelayMillSeconds());
        }

        final int broType = config.getBroType();
        String bannerUrl = config.getBannerUrl();

        long actId = attr.getActId();
        long score = event.getRankScore();
        //默认设置为交友
        long busiId = BusiId.MAKE_FRIEND.getValue();
        String asId = StringUtils.EMPTY;
        EnrollmentInfo enrollmentInfo = enrollmentNewService.tryGetFirstEnrolMemberCache(attr.getActId(), 0L, attr.getRoleId(), memberId);
        if (enrollmentInfo != null) {
            busiId = enrollmentInfo.getRoleBusiId();
            asId = String.valueOf(enrollmentInfo.getSignAsid());
        } else {
            if (attr.getRoleId() != RoleType.USER.getValue()) {
                log.error("bro can not get enrollmentInfo,actId:{},memberId:{}", actId, memberId);
            }
        }

        MemberInfo memberInfo = memberInfoService.getMemberInfo(busiId, RoleType.findByValue(attr.getRoleId()), memberId);
        String nick = memberInfo.getName();

        Map<String, String> extMap = Maps.newHashMapWithExpectedSize(10);
        extMap.put("memberId", memberId);
        extMap.put("nickName", nick);
        extMap.put("logo", StringUtil.isNotBlank(memberInfo.getHdLogo()) ? memberInfo.getHdLogo() : memberInfo.getLogo());
        extMap.put("value", String.valueOf(score));
        extMap.put("svgaUrl", bannerUrl);
        extMap.put("asid", asId);
        extMap.put("ext", config.getExt());
        extMap.put("busiId", String.valueOf(busiId));
        extMap.put("desc", config.getDesc());
        extMap.put("roleType", String.valueOf(attr.getRoleId()));

        GameecologyActivity.BannerBroadcast bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId).setBannerId(config.getBannerId()).setUserNick(nick)
                .setUserScore(score).setJsonData(JSON.toJSONString(extMap)).build();

        GameecologyActivity.GameEcologyMsg bannerBroMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();

        if (broType == BroadcastConfig.BroType.ACT_BUSI.code) {
            broadCastHelpService.broadcast(actId, BusiId.GAME_ECOLOGY, broType, 0, 0, bannerBroMsg);
        } else if (broType == BroadcastConfig.BroType.BUSI_TEMPLATE.code) {
            if (busiId == BusiId.MAKE_FRIEND.getValue() && attr.getExcludeDanmaku() == 1) {
                broadCastHelpService.broadcastExcludeDanmakuChannel(bannerBroMsg);
            } else {
                broadCastHelpService.broadcast(actId, BusiId.findByValue((int) busiId), broType, 0, 0, bannerBroMsg);
            }
        } else if (broType == BroadcastType.FAMILY) {
            long familyId = getFamilyId(attr.getRoleId(), memberId);
            broadCastHelpService.broadcast("", actId, BusiId.findByValue((int) busiId), broType, familyId, 0, 0, bannerBroMsg);
        } else {
            ChannelInfoVo channel = getChannelInfo(attr.getRoleId(), broType, memberId);
            if (channel == null) {
                log.error("pcInvokeBro ranking banner bro error,user not in channel memberId:{} config:{}", memberId, JSON.toJSONString(config));
                return;
            }
            broadCastHelpService.broadcast(actId, BusiId.findByValue((int) busiId), broType, channel.getSid(), channel.getSsid(), bannerBroMsg);
        }
        log.info("pcInvokeBro ranking banner bro done memberId:{} task:{}", memberId, JSON.toJSONString(config));

        bigDataService.saveNoRankDataToFile(actId, BusiId.findByValue(Convert.toInt(busiId)), System.currentTimeMillis(), memberId
                , RoleType.findByValue(attr.getRoleId()), score, BigDataScoreType.RANK_SCORE_BANNER, config.getDesc() + "|" + config.getBannerId() + "|" + config.getBroType());
    }


    private long getFamilyId(int roleType, String memberId) {
        if (RoleType.ROOM.getValue() == roleType) {
            RoomInfo roomInfo = commonService.getRoomInfoByRoomId(Convert.toInt(memberId));
            if (roomInfo == null) {
                throw new RuntimeException("not found roomInfo,roomId:" + memberId);
            }
            return roomInfo.getFamilyId();
        } else if (RoleType.FAMILY.getValue() == roleType) {
            return Convert.toLong(memberId);
        } else if (RoleType.ANCHOR.getValue() == roleType) {
            long familyId = turnoverFamilyThriftClient.queryContractFamilyId(Convert.toLong(memberId));
            if (familyId == 0) {
                throw new RuntimeException("not found contractFamilyId ,anchorId:" + memberId);
            }
            return familyId;
        }

        throw new RuntimeException("not support roleType:" + roleType);
    }

    private ChannelInfoVo getChannelInfo(int roleType, int broType, String memberId) {
        ChannelInfoVo vo = new ChannelInfoVo();
        if (RoleType.ANCHOR.getValue() == roleType || RoleType.USER.getValue() == roleType) {
            UserCurrentChannel channel = commonService.getUserCurrentChannel(Convert.toLong(memberId));
            if (channel == null) {
                log.error("log ranking banner bro error,user not in channel uid:{}", memberId);
                return null;
            }
            vo.setSid(channel.getTopsid());
            vo.setSsid(channel.getSubsid());
            return vo;
        } else if (RoleType.HALL.getValue() == roleType) {
            String[] array = memberId.split("_");
            vo.setSid(Convert.toLong(array[0]));
            vo.setSsid(Convert.toLong(array[1]));
            return vo;

        } else if (RoleType.GUILD.getValue() == roleType) {
            vo.setSid(Convert.toLong(memberId));
            return vo;
        } else if (RoleType.ROOM.getValue() == roleType) {
            RoomInfo roomInfo = commonService.getRoomInfoByRoomId(Convert.toInt(memberId));
            if (roomInfo == null) {
                log.error("log ranking banner bro error, room info cannot be found rooId:{}", memberId);
                return null;
            }

            vo.setSid(roomInfo.sid);
            vo.setSsid(roomInfo.ssid);

            return vo;
        }

        return null;
    }


    public List<TaskBroadcastConfig> getSubScoreBroConfig(Map<Long, List<TaskBroadcastConfig>> bannerConfigMap, TaskProgressChanged event, boolean selectHighestLevel) {
        List<TaskBroadcastConfig> configs = Lists.newArrayList();
        for (long taskIndex = event.getCurrTaskIndex(); taskIndex > event.getStartTaskIndex(); taskIndex--) {
            configs.addAll(bannerConfigMap.get(taskIndex));
            if (selectHighestLevel) {
                return configs;
            }
        }

        return configs;
    }

    private Map<Long, List<TaskBroadcastConfig>> getBroadcastConfig(long roleId, RankingTaskBannerComponentAttr attr) {
        for (String roleIds : attr.getRoleScoreBannerConfig().keySet()) {
            if ((roleIds + ",").contains(roleId + ",")) {
                return attr.getRoleScoreBannerConfig().get(roleIds);
            }
        }

        return attr.getDefaultScoreBannerConfig();
    }

}
