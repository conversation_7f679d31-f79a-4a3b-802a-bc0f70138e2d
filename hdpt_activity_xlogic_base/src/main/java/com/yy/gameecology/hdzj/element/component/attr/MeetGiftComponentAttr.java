package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import lombok.Getter;
import lombok.Setter;

import java.time.Duration;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class MeetGiftComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务id", dropDownSourceBeanClass = BizSource.class)
    protected int buisId;

    @ComponentAttrField(labelText = "开始时间")
    protected Date startTime;

    @ComponentAttrField(labelText = "结束时间")
    protected Date endTime;

    @ComponentAttrField(labelText = "奖池ID", remark = "遇见好礼发奖的奖池ID")
    protected long tAwardTskId;

    @ComponentAttrField(labelText = "奖包ID", remark = "遇见好礼发奖的奖包ID")
    protected long tAwardPkgId;

    @ComponentAttrField(labelText = "发奖场景", remark = "发奖场景")
    protected String awardScene;

//    @ComponentAttrField(labelText = "礼物名称", remark = "遇见好礼发放的礼物名称")
//    protected String giftName;
//
//    @ComponentAttrField(labelText = "礼物图片", remark = "遇见好礼发放的礼物图片", propType = ComponentAttrCollector.PropType.IMAGE)
//    protected String giftIcon;

    @ComponentAttrField(labelText = "日上限", remark = "遇见好礼发放的每日上限")
    protected long dailyLimitCount;

    @ComponentAttrField(labelText = "弹窗延迟", remark = "进频道延迟多久才下发弹窗")
    protected Duration delay;

    @ComponentAttrField(labelText = "随机频道列表", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = ChannelInfo.class), remark = "用户不在频道内的时候跳转随机频道的池子")
    protected List<ChannelInfo> channels;
}
