package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.hdzt.*;
import com.yy.gameecology.activity.bean.rank.PkRankItem;
import com.yy.gameecology.activity.bean.rank.TeamRankItem;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.service.EnrollmentNewService;
import com.yy.gameecology.activity.service.MemberInfoService;
import com.yy.gameecology.activity.service.SvcSDKService;
import com.yy.gameecology.activity.worker.timer.TimerSupport;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.bean.GuildInfoBean;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.HdztRoleId;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.locker.Locker;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.history.attr.PkFireComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * desc:pk为爱宣战组件
 *
 * @createBy 曾文帜
 * @create 2021-04-26 18:18
 **/
@Deprecated
@Component
public class PkFireComponent extends BaseActComponent<PkFireComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * pk卡余额，pk卡只能当天有效  %s_%s , yyyyMMdd_公会id ,field tuanId
     */
    private static final String PK_CARD_BALANCE_HASH_KEY = "pk_card_balance_%s_%s";

    /**
     * 有被人pk资格的key   %s:yyyyMMdd ,field TuanId
     */
    private static final String ACPT_PK_LIST_KEY = "pk_acpt_%s";


    //--- pk模块相关key
    //正在pk 分数,member sid_tuanid ,field sid_tuanId
    private static final String PK_SCORE_RANK_KEY = "pk_score";

    // %s 发起pk,正在pk中成员 公会id,field tuanId
    private static final String PK_START_INFO_KEY = "pk_info_start_%s";

    // %s 接收pk，正在pk中成员 公会id,field tuanId
    private static final String PK_ACPT_INFO_KEY = "pk_info_acpt_%s";

    //pk已结算,pk历史数据 %s: 日期yyyyMM,field tuanId
    private static final String PK_HISTORY_LIST_KEY = "pk_his_%s";


    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private EnrollmentNewService enrollmentService;

    @Autowired
    private ActRedisGroupDao actRedisDao;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private WebdbThriftClient webdbThriftClient;

    @Autowired
    private MemberInfoService memberInfoService;

    @Autowired
    private SvcSDKService svcSDKService;

    @Autowired
    private Locker locker;

    @Autowired
    private TimerSupport timerSupport;

    @Override
    public Long getComponentId() {
        return ComponentId.PK_FIRE;
    }

    /**
     * 小时榜，榜单结束事件，为爱宣战热身赛发放宣战资格和被宣战资格
     */
    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = true)
    public void onPhaseTimeEnd(PhaseTimeEnd event, PkFireComponentAttr attr) {
        log.info("onPhaseTimeEnd start -> event:{}, attr:{}", event, attr);

        long actId = event.getActId();
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();

        if (rankId != attr.getAwardCardSourceRankId() || phaseId != attr.getAwardCardSourcePhaseId()) {
            log.info("not my duty");
            return;
        }

        String checkName = "onPhaseTimeEnd:" + event.getEkey();
        String checkValue = event.getTimestamp() + ":" + event.getSeq();
        //请求去重
        String groupCode = getRedisGroupCode(event.getActId());
        boolean firstRun = actRedisDao.setNX(groupCode, makeKey(attr, checkName), checkValue);
        if (!firstRun) {
            log.warn("PkFireComponent onPhaseTimeEnd dup,return, event:{}, attr:{}", event, attr);
            return;
        }


        String dateStr = null;
        if (TimeKeyHelper.isSplitByHour(event.getTimeKey())) {
            Date date = DateUtil.getDate(event.getEndTime());
            dateStr = DateUtil.format(date, DateUtil.PATTERN_TYPE7);

        } else if (TimeKeyHelper.isSplitByDay(event.getTimeKey())) {
            Date date = DateUtil.getDate(event.getEndTime());
            dateStr = DateUtil.format(date, DateUtil.PATTERN_TYPE2);
        }
        //小时榜，榜单结束事件，为爱宣战热身赛发放宣战资格和被宣战资格
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(actId, rankId, phaseId, dateStr, attr.getAwardPkTopN(), null);
        //top n有发起pk资格
        List<RankItem> startPKChance = Lists.newArrayList();
        //top n外有接受 pk资格
        List<RankItem> acptPKChance = Lists.newArrayList();
        for (int i = 0; i < ranks.size(); i++) {
            Rank rank = ranks.get(i);

            RankItem rankItem = new RankItem();
            rankItem.setMember(rank.getMember());
            rankItem.setRank(rank.getRank());
            if (attr.getPkRoleType() == RoleType.PWTUAN.getValue()) {
                long tuanSid = getPwTuanSid(actId, rank.getMember());
                //陪玩团的频道号
                rankItem.setMember2(tuanSid + "");
            }
            if (i < attr.getAwardStartPkChanceTopN()) {
                startPKChance.add(rankItem);
            } else {
                acptPKChance.add(rankItem);
            }

        }

        Date now = commonService.getNow(actId);
        //保存卡余额以及被挑战资格
        actRedisDao.getRedisTemplate(groupCode).executePipelined((RedisConnection conn) -> {
            for (RankItem item : startPKChance) {
                String key = buildBalanceKey(attr, now, item.getMember2());
                conn.hSet(key.getBytes(), item.getMember().getBytes(), (attr.getAwardPkChanceAmount() + "").getBytes());
            }
            log.info("save balance,actId:{},balance:{},amount:{}", actId, JSON.toJSONString(startPKChance), attr.getAwardPkChanceAmount());

            for (RankItem item : acptPKChance) {
                String key = buildAcptPkMemberKey(attr, now);
                conn.lPush(key.getBytes(), item.getMember().getBytes());
            }
            log.info("save acptPKChance,actId:{},chance:{}", actId, JSON.toJSONString(acptPKChance));

            return null;
        });


    }

    /**
     * 榜单变化事件，累pk榜
     */
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = false)
    public void onRankingScoreChanged(RankingScoreChanged event, PkFireComponentAttr attr) {
        log.info("onTaskProgressChanged start -> event:{}, attr:{}", event, attr);

        //圈定要处理的榜单阶段id
        if (event.getRankId() != attr.getAddPkScoreHdztRankId()) {
            return;
        }

        long addScore = event.getItemScore();
        String tuanId = event.getActors().get(HdztRoleId.PW_TUAN);
        String sid = event.getActors().get(HdztRoleId.PW_CHANNEL);

        //在pk中存在初始分才能累榜，另外防止有结算和累榜并发问题，真正累榜的时候要用lua再次判断有无初始值
        String key = buildPkScoreKey(attr);
        String memberId = sid + "_" + tuanId;
        String groupCode = getRedisGroupCode(event.getActId());
        Double score = actRedisDao.zScore(groupCode, key, memberId);
        if (score == null) {
            log.info("not in pk,return,actId:{},memberId:{},addScore:{}", event.getActId(), memberId, addScore);
            return;
        }


        //开始累榜
        Long res = actRedisDao.zincrWithTime(groupCode, key, memberId, addScore, true);
        if (res == null) {
            log.warn("not in pk,add score failed,actId:{},memberId:{},addScore:{}", event.getActId(), memberId, addScore);
            return;
        }

        log.info("add pk score done,key:{},memberId:{},addScore:{}", key, memberId, addScore);
    }

    /**
     * pk卡余额查询
     *
     * @param actId
     * @param uid
     * @return
     */
    public List<RankItem> queryPkCard(long actId, long uid) {
        PkFireComponentAttr attr = getUniqueComponentAttr(actId);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + 1);
        return queryPkCard(attr, uid);
    }

    /**
     * 被pk的团查询
     *
     * @param actId
     * @param uid
     * @return
     */
    public List<RankItem> queryAcptPkMember(long actId, long uid) {
        PkFireComponentAttr attr = getUniqueComponentAttr(actId);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + 1);
        return queryAcptPkMember(attr, uid);
    }

    /**
     * 开始pk
     *
     * @param actId
     * @param uid
     * @param cardMember
     * @param acptMember
     */
    public void invokeStartPk(long actId, long uid, String cardMember, String acptMember) {
        PkFireComponentAttr attr = getUniqueComponentAttr(actId);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + 1);
        invokeStartPk(attr, uid, cardMember, acptMember);
    }

    /**
     * pk榜查询
     *
     * @param actId
     * @param dateStr
     * @return
     */
    @Cached(timeToLiveMillis = 10 * 1000L)
    public List<PkRankItem> queryPkRank(long actId, String dateStr) {
        PkFireComponentAttr attr = getUniqueComponentAttr(actId);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + 1);
        return queryPkRank(attr, dateStr);
    }

    /**
     * pk卡余额查询
     */
    public List<RankItem> queryPkCard(PkFireComponentAttr attr, long uid) {
        List<RankItem> balances = Lists.newArrayList();

        Date now = commonService.getNow(attr.getActId());
        //ow的公会
        Map<Long, GuildInfoBean> guildInfoBeanMap = webdbThriftClient.getSessionListByOwnerid(uid);
        if (MapUtils.isEmpty(guildInfoBeanMap)) {
            return balances;
        }

        //组余额key
        List<String> balanceKey = Lists.newArrayList();
        for (Long sid : guildInfoBeanMap.keySet()) {
            String key = buildBalanceKey(attr, now, sid + "");
            balanceKey.add(key);
        }
        String groupCode = getRedisGroupCode(attr.getActId());
        //pk余额
        List<Object> results = actRedisDao.getRedisTemplate(groupCode).executePipelined((RedisConnection connection) -> {
            for (String key : balanceKey) {
                connection.hGetAll(key.getBytes());
            }
            return null;
        });
        if (CollectionUtils.isEmpty(results)) {
            return balances;
        }

        int index = 0;
        for (Long sid : guildInfoBeanMap.keySet()) {
            Map<String, String> cardScoreItem = (Map<String, String>) results.get(index);
            for (String tuanId : cardScoreItem.keySet()) {
                RankItem balanceItem = new RankItem();

                long score = Convert.toLong(cardScoreItem.get(tuanId));
                String memberId = sid + "," + tuanId;

                MemberInfo memberInfo = memberInfoService.getPwTeam(tuanId);
                if (memberInfo != null) {
                    balanceItem.setItemDesc(memberInfo.getName());
                }
                balanceItem.setScore(score);
                balanceItem.setMember(memberId);

                balances.add(balanceItem);
            }

            index++;
        }

        return balances;
    }


    /**
     * 被pk的团查询
     */

    public List<RankItem> queryAcptPkMember(PkFireComponentAttr attr, long uid) {
        List<RankItem> result = Lists.newArrayList();

        Date now = commonService.getNow(attr.getActId());
        String key = buildAcptPkMemberKey(attr, now);
        String groupCode = getRedisGroupCode(attr.getActId());
        List<String> acptPkList = actRedisDao.getRedisTemplate(groupCode).opsForList().range(key, 0, 1000);
        if (CollectionUtils.isEmpty(acptPkList)) {
            return result;
        }
        //团基础信息
        Map<String, MemberInfo> teamMap = memberInfoService.getPwTeamMap(acptPkList);
        for (String tuanId : acptPkList) {
            RankItem rankItem = new RankItem();
            rankItem.setMember(tuanId);
            MemberInfo memberInfo = teamMap.get(tuanId);
            if (memberInfo == null) {
                log.error("member not found,tuanId:{}", tuanId);
                continue;
            }
            rankItem.setItemDesc(memberInfo.getName());
            rankItem.setMember2(memberInfo.getSid());
            rankItem.setGroupId(memberInfo.getAsid());
            String pkScoreKey = buildPkScoreKey(attr);
            String pkMemberId = memberInfo.getSid() + "_" + tuanId;
            //开始pk 即初始化个0分，所以有值的代表正在pk,不可接受其他人的pk
            Double score = actRedisDao.getRedisTemplate(groupCode).opsForZSet().score(pkScoreKey, pkMemberId);
            if (score == null) {
                rankItem.setScore(1L);
            } else {
                rankItem.setScore(0L);
            }

            result.add(rankItem);
        }

        return result;
    }


    public void invokeStartPk(PkFireComponentAttr attr, long uid, String cardMember, String acptMember) {
        String lockName = makeKey(attr, "start_pk_lock");
        Secret secret = null;
        try {
            secret = locker.lock(lockName, 10);
            if (secret == null) {
                log.warn("startPk return lock failed,actId:{},uid:{},cardMember:{},acptMember:{}", attr.getActId(), uid, cardMember, acptMember);
                throw new SuperException("网络超时，请重试", SuperException.E_DATA_ERROR);
            }

            startPk(attr, uid, cardMember, acptMember);

            locker.unlock(lockName, secret);

        } catch (Exception e) {
            if (secret != null) {
                locker.unlock(lockName, secret);
            }
            throw e;
        }
    }

    /**
     * 此方法需要根据活动id加redis锁，避免并发发起挑战问题导致数据不一致
     * <p>
     * 发起pk
     */

    public void startPk(PkFireComponentAttr attr, long uid, String cardMember, String acptMember) {
        log.info("startPk begin,actId:{},uid:{},cardMember:{},acptMember:{}", attr.getActId(), uid, cardMember, acptMember);

        String[] cardMemberArray = cardMember.split(",");
        long startSid = Convert.toLong(cardMemberArray[0]);
        String tuanId = cardMemberArray[1];
        Date now = commonService.getNow(attr.getActId());

        String[] acptArray = acptMember.split(",");
        long acptSid = Convert.toLong(acptArray[0]);
        String acptTuanId = acptArray[1];

        //---各种检查

        //公会ow权限验证
        Map<Long, GuildInfoBean> guildInfoBeanMap = webdbThriftClient.getSessionListByOwnerid(uid);
        if (!guildInfoBeanMap.containsKey(startSid)) {
            throw new SuperException("请选择正确的公会", SuperException.E_PARAM_ILLEGAL);
        }

        //团身份验证
        MemberInfo memberInfo = memberInfoService.getPwTeam(tuanId);
        if (memberInfo == null || !(startSid + "").equals(memberInfo.getSid())) {
            throw new SuperException("请选择正确的团", SuperException.E_PARAM_ILLEGAL);
        }

        //pk时间检查
        if ((now.getTime() >= attr.getStartPkEndTime().getTime())) {
            throw new SuperException(String.format("距离比赛结束时间小于%s小时，无法发起宣战", attr.getPkMillSeconds() / 3600_000), SuperException.E_PARAM_ILLEGAL);
        }

        String groupCode = getRedisGroupCode(attr.getActId());
        //pk卡 余额检查
        String balanceKey = buildBalanceKey(attr, now, startSid + "");
        long balance = Convert.toLong(actRedisDao.hget(groupCode, balanceKey, tuanId), 0);
        if (balance <= 0) {
            throw new SuperException("卡余额不足", SuperException.E_PARAM_ILLEGAL);
        }

        //是否正在pk中检查
        String pkScoreKey = buildPkScoreKey(attr);
        String memberId = startSid + "_" + tuanId;
        //开始pk 即初始化个0分，所以有值的代表正在pk,不可接受其他人的pk
        Double score = actRedisDao.getRedisTemplate(groupCode).opsForZSet().score(pkScoreKey, memberId);
        if (score != null) {
            throw new SuperException("pk未结束，不能发起新的pk", SuperException.E_PARAM_ILLEGAL);
        }

        String acptMemberId = acptSid + "_" + acptTuanId;
        Double acptScore = actRedisDao.getRedisTemplate(groupCode).opsForZSet().score(pkScoreKey, acptMemberId);
        if (acptScore != null) {
            throw new SuperException("被挑战方已在pk中", SuperException.E_PARAM_ILLEGAL);
        }


        PkInfoVo pkInfoVo = new PkInfoVo();
        pkInfoVo.setPkId(java.util.UUID.randomUUID().toString());
        pkInfoVo.setMemberId1(memberId);
        pkInfoVo.setMemberId2(acptMemberId);
        pkInfoVo.setPkStartTime(now.getTime());
        pkInfoVo.setPkEndTime(now.getTime() + attr.getPkMillSeconds());
        String pkInfoStr = JSON.toJSONString(pkInfoVo);
        //---数据操作
        actRedisDao.getRedisTemplate(groupCode).executePipelined((RedisConnection conn) -> {
            //扣减pk卡余额
            conn.hIncrBy(balanceKey.getBytes(), tuanId.getBytes(), -1);
            //分数初始值，同时代表已经开始pk
            conn.zAdd(pkScoreKey.getBytes(), 0, memberId.getBytes());
            conn.zAdd(pkScoreKey.getBytes(), 0, acptMemberId.getBytes());
            //pk详细信息
            String startInfoKey = buildStartPkInfoKey(attr, startSid);
            conn.hSet(startInfoKey.getBytes(), tuanId.getBytes(), pkInfoStr.getBytes());
            String acptInfoKey = buildAcptPkInfoKey(attr, acptSid);
            conn.hSet(acptInfoKey.getBytes(), acptTuanId.getBytes(), pkInfoStr.getBytes());

            return null;
        });

        //广播开始pk
        threadPoolManager.get(Const.IMPORTANT_POOL).execute(new Runnable() {
            @Override
            public void run() {
                log.info("broStartPk begin,tuanId:{},acptTuanId:{},pkInfo:{}", tuanId, acptTuanId, JSON.toJSONString(pkInfoVo));
                broStartPk(attr, tuanId, acptTuanId);
            }
        });


        log.info("startPk done,actId:{},uid:{},cardMember:{},acptMember:{}", attr.getActId(), uid, cardMember, acptMember);
    }


    /**
     * pk榜查询
     */
    private List<PkRankItem> queryPkRank(PkFireComponentAttr attr, String dateStr) {

        String groupCode = getRedisGroupCode(attr.getActId());
        //pk历史数据
        String rankHisKey = buildHisRankKey(attr, dateStr);
        List<String> rankHis = actRedisDao.getRedisTemplate(groupCode).opsForList().range(rankHisKey, 0, 1000);
        List<PkRankItem> pkRankItems = queryPkHisRank(rankHis);

        //正在pk数据
        List<PkInfoVo> pkingInfoVos = getPKinginfo(attr, 0L);
        if (!CollectionUtils.isEmpty(pkingInfoVos)) {
            List<PkRankItem> pkingItems = Lists.newArrayList();
            for (PkInfoVo pkInfoVo : pkingInfoVos) {
                PkRankItem item = new PkRankItem();
                //排序用
                long maxScore = Math.max(pkInfoVo.getValue1(), pkInfoVo.getValue2());
                item.setValue(maxScore);

                //宣战方
                TeamRankItem item1 = new TeamRankItem();
                item1.setValue(pkInfoVo.getValue1());
                item1.setName(pkInfoVo.getMemberId1Ext());
                item1.setAsid(Convert.toLong(pkInfoVo.getMember1Sid(), 0));
                //接受放
                TeamRankItem item2 = new TeamRankItem();
                item2.setValue(pkInfoVo.getValue2());
                item2.setName(pkInfoVo.getMemberId2Ext());
                item2.setAsid(Convert.toLong(pkInfoVo.getMember2Sid(), 0));

                item.setItem1(item1);
                item.setItem2(item2);
                pkingItems.add(item);
            }
            pkingItems = pkingItems.stream().sorted(Comparator.comparing(PkRankItem::getValue).reversed()).collect(Collectors.toList());
            //正在pk的排前面
            pkRankItems.addAll(0, pkingItems);
        }

        return pkRankItems;
    }

    private List<PkRankItem> queryPkHisRank(List<String> rankHis) {
        List<PkRankItem> pkRankItems = Lists.newArrayList();

        if (CollectionUtils.isEmpty(rankHis)) {
            return pkRankItems;
        }

        List<PkInfoVo> pkInfoVos = rankHis.stream().map(x -> JSON.parseObject(x, PkInfoVo.class)).collect(Collectors.toList());

        //团基础信息
        List<String> allTuanIds = Lists.newArrayList();
        List<String> tuanId1 = pkInfoVos.stream().map(x -> x.getMemberId1().split("_")[1]).collect(Collectors.toList());
        List<String> tuanId2 = pkInfoVos.stream().map(x -> x.getMemberId2().split("_")[1]).collect(Collectors.toList());
        allTuanIds.addAll(tuanId1);
        allTuanIds.addAll(tuanId2);
        Map<String, MemberInfo> tuanInfoMap = memberInfoService.getPwTeamMap(allTuanIds);


        for (int i = 0; i < pkInfoVos.size(); i++) {
            PkInfoVo vo = pkInfoVos.get(i);
            PkRankItem item = new PkRankItem();

            //排序用
            long maxScore = Math.max(vo.getValue1(), vo.getValue2());
            item.setValue(maxScore);

            //宣战方
            TeamRankItem item1 = new TeamRankItem();
            item1.setValue(vo.getValue1());
            MemberInfo memberInfo1 = tuanInfoMap.get(vo.getMemberId1().split("_")[1]);
            if (memberInfo1 != null) {
                item1.setName(memberInfo1.getName());
                long sid = Convert.toLong(memberInfo1.getSid(), 0);
                item1.setAsid(Convert.toLong(memberInfo1.getAsid(), sid));
            }
            item.setItem1(item1);

            //迎战方
            TeamRankItem item2 = new TeamRankItem();
            item2.setValue(vo.getValue2());
            MemberInfo memberInfo2 = tuanInfoMap.get(vo.getMemberId2().split("_")[1]);
            if (memberInfo2 != null) {
                item2.setName(memberInfo2.getName());
                long sid = Convert.toLong(memberInfo2.getSid(), 0);
                item2.setAsid(Convert.toLong(memberInfo2.getAsid(), sid));
            }
            item.setItem2(item2);

            //胜利方运算
            BigDecimal item1Score = new BigDecimal(vo.getValue1());
            item1Score = item1Score.add(new BigDecimal(vo.getValue1Ext()));
            BigDecimal item2Score = new BigDecimal(vo.getValue2());
            item2Score = item2Score.add(new BigDecimal(vo.getValue2Ext()));
            if (item1Score.compareTo(item2Score) > 0) {
                item1.setWin(1);
                item2.setWin(-1);
            } else {
                item1.setWin(-1);
                item2.setWin(1);
            }


            pkRankItems.add(item);
        }

        pkRankItems = pkRankItems.stream().sorted(Comparator.comparing(PkRankItem::getValue).reversed()).collect(Collectors.toList());

        //top 1 今日战神指定给前端
        PkRankItem top1 = pkRankItems.get(0);
        if (((TeamRankItem) top1.getItem1()).getWin() == 1) {
            top1.getItem1().getViewExt().put("totalRank", "1");
        } else {
            top1.getItem2().getViewExt().put("totalRank", "1");
        }

        return pkRankItems;
    }


    //同1个频道的2个团发起pk时，拿出数据有重复，上线前去重，临时紧急处理！！！
    public List<PkInfoVo> getPKinginfo(PkFireComponentAttr attr, long sid) {
        List<PkInfoVo> result = Lists.newArrayList();

        List<PkInfoVo> pkInfoVos = getPKing(attr, sid);
        if (CollectionUtils.isEmpty(pkInfoVos)) {
            return result;
        }
        Set<String> pkids = Sets.newHashSet();
        for (PkInfoVo vo : pkInfoVos) {
            String pkId = vo.getPkId();
            if (pkids.contains(pkId)) {
                continue;
            }

            pkids.add(pkId);
            result.add(vo);
        }
        return result;
    }

    /**
     * 获取挂件中展示的pk
     *
     * @param sid 当前频道正在pk数据， =0时查所有
     */
    public List<PkInfoVo> getPKing(PkFireComponentAttr attr, long sid) {
        String key = buildPkScoreKey(attr);

        String groupCode = getRedisGroupCode(attr.getActId());
        Set<ZSetOperations.TypedTuple<String>> pkMembers = actRedisDao.zrevRange(groupCode, key, attr.getQueryPkHisMaxCount());
        Map<String, Long> membersScore = Maps.newHashMap();
        Map<String, Long> allMembersScore = Maps.newHashMap();
        List<String> allMembers = Lists.newArrayList();
        for (ZSetOperations.TypedTuple<String> item : pkMembers) {
            String member = item.getValue();
            allMembers.add(member);
            allMembersScore.put(member, item.getScore().longValue());
            //和本频道相关数据
            if (member.contains(sid + "_") || sid == 0) {
                membersScore.put(member, item.getScore().longValue());
            }
        }
        if (MapUtils.isEmpty(membersScore)) {
            return null;
        }
        List<PkInfoVo> vos = Lists.newArrayList();

        List<String> tuanIds = allMembers.stream().map(x -> x.split("_")[1]).collect(Collectors.toList());
        Map<String, MemberInfo> memberInfoMap = memberInfoService.getPwTeamMap(tuanIds);

        for (String member : membersScore.keySet()) {
            String[] memberArray = member.split("_");
            long curSid = sid == 0 ? Convert.toLong(memberArray[0], 0) : sid;
            //团id
            String subMemberId = memberArray[1];

            //本频道是发起pk方的数据
            String startPkInfoKey = buildStartPkInfoKey(attr, curSid);
            String startPkInfo = actRedisDao.hget(groupCode, startPkInfoKey, subMemberId);
            if (StringUtil.isNotBlank(startPkInfo)) {
                PkInfoVo pkInfoVo = buildPkInfo(startPkInfo, allMembersScore, memberInfoMap);
                vos.add(pkInfoVo);
            }
            //不是查所有的时候，才需要再次查接受pk方的数据，否则会查重复
            else if (sid != 0) {
                //本频道是接受方的数据
                String acptPkInfoKey = buildAcptPkInfoKey(attr, curSid);
                String acptPkInfo = actRedisDao.hget(groupCode, acptPkInfoKey, subMemberId);
                if (StringUtil.isNotBlank(acptPkInfo)) {
                    PkInfoVo pkInfoVo = buildPkInfo(acptPkInfo, allMembersScore, memberInfoMap);
                    vos.add(pkInfoVo);
                }
            }
        }

        return vos;
    }

    private PkInfoVo buildPkInfo(String pkInfo, Map<String, Long> membersScore, Map<String, MemberInfo> memberInfoMap) {
        PkInfoVo pkInfoVo = JSON.parseObject(pkInfo, PkInfoVo.class);

        //分数
        Long member1Score = Convert.toLong(membersScore.get(pkInfoVo.getMemberId1()), 0);
        pkInfoVo.setValue1(member1Score);
        Long member2Score = Convert.toLong(membersScore.get(pkInfoVo.getMemberId2()), 0);
        pkInfoVo.setValue2(member2Score);

        //---团昵称
        String subMember1 = pkInfoVo.getMemberId1().split("_")[1];
        if (memberInfoMap.containsKey(subMember1)) {
            MemberInfo memberInfo = memberInfoMap.get(subMember1);
            pkInfoVo.setMemberId1(subMember1);
            pkInfoVo.setMember1Sid(memberInfo.getAsid());
            pkInfoVo.setMemberId1Ext(memberInfo.getName());
        }

        String subMember2 = pkInfoVo.getMemberId2().split("_")[1];
        if (memberInfoMap.containsKey(subMember2)) {
            MemberInfo memberInfo = memberInfoMap.get(subMember2);
            pkInfoVo.setMemberId2(subMember2);
            pkInfoVo.setMember2Sid(memberInfo.getSid());
            pkInfoVo.setMemberId2Ext(memberInfo.getName());
        }

        return pkInfoVo;
    }

    /**
     * 是否展示挂件
     */
    public boolean isShowPkLayer(PkFireComponentAttr attr, Date now) {
        //5.14-5.20号每晚 21:00~1:59
        Date beginDate = DateUtil.getDate(attr.getShowLayerBeginDate(), DateUtil.PATTERN_TYPE5);
        Date beginTime = DateUtil.addHours(beginDate, attr.getShowLayerBeginHour());

        Date endDate = DateUtil.getDate(attr.getShowLayerEndDate(), DateUtil.PATTERN_TYPE5);
        Date endTime = DateUtil.addHours(endDate, attr.getShowLayeyEndHour());

        if (now.getTime() < beginTime.getTime() || now.getTime() > endTime.getTime()) {
            return false;
        }

        int curHour = DateUtil.getHours(now);
        //非跨天
        if (attr.getShowLayeyEndHour() > attr.getShowLayerBeginHour()) {
            if (curHour < attr.getShowLayerBeginHour() || curHour > attr.getShowLayeyEndHour()) {
                return false;
            }
        }

        //跨天
        if (attr.getShowLayerBeginHour() > attr.getShowLayeyEndHour()) {
            if (curHour < attr.getShowLayerBeginHour() && curHour > attr.getShowLayeyEndHour()) {
                return false;
            }
        }

        return true;
    }


    /***
     * 结算
     */
    private int counter = 0;

    // @Scheduled(cron = "0/1 * * * * ? ")
    public void exec() {
        counter++;
        Long cmptId = this.getComponentId();
        Clock clock = new Clock();
        try {

            Set<Long> actIds = this.getActivityIds();
            if (CollectionUtils.isEmpty(actIds)) {
                log.info("{}) exec skip@cmptId:{}, no activity need process", counter, cmptId);
                return;
            }

            List<ActivityInfoVo> effectActInfos = hdztRankingThriftClient.queryEffectActInfos();
            if (CollectionUtils.isEmpty(effectActInfos)) {
                log.info("{}) exec skip@cmptId:{}, hdzt no activity need process", counter, cmptId);
                return;
            }

            //乱序，让机器能相对均匀的处理任务
            List<Long> effectActIds = effectActInfos.stream().map(ActivityInfoVo::getActId).collect(Collectors.toList());
            Collections.shuffle(effectActIds);
            for (Long actId : effectActIds) {
                if (actIds.contains(actId)) {
                    PkFireComponentAttr attr = this.getUniqueComponentAttr(actId);
                    if (attr == null) {
                        log.warn("{}) exec skip@actId:{} no uniq cmptId:{} cmptUseInx:1 attribute!", counter, actId, cmptId);
                    } else {
                        //活动级别加锁
                        timerSupport.work(makeKey(attr, "settle"), 10, new Runnable() {
                            @Override
                            public void run() {
                                settlePk(attr);
                            }
                        });
                    }
                }
            }

            log.info("{}) exec done@cmptId:{}, effectActIds:{}, actIds:{} {}", counter, cmptId, effectActIds.size(), actIds.size(), clock.tag());
        } catch (Throwable t) {
            log.error("{}) exec exception@cmptId:{}, err:{} {}", counter, cmptId, t.getMessage(), clock.tag(), t);
        }
    }

    /**
     * pk结算,每秒一次
     */
    public void settlePk(PkFireComponentAttr attr) {
        //拉取正在进行pk中的场次
        String key = buildPkScoreKey(attr);
        Date now = commonService.getNow(attr.getActId());
        long nowMillSeconds = now.getTime();

        //获取结束时间已经到了，需要进行结算的pk场次
        List<PkInfoVo> needSettle = Lists.newArrayList();
        String groupCode = getRedisGroupCode(attr.getActId());
        Set<ZSetOperations.TypedTuple<String>> pkMembers = actRedisDao.zrange(groupCode, key, attr.getQueryPkHisMaxCount());
        Set<Long> needSettleSidSet = Sets.newHashSet();
        for (ZSetOperations.TypedTuple<String> item : pkMembers) {
            String memberId = item.getValue();
            String[] array = memberId.split("_");
            long sid = Convert.toLong(array[0], 0);
            if (needSettleSidSet.contains(sid)) {
                continue;
            }
            needSettleSidSet.add(sid);

            String pkInfoKey = buildStartPkInfoKey(attr, sid);
            Map<Object, Object> pkInfos = actRedisDao.hGetAll(groupCode, pkInfoKey);
            for (Object infoObject : pkInfos.values()) {
                PkInfoVo pkInfoVo = JSON.parseObject(String.valueOf(infoObject), PkInfoVo.class);
                if (nowMillSeconds > pkInfoVo.getPkEndTime()) {
                    needSettle.add(pkInfoVo);
                }
            }
        }

        //没有需要结算的pk场次
        if (CollectionUtils.isEmpty(needSettle)) {
            return;
        }

        log.info("begin settle,size:{}", needSettle.size());
        for (PkInfoVo pkInfoVo : needSettle) {
            String memberId1 = pkInfoVo.getMemberId1();
            String[] array1 = memberId1.split("_");
            long sid1 = Convert.toLong(array1[0], 0);
            String tuanId1 = array1[1];

            String memberId2 = pkInfoVo.getMemberId2();
            String[] array2 = memberId2.split("_");
            long sid2 = Convert.toLong(array2[0], 0);
            String tuanId2 = array2[1];

            Date pkTime = new Date(pkInfoVo.getPkStartTime());

            String pkScoreKey = buildPkScoreKey(attr);
            String startPkInfoKey = buildStartPkInfoKey(attr, sid1);
            String acptPkInfoKey = buildAcptPkInfoKey(attr, sid2);
            String pkHisKey = buildHisRankKey(attr, DateUtil.format(pkTime, DateUtil.PATTERN_TYPE2));

            log.info("begin settle,pkInfo:{}", JSON.toJSONString(pkInfoVo));

            String succeedMember = executeSettle(attr.getActId(), pkInfoVo.getPkId(), pkScoreKey, startPkInfoKey, acptPkInfoKey, pkHisKey, memberId1, memberId2, tuanId1, tuanId2, nowMillSeconds);

            log.info("settle done,pk id:{},pkScoreKey:{}, startPkInfoKey:{}, acptPkInfoKey:{}, pkHisKey:{}, memberId1:{}, memberId2:{}, nowMillSeconds:{},ret:{}"
                    , pkInfoVo.getPkId(), pkScoreKey, startPkInfoKey, acptPkInfoKey, pkHisKey, memberId1, memberId2, nowMillSeconds, succeedMember);


            threadPoolManager.get(Const.IMPORTANT_POOL).execute(new Runnable() {
                @Override
                public void run() {
                    if (StringUtil.isBlank(succeedMember)) {
                        log.error("settle failed,actId:{},pkInfoVo:{}", attr.getActId(), JSON.toJSONString(pkInfoVo));
                        return;
                    }
                    if ("-1".equals(succeedMember)) {
                        log.warn("score not exist,actId:{},pkInfoVo:{}", attr.getActId(), JSON.toJSONString(pkInfoVo));
                        return;
                    }

                    String winnerSubMember = "";
                    String failedSubMember = "";
                    if (memberId1.equals(succeedMember)) {
                        winnerSubMember = tuanId1;
                        failedSubMember = tuanId2;
                    } else {
                        winnerSubMember = tuanId2;
                        failedSubMember = tuanId1;
                    }
                    log.info("broPkResult begin,succeedMember:{},pkInfo:{}", succeedMember, JSON.toJSONString(pkInfoVo));
                    broPkResult(attr, winnerSubMember, failedSubMember);

                }
            });
        }

    }

    /**
     * 开始pk广播
     */
    public void broStartPk(PkFireComponentAttr attr, String tuan1, String tuan2) {


        Map<String, MemberInfo> memberInfoMap = memberInfoService.getPwTeamMap(Lists.newArrayList(tuan1, tuan2));
        String startAsId = "";
        String startNick = "";
        String acptAsId = "";
        String acptNick = "";
        if (memberInfoMap.containsKey(tuan1)) {
            MemberInfo memberInfo = memberInfoMap.get(tuan1);
            startAsId = StringUtil.isBlank(memberInfo.getAsid()) ? memberInfo.getSid() : memberInfo.getAsid();
            startNick = memberInfo.getName();
        }
        if (memberInfoMap.containsKey(tuan2)) {
            MemberInfo memberInfo = memberInfoMap.get(tuan2);
            acptAsId = StringUtil.isBlank(memberInfo.getAsid()) ? memberInfo.getSid() : memberInfo.getAsid();
            acptNick = memberInfo.getName();
        }


        String tips = "<p><span style='color: #ffdc3a'>##startAsid##【##startNick##】</span>对<span style='color: #ffdc3a'>##acptAsid##【##acptNick##】</span>发起宣战！今日对决风云再起，快快前来观战！</p>";
        tips = tips.replace("##startAsid##", startAsId);
        tips = tips.replace("##startNick##", startNick);
        tips = tips.replace("##acptAsid##", acptAsId);
        tips = tips.replace("##acptNick##", acptNick);
        log.info("broStartPk begin,actId:{},msg:{}", attr.getActId(), tips);

        GameecologyActivity.Act202010_MatchResultBroadcast.Builder reuslt = GameecologyActivity.Act202010_MatchResultBroadcast.newBuilder();
        reuslt.setModuleType(0);
        reuslt.setMatchType(0);
        reuslt.setAsid(0);
        reuslt.setContent(tips);
        reuslt.setActId(attr.getActId());
        reuslt.setSkipFlag(0);
        reuslt.setExtjson("");
        GameecologyActivity.GameEcologyMsg msg =
                GameecologyActivity.GameEcologyMsg.newBuilder()
                        .setUri(GameecologyActivity.PacketType.kAct202010_MatchResultBroadcast_VALUE)
                        .setAct202010MatchResultBroadcast(reuslt.build()).build();

        svcSDKService.broadcastAllChanelsInPW(attr.getActId(), msg);
        log.info("broStartPk done,actId:{},msg:{}", attr.getActId(), tips);

    }

    /**
     * pk结束广播
     */
    public void broPkResult(PkFireComponentAttr attr, String tuan1, String tuan2) {

        Map<String, MemberInfo> memberInfoMap = memberInfoService.getPwTeamMap(Lists.newArrayList(tuan1, tuan2));
        String tuan1AsId = "";
        String tuan1Nick = "";
        String tuan2AsId = "";
        String tuan2Nick = "";
        if (memberInfoMap.containsKey(tuan1)) {
            MemberInfo memberInfo = memberInfoMap.get(tuan1);
            tuan1AsId = StringUtil.isBlank(memberInfo.getAsid()) ? memberInfo.getSid() : memberInfo.getAsid();
            tuan1Nick = memberInfo.getName();
        }
        if (memberInfoMap.containsKey(tuan2)) {
            MemberInfo memberInfo = memberInfoMap.get(tuan2);
            tuan2AsId = StringUtil.isBlank(memberInfo.getAsid()) ? memberInfo.getSid() : memberInfo.getAsid();
            tuan2Nick = memberInfo.getName();
        }


        String tips = "<p><span style='color: #ffdc3a'>##winnerAsId##【##winnerNick##】</span>击败<span style='color: #ffdc3a'>##failedAsId##【##failedNick##】</span>，获得宣战对决胜利，所向披靡！</p>";
        tips = tips.replace("##winnerAsId##", tuan1AsId);
        tips = tips.replace("##winnerNick##", tuan1Nick);
        tips = tips.replace("##failedAsId##", tuan2AsId);
        tips = tips.replace("##failedNick##", tuan2Nick);

        log.info("broPkResult begin,actId:{},msg:{}", attr.getActId(), tips);

        GameecologyActivity.Act202010_MatchResultBroadcast.Builder reuslt = GameecologyActivity.Act202010_MatchResultBroadcast.newBuilder();
        reuslt.setModuleType(0);
        reuslt.setMatchType(0);
        reuslt.setAsid(0);
        reuslt.setContent(tips);
        reuslt.setActId(attr.getActId());
        reuslt.setSkipFlag(0);
        reuslt.setExtjson("");
        GameecologyActivity.GameEcologyMsg msg =
                GameecologyActivity.GameEcologyMsg.newBuilder()
                        .setUri(GameecologyActivity.PacketType.kAct202010_MatchResultBroadcast_VALUE)
                        .setAct202010MatchResultBroadcast(reuslt.build()).build();

        svcSDKService.broadcastAllChanelsInPW(attr.getActId(), msg);
        log.info("broPkResult done,actId:{},msg:{}", attr.getActId(), tips);


        //横幅广播，和龙龙对接
        reuslt.setModuleType(999);
        reuslt.setMatchType(10001);
        String content = String.format("{\"winner\":\"%s\",\"loster\":\"%s\"}", tuan1Nick, tuan2Nick);
        reuslt.setContent(content);
        msg =
                GameecologyActivity.GameEcologyMsg.newBuilder()
                        .setUri(GameecologyActivity.PacketType.kAct202010_MatchResultBroadcast_VALUE)
                        .setAct202010MatchResultBroadcast(reuslt.build()).build();

        svcSDKService.broadcastAllChanelsInPW(attr.getActId(), msg);
        log.info("broPkResult done,actId:{},msg:{}", attr.getActId(), tips);
    }


    /**
     * 执行redis 结算
     * <p>
     * 删除正在pk的分
     * 删除发起pk、接收pk的历史
     * 把pk信息放到历史数据
     *
     * @param pkScoreKey
     * @param startPkInfoKey
     * @param acptPkInfoKey
     * @param pkHisKey
     * @param startPkMember
     * @param acptPkMember
     * @param now
     */
    public String executeSettle(long actId, String pkId, String pkScoreKey, String startPkInfoKey, String acptPkInfoKey, String pkHisKey
            , String startPkMember, String acptPkMember, String startPkSubMember, String acptPkSubMember, long now) {

        try {
            List<String> keys = Lists.newArrayList(pkScoreKey, startPkInfoKey, acptPkInfoKey, pkHisKey);
            List<String> args = Lists.newArrayList(startPkMember, acptPkMember,
                    startPkSubMember, acptPkSubMember
                    , now + "");
            String groupCode = getRedisGroupCode(actId);
            return actRedisDao.executeLua(groupCode, "pkFireComponentSettle.lua", String.class, keys, args);
        } catch (Exception e) {
            log.error("executeSettle error,pk id:{},e:{}", pkId, e.getMessage(), e);
            return null;
        }

    }

    private long getPwTuanSid(long actId, String tuanId) {
        EnrollmentInfo enrollmentInfo = enrollmentService.getFirstEnrolMember(actId, Convert.toLong(BusiId.PEI_WAN.getValue()), RoleType.PWTUAN.getValue(), tuanId);
        if (enrollmentInfo == null) {
            log.error("getPwTuanSid enrollmentInfo is null,actId:{},memberId:{}", actId, tuanId);
            return -1;
        }
        return enrollmentInfo.getSignSid();
    }

    /**
     * 组pk卡余额key
     *
     * @param keySuffix key后缀，团的时候，用公会作为后缀可快速查询出当前公会所有团的余额
     */
    private String buildBalanceKey(PkFireComponentAttr attr, Date now, String keySuffix) {
        String key = String.format(PK_CARD_BALANCE_HASH_KEY, keySuffix, DateUtil.format(now, DateUtil.PATTERN_TYPE2));
        return makeKey(attr, key);
    }

    /**
     * 组被pk成员资格key
     */
    private String buildAcptPkMemberKey(PkFireComponentAttr attr, Date now) {
        String key = String.format(ACPT_PK_LIST_KEY, DateUtil.format(now, DateUtil.PATTERN_TYPE2));
        return makeKey(attr, key);
    }

    /**
     * 发起pk,正在pk的信息
     */
    private String buildStartPkInfoKey(PkFireComponentAttr attr, long sid) {
        return makeKey(attr, String.format(PK_START_INFO_KEY, sid));
    }

    /**
     * 接收pk,正在pk的信息
     */
    private String buildAcptPkInfoKey(PkFireComponentAttr attr, long sid) {
        return makeKey(attr, String.format(PK_ACPT_INFO_KEY, sid));
    }

    /**
     * 已结算的pk数据key
     */
    private String buildHisRankKey(PkFireComponentAttr attr, String dateStr) {
        return makeKey(attr, String.format(PK_HISTORY_LIST_KEY, dateStr));
    }

    /**
     * 正在pk的分数key
     */
    private String buildPkScoreKey(PkFireComponentAttr attr) {
        return makeKey(attr, PK_SCORE_RANK_KEY);
    }


}
