package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserEnterTemplateEvent;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.DelaySvcSDKServiceV2;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.consts.PBCommonNoticeType;
import com.yy.gameecology.common.consts.SvcSdkExtKey;
import com.yy.gameecology.common.consts.WebdbHost;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.WebUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.CommonPBOperateRequest;
import com.yy.gameecology.hdzj.bean.CommonPBOperateResp;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.MeetGiftComponentAttr;
import com.yy.gameecology.hdzj.element.component.service.MeetGiftService;
import com.yy.protocol.pb.GameecologyActivity;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Date;
import java.util.Map;
import java.util.Set;

@Slf4j
@RestController
@RequestMapping("5146")
public class MeetGiftComponent extends BaseActComponent<MeetGiftComponentAttr> {

    private static final String JUMP_ROOM_KEY = "jump_room";

    private static final String FIRST_ENTER_CHANNEL = "first_enter_channel";

    @Autowired
    private MeetGiftService meetGiftService;

    @Autowired
    private DelaySvcSDKServiceV2 delaySvcSDKServiceV2;

    @Autowired
    private CommonDataDao commonDataDao;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;
    @Autowired
    private RetryTool retryTool;

    @Override
    public Long getComponentId() {
        return ComponentId.MEET_PRESENT;
    }

    /**
     * 进频道，发奖
     * @param event
     * @param attr
     */
    @HdzjEventHandler(value = UserEnterTemplateEvent.class, canRetry = true)
    public void onUserEnterTemplate(UserEnterTemplateEvent event, MeetGiftComponentAttr attr) {
        log.info("onUserEnterTemplate with event:{} actId:{}", event, attr.getActId());
        if (event.getHostName() != WebdbHost.tieba) {
            return;
        }

        Date now = commonService.getNow(attr.getActId());
        if (!inPlayTime(attr, now)) {
            log.info("onUserEnterTemplate skip not in play time uid:{} sid:{} ssid:{} actId:{}", event.getUid(), event.getSid(), event.getSsid(), attr.getActId());
            return;
        }

        final long uid = event.getUid();
        final String hdid = event.getHdid();
        long remaining = meetGiftService.queryRemaining(attr, now);
        if (remaining <= 0) {
            log.info("onUserEnterTemplate skip remaining not enough, uid:{} hdid:{} sid:{} ssid:{} actId:{}", uid, hdid, event.getSid(), event.getSsid(), attr.getActId());
            return;
        }

        boolean received = meetGiftService.queryUserReceived(attr, uid, hdid);
        if (received) {
            log.info("onUserEnterTemplate skip received, uid:{} hdid:{} sid:{} ssid:{} actId:{}", uid, hdid, event.getSid(), event.getSsid(), attr.getActId());
            return;
        }

        final long sid = event.getSid(), ssid = event.getSsid();
        String expected = sid + ":" + ssid;

        // 首次进入则直接发弹窗
        boolean set = commonDataDao.hashValueSetNX(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), FIRST_ENTER_CHANNEL, String.valueOf(uid), expected);
        if (set) {
            log.info("onUserEnterTemplate sendNotice uid:{} hdid:{} sid:{} ssid:{} actId:{}", uid, hdid, event.getSid(), event.getSsid(), attr.getActId());
            sendAwardPopupNotice(attr, uid);
        } else {
            log.info("onUserEnterTemplate skip setnx failed uid:{} hdid:{} sid:{} ssid:{} actId:{}", uid, hdid, event.getSid(), event.getSsid(), attr.getActId());
        }
//
//        // 在外面点过领取的，也发弹窗
//        String value = commonDataDao.hashValueGet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), JUMP_ROOM_KEY, String.valueOf(uid));
//        if (!StringUtils.equals(expected, value)) {
//            log.warn("onUserEnterTemplate sid:ssid not equals uid:{} sid:{} ssid:{}", uid, sid, ssid);
//            return;
//        }

//        sendAwardPopupNotice(attr, uid);
    }

    private void sendAwardPopupNotice(MeetGiftComponentAttr attr, long uid) {
        var giftProps = getGiftProps(attr);
        String noticeValue = JSON.toJSONString(Map.of("awardName", giftProps.getLeft(), "awardIcon", giftProps.getRight()));
        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(attr.getActId())
                .setNoticeType(PBCommonNoticeType.MEET_GIFT_NOTICE)
                .setNoticeValue(noticeValue);

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();
        delaySvcSDKServiceV2.unicastUid(uid, msg, attr.getDelay().toSeconds());
    }

    @NeedRecycle(author = "liqingyang", notRecycle = true)
    @Scheduled(cron = "7 0 * * * ?")
    public void meetGiftStat() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                continue;
            }

            var attr = tryGetUniqueComponentAttr(actId);
            Date now = commonService.getNow(actId);
            Date prev = DateUtils.addMinutes(now, -5);
            if (!inPlayTime(attr, prev)) {
                continue;
            }

            Map<String, String> entries = commonDataDao.hashGetAll(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), MeetGiftService.CONSUMED_KEY);
            String prevDate = DateFormatUtils.format(prev, DateUtil.PATTERN_TYPE2);
            long preCount = 0;
            long totalCount = 0;
            for (var entry : entries.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                if (StringUtils.isNumeric(value)) {
                    long count = Long.parseLong(value);
                    totalCount += count;

                    if (StringUtils.equals(prevDate, key)) {
                        preCount = count;
                    }
                }
            }

            String message = String.format("1）上一小时发放礼物数：%d\n（2）截止当前，玩法累计发放礼物数：%d", preCount, totalCount);
            String msg = buildActRuliuMsg(attr.getActId(), false, "见面好礼", message);
            baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_ACT_TURNOVER, msg, Collections.emptyList());
        }
    }

    @Override
    public CommonPBOperateResp commonOperatePbRequest(CommonPBOperateRequest request) {
        log.info("commonOperatePbRequest with ext:{}", request.getExt());
        String hostIdStr = MapUtils.getString(request.getExt(), SvcSdkExtKey.EXKEY_HOSTID.getCode());
        int hostId = Convert.toInt(hostIdStr);
        if (hostId != WebdbHost.tieba.getHostId()) {
            return new CommonPBOperateResp(400, "", "本活动只能在贴吧App中参与");
        }
        long uid = request.getOpUid(), sid = request.getOpSid(), ssid = request.getOpSsid();
        var attr = getComponentAttr(request.getActId(), request.getCmptIndex());
        if (attr == null) {
            return new CommonPBOperateResp(400, "", "参数错误");
        }

        if (sid <= 0 || ssid <= 0) {
            return new CommonPBOperateResp(500, "", "请在频道内点击领取");
        }

        Date now = commonService.getNow(attr.getActId());

        if (!inPlayTime(attr, now)) {
            return new CommonPBOperateResp(400, "", "不在活动时间内，请在活动时间内参与");
        }

        final String hdid = request.getHdid();
        Integer sent = meetGiftService.trySendMeetGift(attr, uid, hdid, sid, ssid, now);
        if (sent == null) {
            return new CommonPBOperateResp(500, "", "服务器正忙，请稍后再试");
        }

        return switch (sent) {
            case 0 -> {
                var giftProps = getGiftProps(attr);
                yield new CommonPBOperateResp(0, JSON.toJSONString(Map.of("awardName", giftProps.getLeft(), "awardIcon", giftProps.getRight())), "领取成功");
            }
            case 1 -> new CommonPBOperateResp(462, "", "已经领取过了");
            case 2 -> new CommonPBOperateResp(463, "", "库存不足");
            default -> new CommonPBOperateResp(500, "", "服务器正忙，请稍后再试");
        };
    }

    /**
     * 获取礼物信息
     * @param attr
     * @return pair.left -> giftName; pair.right -> giftIcon
     */
    private Pair<String, String> getGiftProps(MeetGiftComponentAttr attr) {
        var awardTasks = hdztAwardServiceClient.queryAwardTasks(attr.getTAwardTskId(), false);
        String giftName = StringUtils.EMPTY, giftIcon = StringUtils.EMPTY;
        if (awardTasks != null && awardTasks.get(attr.getTAwardPkgId()) != null) {
            giftName = awardTasks.get(attr.getTAwardPkgId()).getPackageName();
            giftIcon = awardTasks.get(attr.getTAwardPkgId()).getPackageImage();
        }

        return Pair.of(giftName, giftIcon);
    }

    @GetMapping("room")
    public Response<ChannelInfo> queryJumpRoom(@RequestParam(name = "actId") long actId,
                                               @RequestParam(name = "cmptInx", required = false, defaultValue = "810") long cmptInx) {

//        long uid = getLoginYYUid();
//        if (uid <= 0) {
//            return Response.fail(401, "login is required");
//        }
        var attr = getComponentAttr(actId, cmptInx);

        if (CollectionUtils.isEmpty(attr.getChannels())) {
            return Response.fail(500, "暂不支持领取");
        }

        int index = RandomUtils.nextInt(0, attr.getChannels().size());
        ChannelInfo channelInfo = attr.getChannels().get(index);

//        commonDataDao.hashValueSet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), JUMP_ROOM_KEY, String.valueOf(uid), channelInfo.getSid() + ":" + channelInfo.getSsid());
        log.info("queryJumpRoom with sid:{} ssid:{}", channelInfo.getSid(), channelInfo.getSsid());

        return Response.success(channelInfo);
    }

    @GetMapping("info")
    public Response<MeetGiftInfo> queryMeetGiftInfo(@RequestParam(name = "actId") long actId,
                                                    @RequestParam(name = "cmptInx", required = false, defaultValue = "810") long cmptInx) {

        var attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        long uid = getLoginYYUid();
        Date now = commonService.getNow(attr.getActId());
        long remaining = meetGiftService.queryRemaining(attr, now);
        var giftProps = getGiftProps(attr);
        if (uid <= 0) {
            return Response.success(new MeetGiftInfo(giftProps.getLeft(), giftProps.getRight(), false, remaining));
        }

        if (!inPlayTime(attr, now)) {
            return Response.success(new MeetGiftInfo(giftProps.getLeft(), giftProps.getRight(), false, 0));
        }

        var clientInfo = WebUtil.getClientInfo();
        boolean received = meetGiftService.queryUserReceived(attr, uid, clientInfo.hdid());

        return Response.success(new MeetGiftInfo(giftProps.getLeft(), giftProps.getRight(), received, remaining));
    }

    private boolean inPlayTime(MeetGiftComponentAttr attr, Date now) {
        ActivityInfoVo activityInfoVo = actInfoService.queryActivityInfo(attr.getActId());
        Assert.notNull(activityInfoVo, "activityInfoVo is null");
        Date startTime = new Date(activityInfoVo.getBeginTime()), endTime = new Date(activityInfoVo.getEndTime());
        if (attr.getStartTime() != null) {
            startTime = attr.getStartTime();
        }

        if (attr.getEndTime() != null) {
            endTime = attr.getEndTime();
        }

        return now.after(startTime) && now.before(endTime);
    }

    @Getter
    @Setter
    public static class MeetGiftInfo {
        protected String awardName;

        protected String awardIcon;

        protected boolean received;

        protected long remaining;

        public MeetGiftInfo() {
        }

        public MeetGiftInfo(String awardName, String awardIcon, boolean received, long remaining) {
            this.awardName = awardName;
            this.awardIcon = awardIcon;
            this.received = received;
            this.remaining = remaining;
        }
    }
}
