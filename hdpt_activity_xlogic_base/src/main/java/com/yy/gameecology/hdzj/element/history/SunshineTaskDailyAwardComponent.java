package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.bean.actlayer.BabyMissionItem;
import com.yy.gameecology.activity.bean.actlayer.TaskItem;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.client.thrift.FtsBaseInfoBridgeClient;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.exception.BadRequestException;
import com.yy.gameecology.activity.exception.ParameterException;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.BigDataService;
import com.yy.gameecology.activity.service.BroadCastHelpService;
import com.yy.gameecology.activity.service.EnrollmentNewService;
import com.yy.gameecology.common.bean.PairBean;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.consts.BigDataScoreType;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.db.model.gameecology.ActTaskScore;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.AnchorTaskScoreInfo;
import com.yy.gameecology.hdzj.bean.BroadcastConfig;
import com.yy.gameecology.hdzj.bean.SunShineTaskAwardInvoke;
import com.yy.gameecology.hdzj.bean.UserAwardInfo;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.history.attr.SunshineTaskDailyAwardComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ！！！注意，因为gameecology.act_task_score 数据没有按照组件实例区分，影响到结算定时器不能按照监听榜单结束事件结算等等，1个活动中，本组件只能有1个实例！！！！！
 * 突破任务：后一天的任务数值依赖于前一天的数值
 * <p>
 * A. 个人每日任务
 * 1. 监听榜单分值改变事件 RankingScoreChanged
 * 2. 读取个人每日任务,顺序为：redis --> mysql --> 基于模板创建
 * 3. 计算任务完成情况(上次的榜单分值小于任务完成值,并且本次的榜单分值大于任务完成值,则认定该任务完成)
 * 4. 有完成的任务时,更新mysql
 * 5. 读取已完成任务,保存到redis中
 * <p>
 * B. 全站任务(个人 & 角色)
 * 1. 监听榜单分值改变事件 RankingScoreChanged
 * 2. 基于任务配置计算任务完成情况(上次的榜单分值小于任务完成值,并且本次的榜单分值大于任务完成值,则认定该任务完成)
 * 3. 有完成的任务,记录到MySQL中
 * 4. 任务完成横幅广播
 * 5. 记录发奖记录到redis
 * <p>
 * C. 主播第二天的过任务数值
 * 1. 监听榜单结束事件
 * 2. 读取上一天完成了个人二级任务的主播,已上一天的榜单分值作为今天二级任务的完成值
 * 3. 基于上一天的个人任务完成情况,初始化任务数据
 * 4. 将用户的个人任务数据缓存到redis
 * <p>
 * D. 定时器结算上一天的任务奖励
 * 1. 从数据库中读取上一天已完成的任务,按完成时间升序
 * 2. 计算奖励数额：每日限额/总限额超出则不处理,只记录redis,奖励数值还需要剔除掉【交友乱斗宝箱怪】玩法的奖励
 * 3. 获取对应业务的奖包,调用中台发奖接口进行发奖
 * <p>
 * E. H5接口
 * 1. 获取总奖池额度
 * 2. 查询个人突破+全站突破任务信息
 * 3. 查询任务完成列表
 *
 * <AUTHOR>
 * @date 2021.10.15 10:54
 */
@Deprecated
@Component
public class SunshineTaskDailyAwardComponent extends BaseActComponent<SunshineTaskDailyAwardComponentAttr> {

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private EnrollmentNewService enrollmentNewService;

    @Autowired
    private BroadCastHelpService broadCastHelpService;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    private BigDataService bigDataService;

    @Autowired
    private FtsBaseInfoBridgeClient ftsBaseInfoBridgeClient;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;


    @Override
    public Long getComponentId() {
        return ComponentId.SUNSHINE_TASK_DAILY_AWARD;
    }

    /**
     * 总奖池
     */
    private static final String TOTAL_AWARD_POOL_KEY = "award_pool:total";

    /**
     * 奖池用完了未发放记录
     * %s:%s:%s taskTime:uid:taskType:level
     */
    private static final String POOL_OUT_KEY = "award_pool:out:%s:%s:%s:%s";

    /**
     * 主播任务缓存key
     * hash anchor_task_score_list:time  {uid_type:list<task>}  hash  time-> yyyyMMdd
     */
    private static final String ANCHOR_TASK_SCORE_LIST = "anchor_task_score_list:%s";

    /**
     * yb奖励记录 anchor_task_finish_award  {memberId:task}  hash
     */
    private static final String ANCHOR_TASK_FINISH_AWARD = "anchor_task_finish_award";

    //private Map<Long, Map<String, List<ActTaskScore>>> taskScoreMap = new HashMap<>();

    //个人任务
    private static final String SINGLE = "single";
    //全站个人
    private static final String ALL_SINGLE = "all_single";
    //全站天团
    private static final String ALL_TIANTUAN = "all_tiantuan";

    //没有个人突破任务的占位数据
    private static final String EMPTY_DAILY_TASK = "{}";

    /**
     * 个人突破任务2级
     */
    private static final int SINGLE_LEVEL2 = 2;


    /**
     * 需要结算发奖的任务类型
     */
    private final List<String> ALL_AWARD_TASK = Lists.newArrayList(SINGLE, ALL_SINGLE, ALL_TIANTUAN);


    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = false)
    public void onRankingScoreChanged(RankingScoreChanged event, SunshineTaskDailyAwardComponentAttr attr) {
        if (event.getRankId() != attr.getRankId() || event.getPhaseId() != attr.getPhaseId()) {
            return;
        }

        if (SysEvHelper.isDev()) {
            log.info("log SunshineTaskComponent handlerTaskEvent,event:{}", JSON.toJSONString(event));
        }

        long actId = event.getActId();
        //榜单更新时间
        Date rankUpdateDate = DateUtil.getDate(event.getOccurTime());
        String member = event.getMember();

        //个人高光任务
        handleSingleTaskScore(event, attr, actId, member, rankUpdateDate);

        //全站高光任务
        handleAllTaskScore(event, attr, actId, member, rankUpdateDate);

    }


    private void handleSingleTaskScore(RankingScoreChanged event, SunshineTaskDailyAwardComponentAttr attr, long actId, String member, Date rankUpdateDate) {

        String taskTime = DateUtil.format(rankUpdateDate, DateUtil.PATTERN_TYPE2);
        //获取主播任务列表
        long uid = Convert.toLong(member);
        List<ActTaskScore> anchorDailyTask = getOrCreateAnchorDailyTask(actId, uid, taskTime, attr, true);
        if (anchorDailyTask.isEmpty()) {
            return;
        }
        long rankScore = event.getRankScore();
        List<ActTaskScore> completedList = calculateAnchorTask(anchorDailyTask, rankScore, event.getItemScore());
        if (completedList.isEmpty()) {
            log.info("log SunshineTaskComponent onRankingScoreChanged no single task finish -> uid:{}, itemId:{} addScore:{}, total:{}",
                    event.getItemId(), uid, event.getItemScore(), event.getRankScore());
            return;
        }
        setupTask(completedList, uid, rankUpdateDate, taskTime);

        //更新数据库
        int[] ints = gameecologyDao.batchUpdateTaskScore(completedList);

        for (ActTaskScore actTaskScore : completedList) {
            bro(attr, uid, actTaskScore);
        }
        //记录奖励
        String groupCode = redisConfigManager.getGroupCode(actId);
        ActTaskScore actTaskScore = new ActTaskScore();
        actTaskScore.setCompleted(1L);
        actTaskScore.setUid(uid);
        actTaskScore.setActId(actId);
        List<ActTaskScore> taskScores = gameecologyDao.queryAnchorTasks(actTaskScore);
        actRedisDao.hset(groupCode, Const.addActivityPrefix(actId, ANCHOR_TASK_FINISH_AWARD), member, JSON.toJSONString(taskScores));

        log.info("log SunshineTaskComponent handleTaskScore uid finish single task, uid:{}, updateResult:{} task:{}",
                member, ints, JSON.toJSONString(completedList));
    }

    private void handleAllTaskScore(RankingScoreChanged event, SunshineTaskDailyAwardComponentAttr attr, long actId, String member, Date rankUpdateDate) {

        String taskTime = DateUtil.format(rankUpdateDate, DateUtil.PATTERN_TYPE2);
        //不在开启的时间里
        List<String> list = attr.getOpenTimeMap().get(ALL_SINGLE);
        if (!list.contains(taskTime)) {
            return;
        }
        //获取主播任务列表
        EnrollmentInfo memberInfo = enrollmentNewService.tryGetFirstEnrolMemberCache(actId, 0L, RoleType.ANCHOR.getValue(), member);
        long roleId = memberInfo == null ? 0 : memberInfo.getDestRoleId();
        String taskJson;
        //天团或者未上报用户
        if (attr.getAllTaskTTRoleId().contains(roleId)) {
            taskJson = attr.getDefaultTaskScore().get(ALL_TIANTUAN);
        } else {
            taskJson = attr.getDefaultTaskScore().get(ALL_SINGLE);
        }
        List<ActTaskScore> taskConfig = JSON.parseArray(taskJson, ActTaskScore.class);
        long rankScore = event.getRankScore();
        List<ActTaskScore> completedList = calculateAnchorTask(taskConfig, rankScore, event.getItemScore());
        if (completedList.isEmpty()) {
            log.info("log SunshineTaskComponent  onRankingScoreChanged no all task finish -> uid:{},item:{} addScore:{}, total:{}",
                    event.getItemId(), member, event.getItemScore(), event.getRankScore());
            return;
        }

        //补充字段
        long uid = Convert.toLong(member);

        setupTask(completedList, uid, rankUpdateDate, taskTime);

        //全站任务直接入库, 更新数据库
        gameecologyDao.batchInsertActTaskScore(completedList);

        //发广播
        for (ActTaskScore actTaskScore : completedList) {
            bro(attr, uid, actTaskScore);
        }

        //记录奖励
        String groupCode = redisConfigManager.getGroupCode(actId);
        //更新获奖记录
        ActTaskScore actTaskScore = new ActTaskScore();
        actTaskScore.setCompleted(1L);
        actTaskScore.setUid(uid);
        actTaskScore.setActId(actId);
        List<ActTaskScore> taskScores = gameecologyDao.queryAnchorTasks(actTaskScore);
        actRedisDao.hset(groupCode, Const.addActivityPrefix(actId, ANCHOR_TASK_FINISH_AWARD), member, JSON.toJSONString(taskScores));
        log.info("log SunshineTaskComponent  done, finish all task -> event:{}, tasks:{}", event, JSON.toJSONString(completedList));
    }


    private void setupTask(List<ActTaskScore> actTaskScores, long uid, Date updateTime, String taskTime) {
        for (ActTaskScore actTaskScore : actTaskScores) {
            actTaskScore.setuTime(updateTime);
            actTaskScore.setUid(uid);
            actTaskScore.setTaskTime(taskTime);
        }
    }


    /**
     * 触发过任务常规高光、霸屏等横幅
     */
    private void bro(SunshineTaskDailyAwardComponentAttr attr, long uid, ActTaskScore taskScore) {
        String taskType = taskScore.getTaskType();
        if (!attr.getLevelBro().containsKey(taskType)) {
            return;
        }
        if (!attr.getLevelBro().get(taskType).containsKey(taskScore.getLevel())) {
            return;
        }
        List<BroadcastConfig> configs = attr.getLevelBro().get(taskType).get(taskScore.getLevel());
        if (CollectionUtils.isEmpty(configs)) {
            return;
        }
        for (BroadcastConfig config : configs) {
            log.info("invokeBro begin,actId:{},uid:{},taskType:{},level:{},config:{}", attr.getActId(), uid, taskType, taskScore.getLevel(), JSON.toJSONString(config));
            threadPoolManager.get(Const.IMPORTANT_POOL).execute(new Runnable() {
                @Override
                public void run() {
                    invokeBro(attr, uid, taskScore, config);
                }
            });
        }
    }

    private void invokeBro(SunshineTaskDailyAwardComponentAttr attr, long uid, ActTaskScore taskScore, BroadcastConfig config) {
        String taskType = taskScore.getTaskType();
        //延迟广播
        if (config.getDelayMillSeconds() > 0) {
            log.info("invokeBro delay,actId:{},uid:{},taskType:{},level:{},config:{}", attr.getActId(), uid, taskType, taskScore.getLevel(), JSON.toJSONString(config));
            SysEvHelper.waiting(config.getDelayMillSeconds());
        }

        int broType = config.getBroType();
        String bannerUrl;
        //优先用任务配置,其次用默认
        if (StringUtil.isNotBlank(config.getBannerUrl())) {
            bannerUrl = config.getBannerUrl();
        } else {
            bannerUrl = attr.getDefaultBannerUrl();
        }
        UserBaseInfo userInfo = commonService.getUserInfo(uid, false);
        String nick = userInfo.getNick();
        long actId = attr.getActId();
        Long score = taskScore.getScore();

        String busiId = BusiId.MAKE_FRIEND.getValue() + "";
        String asId = "";
        EnrollmentInfo enrollmentInfo = enrollmentNewService.tryGetFirstEnrolMemberCache(attr.getActId(), 0L, RoleType.ANCHOR.getValue(), uid + "");
        if (enrollmentInfo != null) {
            busiId = enrollmentInfo.getRoleBusiId() + "";
            asId = enrollmentInfo.getSignAsid() + "";
        } else {
            log.error("bro can not get enrollmentInfo,actId:{},memberId:{}", actId, uid + "");
        }

        Map<String, String> extMap = Maps.newHashMap();
        extMap.put("nickName", nick);
        extMap.put("logo", StringUtil.isNotBlank(userInfo.getHdLogo()) ? userInfo.getHdLogo() : userInfo.getLogo());
        extMap.put("value", score + "");
        extMap.put("svgaUrl", bannerUrl);
        extMap.put("award", taskScore.getAward());
        extMap.put("asid", asId);
        extMap.put("ext", config.getExt());
        extMap.put("busiId", busiId);

        int bannerId = config.getBannerId();
        GameecologyActivity.BannerBroadcast bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId).setBannerId(bannerId).setUserNick(nick)
                .setUserScore(score).setJsonData(JSON.toJSONString(extMap)).build();

        GameecologyActivity.GameEcologyMsg bannerBroMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();

        if (broType == BroadcastConfig.BroType.ACT_BUSI.code) {
            broadCastHelpService.broadcast(actId, BusiId.GAME_ECOLOGY, broType, 0L, 0L, bannerBroMsg);
        } else {
            UserCurrentChannel channel = commonService.getUserCurrentChannel(uid);
            if (channel == null) {
                log.info("log SunshineTaskComponent bro done ,user not in channel uid:{} task:{}", uid, JSON.toJSONString(taskScore));
                return;
            }
            broadCastHelpService.broadcast(actId, BusiId.GAME_ECOLOGY, broType, channel.getTopsid(), channel.getSubsid(), bannerBroMsg);
        }
        log.info("log SunshineTaskComponent bro done uid:{} task:{}", uid, JSON.toJSONString(taskScore));

        bigDataService.saveNoRankDataToFile(actId, BusiId.GAME_ECOLOGY, System.currentTimeMillis(), uid + ""
                , RoleType.ANCHOR, score, BigDataScoreType.SUNSHINE_TASK_BANNER, config.getDesc() + "|" + config.getBannerId() + "|" + config.getBroType());
    }


    private List<ActTaskScore> calculateAnchorTask(List<ActTaskScore> scoreList, long total, long score) {
        List<ActTaskScore> completedList = new ArrayList<>();
        //等级升序
        scoreList.sort(Comparator.comparing(ActTaskScore::getLevel));
        long src = total - score;
        for (ActTaskScore actTaskScore : scoreList) {
            Long target = actTaskScore.getScore();
            //完成任务
            if (src < target && total >= target) {
                actTaskScore.setCompleted(1L);
                actTaskScore.setcTime(new Date());
                completedList.add(actTaskScore);
            }
        }
        return completedList;

    }

    /**
     * 读取个人高光任务，如果不存在则创建
     */
    private List<ActTaskScore> getOrCreateAnchorDailyTask(long actId, long uid, String taskTime, SunshineTaskDailyAwardComponentAttr attr, boolean isWait) {
        if (!actInfoService.inActTime(actId)) {
            log.warn("getAnchorDailyTask not in act time, act:{}", actId);
            return Lists.newArrayList();
        }

        //redis 缓存读任务
        String groupCode = redisConfigManager.getGroupCode(actId);
        String dailyTaskCache = actRedisDao.hget(groupCode, Const.addActivityPrefix(actId, String.format(ANCHOR_TASK_SCORE_LIST, taskTime)),
                uid + "");

        //没有个人突破任务
        if (EMPTY_DAILY_TASK.equals(dailyTaskCache)) {
            return Lists.newArrayList();
        }

        if (StringUtil.isNotBlank(dailyTaskCache)) {
            return JSON.parseArray(dailyTaskCache, ActTaskScore.class);
        }

        //redis 缓存读不到，mysql读
        List<ActTaskScore> actTaskScores = gameecologyDao.queryAnchorTasksByTime(actId, uid, taskTime);
        if (CollectionUtils.isNotEmpty(actTaskScores)) {
            actRedisDao.hset(groupCode, Const.addActivityPrefix(actId, String.format(ANCHOR_TASK_SCORE_LIST, taskTime)),
                    uid + "", JSON.toJSONString(actTaskScores));
            return actTaskScores;
        }

        //都读不到就初始化创建任务
        return createAnchorDailyTask(actId, uid, taskTime, attr, isWait);
    }


    private List<ActTaskScore> createAnchorDailyTask(long actId, long uid, String taskTime, SunshineTaskDailyAwardComponentAttr attr, boolean isWait) {
        log.info("createAnchorDailyTask begin actId:{},uid:{},taskTime:{}", actId, uid, taskTime);

        List<ActTaskScore> result;
        //不存在,读取前一天任务信息
        Date date = DateUtil.getDate(taskTime, DateUtil.PATTERN_TYPE2);
        Date lastDay = DateUtil.getAddDay(date, -1);
        String lastDayFormat = DateUtil.format(lastDay, DateUtil.PATTERN_TYPE2);
        List<ActTaskScore> lastDayTaskScores = gameecologyDao.queryAnchorTasksByTime(actId, uid, lastDayFormat);
        //过滤个人任务
        lastDayTaskScores = lastDayTaskScores.stream().filter(x -> SINGLE.equals(x.getTaskType())).collect(Collectors.toList());

        //前一天的也不存在, 则构建默认任务  warn: 若测试时调一天, 则
        if (CollectionUtils.isEmpty(lastDayTaskScores)) {
            List<ActTaskScore> newTaskScores = buildDefaultTaskScore(attr, uid, taskTime);
            //
            if (CollectionUtils.isEmpty(newTaskScores)) {
                actRedisDao.hset(getRedisGroupCode(actId), Const.addActivityPrefix(actId, String.format(ANCHOR_TASK_SCORE_LIST, taskTime)),
                        uid + "", EMPTY_DAILY_TASK);
                return newTaskScores;
            }
            for (ActTaskScore actTaskScore : newTaskScores) {
                actTaskScore.setUid(uid);
                actTaskScore.setActId(actId);
                actTaskScore.setTaskTime(taskTime);
                actTaskScore.setCompleted(0L);
                actTaskScore.setcTime(new Date());
            }
            result = newTaskScores;
        } else {
            for (ActTaskScore actTaskScore : lastDayTaskScores) {
                actTaskScore.setTaskTime(taskTime);
                actTaskScore.setcTime(new Date());
                actTaskScore.setCompleted(0L);
                if (actTaskScore.getLevel() == SINGLE_LEVEL2 && actTaskScore.getCompleted() == 1) {
                    String dateStr = TimeKeyHelper.getTimeCode(attr.getTimeKey(), lastDay);
                    Rank rank = hdztRankingThriftClient.queryPointedMembersRanking(actId, attr.getRankId(), attr.getPhaseId(),
                            dateStr, uid + "", Maps.newHashMap());

                    String singleRate = attr.getAwardRateMap().get(SINGLE);
                    BigDecimal awardRate = new BigDecimal(singleRate);
                    BigDecimal award = awardRate.multiply(new BigDecimal(rank.getScore()));
                    actTaskScore.setAward(award.longValue() + "");
                    actTaskScore.setScore(rank.getScore());
                }
            }
            result = lastDayTaskScores;
        }

        log.info("createAnchorDailyTask actId:{},uid:{},task:{}", actId, uid, JSON.toJSONString(result));
        return save2dbAndCache(actId, uid, taskTime, result, isWait);
    }


    private List<ActTaskScore> save2dbAndCache(long actId, long uid, String taskTime, List<ActTaskScore> actTaskScores, boolean isWait) {
        Clock clock = new Clock();
        String timerName = "task_score_" + uid;
        String groupCode = redisConfigManager.getGroupCode(actId);
        do {
            Secret lock = null;
            try {
                String hget = actRedisDao.hget(groupCode, Const.addActivityPrefix(actId, String.format(ANCHOR_TASK_SCORE_LIST, taskTime)),
                        uid + "");
                if (!StringUtil.isBlank(hget)) {
                    return JSON.parseArray(hget, ActTaskScore.class);
                }

                lock = locker.lock(Const.addActivityPrefix(actId, timerName), 15);
                if (lock != null) {
                    List<ActTaskScore> dbActTaskScores = gameecologyDao.queryAnchorTasksByTime(actId, uid, taskTime);
                    if (dbActTaskScores.isEmpty()) {
                        //更新数据库
                        gameecologyDao.batchInsertActTaskScore(actTaskScores);
                        ActTaskScore taskScore = new ActTaskScore();
                        taskScore.setActId(actId);
                        taskScore.setTaskTime(taskTime);
                        taskScore.setUid(uid);
                        //全站任务单独处理,不需要缓存
                        taskScore.setTaskType(SINGLE);
                        actTaskScores = gameecologyDao.queryAnchorTasks(taskScore);
                        //更新缓存
                        actRedisDao.hset(groupCode, Const.addActivityPrefix(actId, String.format(ANCHOR_TASK_SCORE_LIST, taskTime)),
                                uid + "", JSON.toJSONString(actTaskScores));
                    }
                    break;
                } else if (isWait) {
                    SysEvHelper.waiting(2000);
                }
            } catch (Exception e) {
                log.error("[{}] err:{}", timerName, e.getMessage(), e);
            } finally {
                if (lock != null) {
                    locker.unlock(timerName, lock);
                }
                log.info("log SunshineTaskComponent save2dbAndCache complete,timerName:{},cost:{}", timerName, clock.tag());
            }
        } while (isWait);
        return actTaskScores;
    }


    /**
     * 模板:只有 类型,等级,分数,过关奖励 四个字段 需补充活动ID uid 任务时间 三个字段
     *
     * @param
     * @param attr
     * @return
     */
    private List<ActTaskScore> buildDefaultTaskScore(SunshineTaskDailyAwardComponentAttr attr, long uid, String taskTime) {
        String single = attr.getDefaultTaskScore().get(SINGLE);
        //没有配置的话就没有个人突破任务
        if (StringUtil.isEmpty(single)) {
            return Lists.newArrayList();
        }
        return JSON.parseArray(single, ActTaskScore.class);
    }

    //更新主播第二天的门槛
    //若更新门槛时,有新的分值进入,存在构建默认任务问题
    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = false)
    public void onPhaseTimeEndEvent(PhaseTimeEnd event, SunshineTaskDailyAwardComponentAttr attr) {
        if (event.getRankId() != attr.getRankId()) {
            return;
        }

        long actId = event.getActId();
        Date now = commonService.getNow(actId);
        String taskTime = DateUtil.format(now, DateUtil.PATTERN_TYPE2);

        if (!actInfoService.inActTime(actId)) {
            log.warn("actId:{} is end, will not create taskScore", actId);
            return;
        }
        //等待其他时间完成
        SysEvHelper.waiting(30000);

        log.info("begin batchInitDailyTask,actId:{},event:{},attr:{}", actId, JSON.toJSONString(event), JSON.toJSONString(attr));
        //初始化新的一天任务数据
        batchInitDailyTask(taskTime, event, attr);

        //更新缓存
        String groupCode = redisConfigManager.getGroupCode(actId);
        ActTaskScore query = new ActTaskScore();
        query.setActId(actId);
        query.setTaskTime(taskTime);
        List<ActTaskScore> cacheList = gameecologyDao.queryAnchorTasks(query);
        Map<Long, List<ActTaskScore>> map = cacheList.stream().collect(Collectors.groupingBy(ActTaskScore::getUid));
        for (Map.Entry<Long, List<ActTaskScore>> entry : map.entrySet()) {
            actRedisDao.hset(groupCode, Const.addActivityPrefix(actId, String.format(ANCHOR_TASK_SCORE_LIST, taskTime)),
                    entry.getKey() + "", JSON.toJSONString(entry.getValue()));
        }
    }

    private void batchInitDailyTask(String taskTime, PhaseTimeEnd event, SunshineTaskDailyAwardComponentAttr attr) {
        Clock clock = new Clock();
        long actId = event.getActId();
        Date date = DateUtil.getDate(event.getEndTime(), DateUtil.DEFAULT_PATTERN);
        String lastDayFormat = DateUtil.format(date, DateUtil.PATTERN_TYPE2);
        //初始化前一天完成了二级任务的主播
        List<ActTaskScore> completedTask = gameecologyDao.queryCompletedTaskScore(actId, lastDayFormat, SINGLE, SINGLE_LEVEL2, 1);
        List<Long> completedTaskUid = completedTask
                .stream().map(ActTaskScore::getUid)
                .distinct().collect(Collectors.toList());
        List<String> completedTaskMemberId = completedTaskUid.stream().map(x -> x + "").collect(Collectors.toList());

        clock.tag();

        String timeCode = TimeKeyHelper.getTimeCode(event.getTimeKey(), date);
        Map<String, Rank> completeRanks = hdztRankingThriftClient.queryPointedMembersRanking(actId, event.getRankId(), event.getPhaseId(), timeCode,
                completedTaskMemberId, Maps.newHashMap());

        clock.tag();

        log.info("batchInitDailyTask,get completedTask,actId:{},completedTask:{}", actId, JSON.toJSONString(completedTask));
        //初始化二级个人任务
        for (ActTaskScore actTaskScore : completedTask) {
            Rank rank = completeRanks.get(actTaskScore.getUid() + "");
            actTaskScore.setScore(rank.getScore());
            actTaskScore.setTaskTime(taskTime);
            actTaskScore.setCompleted(0L);
            actTaskScore.setcTime(new Date());
            actTaskScore.setuTime(new Date());
            String singleRate = attr.getAwardRateMap().get(SINGLE);
            BigDecimal awardRate = new BigDecimal(singleRate);
            BigDecimal award = awardRate.multiply(new BigDecimal(rank.getScore()));
            actTaskScore.setAward(award.longValue() + "");
            log.info("batchInitDailyTask,new completedTask,actId:{},actTaskScore:{}", actId, JSON.toJSONString(actTaskScore));
        }


        int[] batchInsertRet = gameecologyDao.batchInsertActTaskScore(completedTask);
        clock.tag();
        log.info("batchInitDailyTask,new completedTask,actId:{},batchInsertRet:{}", actId, JSON.toJSONString(batchInsertRet));
        //初始化其他个人任务
        clock.tag();
        int batchCopyRet = gameecologyDao.copyLastDayTask(actId, lastDayFormat, SINGLE, taskTime);
        log.info("batchInitDailyTask,batchCopyRet,actId:{},batchInsertRet:{},cost:{}", actId, batchCopyRet, clock.tag());
    }


    /**
     * 查询接口个人突破+全站突破
     */
    public AnchorTaskScoreInfo queryAnchorTaskScore(long actId, long uid, String taskTime, long index) {
        AnchorTaskScoreInfo result = new AnchorTaskScoreInfo();

        if (!isMyDuty(actId)) {
            log.error("无效活动,actId:{}", actId);
            throw new BadRequestException("无效活动");
        }
        SunshineTaskDailyAwardComponentAttr attr = getComponentAttr(actId, index);
        if (attr == null) {
            log.error("index error,actId:{},index:{}", actId, index);
            throw new ParameterException("index error");
        }

        Date date = DateUtil.getDate(taskTime, DateUtil.PATTERN_TYPE2);

        //未签约主播构建默认的任务,防止刷接口,生成大量数据库数据
        long roleId = enrollmentNewService.getFirstEnrolDestRoleId(attr.getActId(), 0L, RoleType.ANCHOR.getValue(), uid + "");

        //查询时间大于当前时间,则不构建任务
        if (commonService.getNow(actId).before(date) || !actInfoService.inActTime(actId) || roleId == 0) {
            result.setSingle(buildSingleDefaultTask(attr, uid, taskTime));
            result.setAll(buildAllAnchorTask(attr, uid, taskTime, roleId));
            result.setStartAll(0);
            result.setStartSingle(0);
            return result;
        }

        String dateStr = TimeKeyHelper.getTimeCode(attr.getTimeKey(), date);

        Rank rank = hdztRankingThriftClient.queryPointedMembersRanking(actId, attr.getRankId(), attr.getPhaseId(), dateStr,
                uid + "", Maps.newHashMap());

        List<ActTaskScore> anchorDailyTask = getOrCreateAnchorDailyTask(actId, uid, taskTime, attr, false);

        List<ActTaskScore> singleList = anchorDailyTask.stream().filter(it -> SINGLE.equals(it.getTaskType())).collect(Collectors.toList());
        result.setSingle(singleList);
        result.setStartSingle(CollectionUtils.isEmpty(singleList) ? 0 : 1);
        result.setAll(buildAllAnchorTask(attr, uid, taskTime, roleId));
        int startAll = 0;
        List<String> timeList = attr.getOpenTimeMap().get(ALL_SINGLE);
        if (timeList.contains(taskTime)) {
            startAll = 1;
        }
        result.setStartAll(startAll);
        result.setScore(rank.getScore() == -1 ? 0 : rank.getScore());
        return result;
    }


    /**
     * 个人突破和全站突破任务合并查询
     *
     * @return
     */
    public List<BabyMissionItem> querySingleAndAllTask(long actId, long uid, String taskTime, long index) {
        List<BabyMissionItem> items = Lists.newArrayList();
        AnchorTaskScoreInfo scoreInfo = queryAnchorTaskScore(actId, uid, taskTime, index);
        SunshineTaskDailyAwardComponentAttr attr = getComponentAttr(actId, index);
        if (scoreInfo == null) {
            return items;
        }
        BabyMissionItem mission = new BabyMissionItem();
        mission.setCompletedCount(scoreInfo.getScore());
        List<TaskItem> taskConfig = buildTaskConfigView(attr, scoreInfo);
        mission.setTaskItems(taskConfig);

        PairBean pairBean = getCurLevelConfig(scoreInfo.getScore(), taskConfig);
        long curLevelConfig = pairBean.getSecond();

        mission.setTotalCount(curLevelConfig);
        mission.setLevel(pairBean.getFirst());

        items.add(mission);
        return items;
    }

    /**
     * 当前阶段的总任务数
     *
     * @param score
     * @param taskConfig
     * @return
     */
    private PairBean getCurLevelConfig(long score, List<TaskItem> taskConfig) {
        long curMinScore = 0;
        long curMaxScore = 0;
        for (int i = 0; i < taskConfig.size(); i++) {
            TaskItem item = taskConfig.get(i);
            long curGapScore = item.getPassValue();
            curMaxScore = curMinScore + curGapScore;
            if (score >= curMinScore && score < curMaxScore) {
                return new PairBean(i + 1, curMaxScore);
            }

            curMinScore = curMinScore + curGapScore;
        }

        return new PairBean(1, curMaxScore);
    }

    private List<TaskItem> buildTaskConfigView(SunshineTaskDailyAwardComponentAttr attr, AnchorTaskScoreInfo scoreInfo) {
        List<ActTaskScore> actTaskScores = buildActTaskScoreScore(scoreInfo);
        return convertTaskConfigView(attr, actTaskScores);
    }

    private List<TaskItem> convertTaskConfigView(SunshineTaskDailyAwardComponentAttr attr, List<ActTaskScore> taskScores) {
        List<TaskItem> result = Lists.newLinkedList();
        for (int i = 0; i < taskScores.size(); i++) {
            ActTaskScore taskScore = taskScores.get(i);
            TaskItem item = new TaskItem();
            long passValue = i == 0 ? taskScore.getScore() : taskScore.getScore() - taskScores.get(i - 1).getScore();
            item.setPassValue(passValue);
            String taskName = SINGLE.equals(taskScore.getTaskType()) ? "个人" : "全站";
            item.setRemark(taskName + taskScore.getLevel());
            JSONObject ext = null;
            if (StringUtil.isNotBlank(taskScore.getExtJson())) {
                ext = JSON.parseObject(taskScore.getExtJson());
            }
            if (ext == null) {
                ext = new JSONObject();
            }
            //这里是非分段
            String desc = String.format(attr.getAwardDescTips(), taskScore.getScore(), taskName);
            ext.put("desc", desc);
            item.setExtjson(JSON.toJSONString(ext));
            result.add(item);
        }
        return result;
    }

    private List<ActTaskScore> buildActTaskScoreScore(AnchorTaskScoreInfo scoreInfo) {
        List<ActTaskScore> taskScores = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(scoreInfo.getSingle())) {
            taskScores.addAll(scoreInfo.getSingle());
        }
        if (CollectionUtils.isNotEmpty(scoreInfo.getAll())) {
            taskScores.addAll(scoreInfo.getAll());
        }
        //分数从小到大排列
        return taskScores.stream().sorted(Comparator.comparing(ActTaskScore::getScore)).collect(Collectors.toList());
    }

    private List<ActTaskScore> buildSingleDefaultTask(SunshineTaskDailyAwardComponentAttr attr, long uid, String taskTime) {
        String single = attr.getDefaultTaskScore().get(SINGLE);
        return JSON.parseArray(single, ActTaskScore.class);
    }


    /**
     * 构建默认task仅展示用
     */
    private List<ActTaskScore> buildAllAnchorTask(SunshineTaskDailyAwardComponentAttr attr, long uid, String taskTime, long roleId) {

        List<ActTaskScore> list;
        if (attr.getAllTaskTTRoleId().contains(roleId)) {
            String allTiantuan = attr.getDefaultTaskScore().get(ALL_TIANTUAN);
            list = JSON.parseArray(allTiantuan, ActTaskScore.class);
        } else {
            String allSingle = attr.getDefaultTaskScore().get(ALL_SINGLE);
            list = JSON.parseArray(allSingle, ActTaskScore.class);
        }
        for (ActTaskScore actTaskScore : list) {
            actTaskScore.setActId(attr.getActId());
            actTaskScore.setTaskTime(taskTime);
            actTaskScore.setUid(uid);
        }
        return list;
    }

    //查询完成情况
    public List<String> queryAnchorFinishTaskAward(long actId, Long index, long uid) {
        String groupCode = redisConfigManager.getGroupCode(actId);
        String tasks = actRedisDao.hget(groupCode, Const.addActivityPrefix(actId, ANCHOR_TASK_FINISH_AWARD), uid + "");
        List<String> finish = new ArrayList<>();
        if (StringUtil.isBlank(tasks)) {
            return finish;
        }
        List<ActTaskScore> finishList = JSON.parseArray(tasks, ActTaskScore.class);
        finishList.sort(Comparator.comparing(ActTaskScore::getuTime).reversed());

        SunshineTaskDailyAwardComponentAttr attr = getComponentAttr(actId, index);

        for (ActTaskScore actTaskScore : finishList) {
            finish.add(buildAwardRecord(attr, actTaskScore, uid));
        }
        return finish;
    }

    private String buildAwardRecord(SunshineTaskDailyAwardComponentAttr attr, ActTaskScore actTaskScore, long uid) {
        // Date date = DateUtil.getDate(dateStr, DateUtil.PATTERN_TYPE1);
        String timeForm = "MM月dd日HH:mm";
        String time = DateUtil.format(actTaskScore.getuTime(), timeForm);
        UserBaseInfo userInfo = commonService.getUserInfo(uid, false);
        String task = SINGLE.equals(actTaskScore.getTaskType()) ? "个人突破" : "高光任务";

        String result = "%s于%s完成%s任务LV%s,获得%s队友金奖励";

        String key = makePoolOutRecord(attr, actTaskScore.getTaskTime(), actTaskScore.getUid(), actTaskScore.getTaskType(), actTaskScore.getLevel());
        String poolOut = actRedisDao.get(getRedisGroupCode(attr.getActId()), key);
        if (!StringUtil.isBlank(poolOut)) {
            result = "%s于%s完成%s任务LV%s,手慢了未获得队友金（全服奖池已发完）";
        }


        long award = Convert.toLong(actTaskScore.getAward().trim());
        return String.format(result, userInfo.getNick(), time, task, actTaskScore.getLevel(), award);
    }


    /***
     * 结算前一天任务发奖
     */
    long counter = 0;

    // @Scheduled(cron = "0 0/1 * * * ? ")
    public void invokeSettlePreDayAward() {
        Long cmptId = this.getComponentId();
        Clock clock = new Clock();

        Set<Long> actIds = this.getActivityIds();
        if (CollectionUtils.isEmpty(actIds)) {
            log.info("{}) exec skip@cmptId:{}, no activity need process", counter, cmptId);
            return;
        }

        List<ActivityInfoVo> effectActInfos = hdztRankingThriftClient.queryEffectActInfos();
        if (CollectionUtils.isEmpty(effectActInfos)) {
            log.info("{}) exec skip@cmptId:{}, hdzt no activity need process", counter, cmptId);
            return;
        }

        for (ActivityInfoVo actInfo : effectActInfos) {
            long actId = actInfo.getActId();
            if (!actIds.contains(actId)) {
                continue;
            }
            SunshineTaskDailyAwardComponentAttr attr = this.getUniqueComponentAttr(actId);
            if (attr == null) {
                log.warn("{}) exec skip@actId:{} no uniq cmptId:{} cmptUseInx:1 attribute!", counter, actId, cmptId);
                continue;
            }
            Date now = commonService.getNow(actId);
            //活动结束后，有可能还要结算前一天，所以用活动展示结束时间来判断
            if (!actInfoService.inActShowTime(now, actInfo)) {
                continue;
            }
            //延后执行，在前一天所有pk宝箱乱斗结束后执行
            if (DateUtil.getHours(now) < 1) {
                continue;
            }
            String settleTime = DateUtil.format(DateUtil.getAddDay(now, -1), DateUtil.PATTERN_TYPE2);
            String dupKey = makeKey(attr, "settle_award:" + settleTime);
            if (!actRedisDao.setNX(getRedisGroupCode(actId), dupKey, DateUtil.format(now))) {
                log.info("invokeSettlePreDayAward already run,key:{}", dupKey);
                return;
            }


            SunShineTaskAwardInvoke para = new SunShineTaskAwardInvoke(settleTime, attr);
            String paraStr = JSON.toJSONString(para);
            try {

                //baiduInfoFlowRobotService.sendNotify(attr.getRlGroupId(), attr.getRlBaiduWebhook(), "重要通知:主播高光任务结算开始（如果没看到结算成功消息会有问题）,actId:" + attr.getActId(), attr.getRlUserIds());
                baiduInfoFlowRobotService.sendNotifyByConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, "重要通知:主播高光任务结算开始（如果没看到结算成功消息会有问题）,actId:" + attr.getActId(), attr.getRlUserIds());

                log.info("invokeSettlePreDayAward begin,para:{}", paraStr);
                settlePreDayAward(para);
                log.info("invokeSettlePreDayAward ok");

                //baiduInfoFlowRobotService.sendNotify(attr.getRlGroupId(), attr.getRlBaiduWebhook(), "很幸运，主播高光任务结算成功了,actId:" + attr.getActId(), attr.getRlUserIds());
                baiduInfoFlowRobotService.sendNotifyByConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, "很幸运，主播高光任务结算成功了,actId:" + attr.getActId(), attr.getRlUserIds());

            } catch (Exception e) {
                log.error("invokeSettlePreDayAward error,para:{},e:{}", paraStr, e.getMessage(), e);

                //baiduInfoFlowRobotService.sendNotify(attr.getRlGroupId(), attr.getRlBaiduWebhook(), "严重错误，主播高光任务结算失败了,actId:" + attr.getActId(), attr.getRlUserIds());
                baiduInfoFlowRobotService.sendNotifyByConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, "严重错误，主播高光任务结算失败了,actId:" + attr.getActId(), attr.getRlUserIds());
            }

        }

        log.info("invokeSettlePreDayAward done,colck:{}", clock.tag());


    }

    public void settlePreDayAward(SunShineTaskAwardInvoke para) {
        //线上临时改的结算版本，有问题好回滚
        final String v2Str = "V2";
        if (v2Str.equals(para.getAttr().getSettleVersion())) {
            settlePreDayAwardV2(para);
        } else {
            settlePreDayAwardV1(para);
        }
    }

    /**
     * 结算前一天奖励
     * 注意，这个方法可能会重试，所以必须保障这个方法的幂等性！！！目前是用发奖seq保证
     */
    public void settlePreDayAwardV1(SunShineTaskAwardInvoke para) {
        log.info("settlePreDayAward begin,para:{}", JSON.toJSONString(para));
        String taskTime = para.getTaskTime();
        SunshineTaskDailyAwardComponentAttr attr = para.getAttr();

        long actId = attr.getActId();
        List<ActTaskScore> preDayAwardInfo = gameecologyDao.queryMaxAward(actId, taskTime, ALL_AWARD_TASK);
        if (CollectionUtils.isEmpty(preDayAwardInfo)) {
            log.warn("settlePreDayAward empty,actId:{},taskTime:{}", actId, taskTime);
            return;
        }
        List<UserAwardInfo> userAwardInfos = Lists.newArrayList();
        for (ActTaskScore taskScore : preDayAwardInfo) {
            long uid = taskScore.getUid();
            long awardAmount = Convert.toLong(taskScore.getAward());

            //剔除交友乱斗宝箱怪玩法奖励金额
            long ftsAmount = ftsBaseInfoBridgeClient.queryChannelFightChampionTeammate(taskTime, uid);
            if (ftsAmount >= awardAmount) {
                //交友乱斗队友金大于日任务,跳过发放
                log.warn("settlePreDayAward fts cal skip,actId:{},taskTime:{},uid:{}", actId, taskTime, uid);
                continue;
            } else {
                awardAmount = awardAmount - ftsAmount;
                log.warn("settlePreDayAward fts cal deduction,actId:{},taskTime:{},uid:{},afterAmount:{},ftsAmount:{}", actId, taskTime, uid, awardAmount, ftsAmount);
            }


            EnrollmentInfo enrollmentInfo = enrollmentNewService.tryGetFirstEnrolMemberCache(attr.getActId(), 0L, RoleType.ANCHOR.getValue(), uid + "");
            if (enrollmentInfo == null) {
                log.error("结算突破/全站任务严重错误,not found EnrollmentInfo,actId:{},memberId:{}", attr.getActId(), uid);
                throw new RuntimeException("结算突破/全站任务严重错误-error-actId:" + actId);
            }
            Map<Long, Long> taskIdPackageId = attr.getBusiTaskIdPackageId().get(enrollmentInfo.getRoleBusiId());
            if (MapUtils.isEmpty(taskIdPackageId)) {
                log.error("settlePreDayAward not found,taskTime:{},attr:{},enrollmentInfo:{}", taskTime, JSON.toJSONString(attr), JSON.toJSONString(enrollmentInfo));
                continue;
            }

            UserAwardInfo awardInfo = new UserAwardInfo();
            awardInfo.setUid(uid);
            Map<Long, Map<Long, Integer>> taskPackageIdAmount = Maps.newHashMap();
            for (Long taskId : taskIdPackageId.keySet()) {
                Map<Long, Integer> item = taskPackageIdAmount.getOrDefault(taskId, Maps.newHashMap());
                if (awardAmount > Integer.MAX_VALUE) {
                    log.error("settlePreDayAward error,发奖阻断，数据溢出,actId:{},uid:{},awardAmount:{}", actId, uid, awardAmount);
                    continue;
                }

                int roundDownAmount = Convert.toInt(awardAmount) / attr.getRoundingDown() * attr.getRoundingDown();
                item.put(taskIdPackageId.get(taskId), roundDownAmount);

                taskPackageIdAmount.put(taskId, item);
            }
            awardInfo.setTaskPackageIds(taskPackageIdAmount);
            userAwardInfos.add(awardInfo);
        }


        Date now = commonService.getNow(attr.getActId());
        String time = DateUtil.format(now);
        //发奖
        for (UserAwardInfo userAwardInfo : userAwardInfos) {
            String seq = String.format("SettleSunTask-%s-%s-%s-%s-%s", attr.getActId(), attr.getRankId(), taskTime, attr.getCmptUseInx(), userAwardInfo.getUid());
            BatchWelfareResult result = hdztAwardServiceClient.doBatchWelfare(seq, userAwardInfo.getUid(), userAwardInfo.getTaskPackageIds(), time, 3, Maps.newHashMap());
            if (result == null || result.getCode() != 0) {
                log.error("settlePreDayAward error,发奖失败,seq:{},actId:{},uid:{},package:{},result:{}", seq, actId, userAwardInfo.getUid(), JSON.toJSONString(userAwardInfo), JSON.toJSONString(result));
                throw new RuntimeException("发奖失败");
            } else {
                log.info("settlePreDayAward ok,seq:{},actId:{},uid:{},package:{},result:{}", seq, actId, userAwardInfo.getUid(), JSON.toJSONString(userAwardInfo), JSON.toJSONString(result));
            }
        }
    }

    /**
     * 获取总奖池额度
     */
    public long getTotalPoolBalance(long actId, long index) {
        return getTotalPoolBalance(getComponentAttr(actId, index));
    }

    private long getTotalPoolBalance(SunshineTaskDailyAwardComponentAttr attr) {
        String key = makeKey(attr, TOTAL_AWARD_POOL_KEY);
        return Convert.toLong(actRedisDao.get(getRedisGroupCode(attr.getActId()), key));
    }

    public void setTotalPoolBalance(SunshineTaskDailyAwardComponentAttr attr, long amount) {
        log.info("setTotalPoolBalance begin,actId:{},amount:{}", JSON.toJSONString(attr), amount);
        try {
            String key = makeKey(attr, TOTAL_AWARD_POOL_KEY);
            actRedisDao.set(getRedisGroupCode(attr.getActId()), key, amount + "");
        } catch (Exception e) {
            log.error("主播高光任务结算严重错误,setTotalPoolBalance error,attr:{},amount:{},e:{}", JSON.toJSONString(attr), amount, e.getMessage(), e);
            //baiduInfoFlowRobotService.sendNotify(attr.getRlGroupId(), attr.getRlBaiduWebhook(), "主播高光任务结算严重错误,ActId:" + attr.getActId(), attr.getRlUserIds());
            baiduInfoFlowRobotService.sendNotifyByConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, "主播高光任务结算严重错误,ActId:" + attr.getActId(), attr.getRlUserIds());
        }
    }

    /**
     * 获取角色日上限
     */
    public long getRoleAwardDayLimit(SunshineTaskDailyAwardComponentAttr attr, long roleId) {
        for (String key : attr.getRoleAwardDayLimit().keySet()) {
            if (("," + key + ",").contains("," + roleId + ",")) {
                return attr.getRoleAwardDayLimit().get(key);
            }
        }

        return attr.getDefaultRoleAwardDayLimit();
    }

    /**
     * 结算前一天奖励
     * 注意，这个方法可能会重试，所以必须保障这个方法的幂等性！！！目前是用发奖seq保证
     */
    public void settlePreDayAwardV2(SunShineTaskAwardInvoke para) {
        log.info("settlePreDayAward begin,para:{}", JSON.toJSONString(para));
        String taskTime = para.getTaskTime();
        SunshineTaskDailyAwardComponentAttr attr = para.getAttr();

        long actId = attr.getActId();
        List<ActTaskScore> preDayAwardInfo = gameecologyDao.queryAward(actId, taskTime, ALL_AWARD_TASK);
        if (CollectionUtils.isEmpty(preDayAwardInfo)) {
            log.warn("settlePreDayAward empty,actId:{},taskTime:{}", actId, taskTime);
            return;
        }

        //用户需要发放
        Map<Long, Long> userSend = Maps.newHashMap();


        //总限量余额
        long totalPoolBalance = getTotalPoolBalance(attr);
        log.info("settlePreDayAward begin,taskTime:{},totalPoolBalance:{}", taskTime, totalPoolBalance);
        for (ActTaskScore actTaskScore : preDayAwardInfo) {
            long uid = actTaskScore.getUid();

            //日限制
            EnrollmentInfo enrollmentInfo = enrollmentNewService.tryGetFirstEnrolMemberCache(attr.getActId(), 0L, RoleType.ANCHOR.getValue(), uid + "");
            if (enrollmentInfo == null) {
                log.error("结算突破/全站任务严重错误,not found EnrollmentInfo,actId:{},memberId:{}", attr.getActId(), uid);
                throw new RuntimeException("结算突破/全站任务严重错误-error-actId:" + actId);
            }
            long dayLimit = getRoleAwardDayLimit(attr, enrollmentInfo.getDestRoleId());


            //--- 剔除交友乱斗宝箱怪玩法奖励金额
            long ftsAmount = ftsBaseInfoBridgeClient.queryChannelFightChampionTeammate(taskTime, uid);
            long itemAward = Convert.toLong(actTaskScore.getAward());
            if (ftsAmount >= dayLimit) {
                log.warn("settlePreDayAward fts more than day limit skip,actId:{},taskTime:{},uid:{},ftsAmount:{},itemAward:{}", actId, taskTime, uid, ftsAmount, itemAward);
                continue;
            } else if (ftsAmount >= itemAward) {
                log.warn("settlePreDayAward fts cal skip,actId:{},taskTime:{},uid:{},ftsAmount:{},itemAward:{}", actId, taskTime, uid, ftsAmount, itemAward);
                continue;
            } else {
                itemAward = itemAward - ftsAmount;
                log.warn("settlePreDayAward fts cal deduction,actId:{},taskTime:{},uid:{},afterAmount:{},ftsAmount:{}", actId, taskTime, uid, itemAward, ftsAmount);
            }

            //---日限制额逻辑
            if (itemAward > dayLimit) {
                log.warn("settlePreDayAward more than dayLimit,actId:{},taskTime:{},uid:{},itemAward:{},dayLimit:{}，ftsAmount:{}", actId, taskTime, uid, itemAward, dayLimit, ftsAmount);
                itemAward = dayLimit - ftsAmount;
            }


            //用户上1个等级的奖励
            long userOldAward = 0;
            if (userSend.containsKey(uid)) {
                userOldAward = userSend.get(uid);
            }
            if (userOldAward >= itemAward) {
                log.warn("settlePreDayAward old award more than new award,uid:{},itemAward:{},oldAward:{},actTaskScore:{}", uid, itemAward, userOldAward, JSON.toJSONString(actTaskScore));
                continue;
            }


            //---发放超过总奖池，阻断
            if (itemAward > totalPoolBalance + userOldAward) {
                log.warn("settlePreDayAward,itemAward more than totalPoolBalance,uid:{},itemAward:{},userOldAward:{},totalPoolBalance:{}", uid, itemAward, userOldAward, totalPoolBalance);
                //清空奖池
                totalPoolBalance = 0;

                //本次记录因为奖池消耗完了，不做发放，记录下标记，给前端展示用
                String key = makePoolOutRecord(attr, actTaskScore.getTaskTime(), uid, actTaskScore.getTaskType(), actTaskScore.getLevel());
                actRedisDao.set(getRedisGroupCode(actId), key, DateUtil.format(new Date()));

                continue;
            }

            //---总奖池把要被覆盖的发放还原回去
            if (userOldAward > 0) {
                totalPoolBalance = totalPoolBalance + userOldAward;
            }


            totalPoolBalance = totalPoolBalance - itemAward;
            userSend.put(uid, itemAward);
        }


        List<UserAwardInfo> userAwardInfos = Lists.newArrayList();

        for (Long uid : userSend.keySet()) {
            long awardAmount = userSend.get(uid);


            EnrollmentInfo enrollmentInfo = enrollmentNewService.tryGetFirstEnrolMemberCache(attr.getActId(), 0L, RoleType.ANCHOR.getValue(), uid + "");
            if (enrollmentInfo == null) {
                log.error("结算突破/全站任务严重错误,not found EnrollmentInfo,actId:{},memberId:{}", attr.getActId(), uid);
                throw new RuntimeException("结算突破/全站任务严重错误-error-actId:" + actId);
            }
            Map<Long, Long> taskIdPackageId = attr.getBusiTaskIdPackageId().get(enrollmentInfo.getRoleBusiId());
            if (MapUtils.isEmpty(taskIdPackageId)) {
                log.error("settlePreDayAward not found,taskTime:{},attr:{},enrollmentInfo:{}", taskTime, JSON.toJSONString(attr), JSON.toJSONString(enrollmentInfo));
                continue;
            }

            UserAwardInfo awardInfo = new UserAwardInfo();
            awardInfo.setUid(uid);
            Map<Long, Map<Long, Integer>> taskPackageIdAmount = Maps.newHashMap();
            for (Long taskId : taskIdPackageId.keySet()) {
                Map<Long, Integer> item = taskPackageIdAmount.getOrDefault(taskId, Maps.newHashMap());
                if (awardAmount > Integer.MAX_VALUE) {
                    log.error("settlePreDayAward error,发奖阻断，数据溢出,actId:{},uid:{},awardAmount:{}", actId, uid, awardAmount);
                    continue;
                }

                int roundDownAmount = Convert.toInt(awardAmount) / attr.getRoundingDown() * attr.getRoundingDown();
                item.put(taskIdPackageId.get(taskId), roundDownAmount);

                taskPackageIdAmount.put(taskId, item);
            }
            awardInfo.setTaskPackageIds(taskPackageIdAmount);
            userAwardInfos.add(awardInfo);
        }


        Date now = commonService.getNow(attr.getActId());
        String time = DateUtil.format(now);
        //发奖
        for (UserAwardInfo userAwardInfo : userAwardInfos) {
            String seq = String.format("SettleSunTask-%s-%s-%s-%s-%s", attr.getActId(), attr.getRankId(), taskTime, attr.getCmptUseInx(), userAwardInfo.getUid());
            BatchWelfareResult result = hdztAwardServiceClient.doBatchWelfare(seq, userAwardInfo.getUid(), userAwardInfo.getTaskPackageIds(), time, 3, Maps.newHashMap());
            if (result == null || result.getCode() != 0) {
                log.error("settlePreDayAward error,发奖失败,seq:{},actId:{},uid:{},package:{},result:{}", seq, actId, userAwardInfo.getUid(), JSON.toJSONString(userAwardInfo), JSON.toJSONString(result));
                throw new RuntimeException("发奖失败");
            } else {
                log.info("settlePreDayAward ok,seq:{},actId:{},uid:{},package:{},result:{}", seq, actId, userAwardInfo.getUid(), JSON.toJSONString(userAwardInfo), JSON.toJSONString(result));
            }
        }

        //---所有搞成功后 最后保存总奖池余额(定时器保证运行唯一性，否则有并发问题)
        setTotalPoolBalance(attr, totalPoolBalance);

    }

    /**
     * 生成额度用完了，无法发奖key
     */
    private String makePoolOutRecord(SunshineTaskDailyAwardComponentAttr attr, String taskTime, long uid, String taskType, long level) {
        return makeKey(attr, String.format(POOL_OUT_KEY, taskTime, uid, taskType, level));
    }
}
