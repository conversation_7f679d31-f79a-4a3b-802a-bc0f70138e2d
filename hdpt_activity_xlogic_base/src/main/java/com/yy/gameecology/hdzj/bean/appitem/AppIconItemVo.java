package com.yy.gameecology.hdzj.bean.appitem;

import lombok.Data;

/**
 * desc: app气泡
 *
 * <AUTHOR>
 * @date 2024-05-22 19:50
 **/
@Data
public class AppIconItemVo {

    /**
     * 给客户端去重显示key。例如如果一天显示1次，服务端会以日期组key,如果为空则常驻显示
     */
    private String dupKey;
    /**
     * 1== webview 2==原生气泡 3==banner广告位
     */
    private int type;
    /**
     * 功能id,见 com.yy.gameecology.common.consts.AppIconItemId
     */
    private long id;

    /**
     * 宽度，单位为 dp
     */
    private int width;

    /**
     * 宽度，单位为 dp
     */
    private int height;

    /**
     * 需要展示几秒，单位为 秒， 如果 -1 则常驻显示
     */
    private int duration;

    /**
     * 是否需要展示红点
     */
    private boolean showRed;

    /**
     * 气泡展示的位置， left、right、top、bottom， 相对于 view 的上下左右
     */
    private String position;

    /**
     * webview内容
     */
    private IconWeb web;

    /**
     * 原生气泡内容
     */
    private IconNative nativeContent;

    /**
     * 横幅广告位内容
     */
    private IconBanner banner;

}
