package com.yy.gameecology.hdzj.bean;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.common.utils.WebUtil;
import com.yy.protocol.pb.GameecologyActivity;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-12-13 20:18
 **/
public class CommonPBOperateRequest {

    /**
     * 请求发起人-从通道取
     */
    private long opUid;

    /**
     * 请求发起频道号-从通道取
     */
    private long opSid;

    /**
     * 请求发起自频道号-从通道取
     */
    private long opSsid;

    /**
     * ip-从通道中取
     */
    private String ip;

    /**
     * 活动ID
     */
    private long actId;
    /**
     * 请求seq
     */
    private String seq;
    /**
     * 请求类型
     */
    private String opType;
    /**
     * 请求数据id
     */
    private String opId;

    private String opTarget;
    private String opContent;
    /**
     * 响应组件id
     */
    private long cmptId;
    /**
     * 响应组件index
     */
    private long cmptIndex;

    private Map<Long, String> ext;
    /**
     * 扩展json数据，约定使用
     */
    private String extjson;

    public CommonPBOperateRequest() {
    }

    public CommonPBOperateRequest(GameecologyActivity.CommonOperateRequest request) {
        this.actId = request.getActId();
        this.seq = request.getSeq();
        this.opType = request.getOpType();
        this.opId = request.getOpId();
        this.opTarget = request.getOpTarget();
        this.opContent = request.getOpContent();
        this.cmptId = request.getCmptId();
        this.cmptIndex = request.getCmptIndex();
        this.extjson = request.getExtjson();
    }

    public long getActId() {
        return actId;
    }

    public void setActId(long actId) {
        this.actId = actId;
    }

    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }

    public String getOpType() {
        return opType;
    }

    public void setOpType(String opType) {
        this.opType = opType;
    }

    public String getOpId() {
        return opId;
    }

    public void setOpId(String opId) {
        this.opId = opId;
    }

    public String getOpTarget() {
        return opTarget;
    }

    public void setOpTarget(String opTarget) {
        this.opTarget = opTarget;
    }

    public String getOpContent() {
        return opContent;
    }

    public void setOpContent(String opContent) {
        this.opContent = opContent;
    }

    public long getCmptId() {
        return cmptId;
    }

    public void setCmptId(long cmptId) {
        this.cmptId = cmptId;
    }

    public long getCmptIndex() {
        return cmptIndex;
    }

    public void setCmptIndex(long cmptIndex) {
        this.cmptIndex = cmptIndex;
    }

    public String getExtjson() {
        return extjson;
    }

    public void setExtjson(String extjson) {
        this.extjson = extjson;
    }

    public long getOpUid() {
        return opUid;
    }

    public void setOpUid(long opUid) {
        this.opUid = opUid;
    }

    public long getOpSid() {
        return opSid;
    }

    public void setOpSid(long opSid) {
        this.opSid = opSid;
    }

    public long getOpSsid() {
        return opSsid;
    }

    public void setOpSsid(long opSsid) {
        this.opSsid = opSsid;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Map<Long, String> getExt() {
        return ext;
    }

    public void setExt(Map<Long, String> ext) {
        this.ext = ext;
    }

    public String getHdid() {
        if (!StringUtils.startsWith(extjson, StringUtil.OPEN_BRACE)) {
            return StringUtils.EMPTY;
        }

        Map<String, String> ext = JSON.parseObject(extjson, new TypeReference<HashMap<String, String>>(){});

        String hdid = MapUtils.getString(ext, WebUtil.YY_HEADER_HDID);
        if (StringUtils.isEmpty(hdid)) {
            hdid = MapUtils.getString(ext, WebUtil.YY_HEADER_HDID_0, StringUtils.EMPTY);
        }

        return hdid;
    }
}
