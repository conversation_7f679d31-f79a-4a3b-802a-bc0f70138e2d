package com.yy.gameecology.hdzj.element.component;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.ScheduledExt;
import com.yy.gameecology.activity.bean.hdzt.HdztAwardLotteryMsg;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.client.AsynInit;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.ReadFileUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.RollAwardRecordComponentAttr;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: CXZ
 * @Desciption: 抽奖记录滚屏组件（假数据）
 * @Date: 2021/11/06 16:39
 * @Modified:
 */
@Component
public class RollAwardRecordComponent extends BaseActComponent<RollAwardRecordComponentAttr> implements AsynInit {

    private static final String AWARD_RECORD_KEY = "award_record";

    private List<Long> uidList = new ArrayList<>();


    private static final Random RANDOM = new Random();

    @Lazy
    @Autowired
    private RollAwardRecordComponent myself;

    private void init() {
        String uidsFileName = "random_award_uids.txt";
        try {
            List<String> contents = ReadFileUtil.readFromFile(uidsFileName, "utf8", null);
            if (contents != null) {
                uidList = contents
                        .stream()
                        .filter(StringUtils::isNumeric)
                        .map(Long::valueOf)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("init, get uid list failed, file:{}, e:{}", uidsFileName, e.getMessage(), e);
        }
    }

    @Override
    public Long getComponentId() {
        return ComponentId.ROLL_AWARD_RECORD;
    }

    /**
     * 监听抽奖事件，保存中奖记录
     *
     * @param event
     * @param attr
     */
    @HdzjEventHandler(value = HdztAwardLotteryMsg.class, canRetry = false)
    public void onHdztAwardLotteryMsg(HdztAwardLotteryMsg event, RollAwardRecordComponentAttr attr) {
        if (!ArrayUtils.contains(attr.getTaskIds(), event.getTaskId())) {
            return;
        }
        List<HdztAwardLotteryMsg.Award> awardRecords = event.getData();
        if (awardRecords.isEmpty()) {
            log.error("award is empty,actId={},cmptUseInx={}", attr.getActId(), attr.getCmptUseInx());
            return;
        }
        saveAwardRecord(event.getData(), attr);
        log.info("onhdztAwardLotteryMsg done -> event:{}, attr:{}", JSON.toJSONString(event), attr);
    }

    /**
     * 产生假数据
     *
     * @param attr
     */
    private void makeFakeAwardRecord(RollAwardRecordComponentAttr attr) {

        //假数据概率不配置时，假数据不产生
        Map<String, Integer> itemProbabilityMap = attr.getFakeItemProbabilityMap();
        if (CollectionUtils.isEmpty(itemProbabilityMap)) {
            log.warn("makeFakeAwardRecord ignore,itemProbabilityMap is empty,actId={},cmptUseInx={}", attr.getActId(), attr.getCmptUseInx());
            return;
        }
        if (CollectionUtils.isEmpty(uidList)) {
            log.error("makeFakeAwardRecord uidList is empty,actId={},cmptUseInx={}", attr.getActId(), attr.getCmptUseInx());
            return;
        }

        //活动开始后10分钟产生假数据
        if (!actInfoService.intWorkTime(attr.getActId(), -attr.getFakeDelaySec(), 0L)) {
            return;
        }

        int startProbability = 0;
        TreeMap<Integer, String> probabilityTreeMap = Maps.newTreeMap();
        for (Map.Entry<String, Integer> entry : itemProbabilityMap.entrySet()) {
            int probability = entry.getValue();
            if (probability <= 0) {
                log.error("makeFakeAwardRecord fakeItemProbability error ,actId={},cmptUseInx={},item={} probability={}"
                        , attr.getActId(), attr.getCmptUseInx(), entry.getKey(), probability);
                continue;
            }
            probabilityTreeMap.put(startProbability, entry.getKey());
            startProbability += probability;

        }

        if (startProbability <= 0) {
            log.error("makeFakeAwardRecord fakeItemProbabilityMap error, totalProbability is zero,actId={},cmptUseInx={},itemProbabilityMap={}"
                    , attr.getActId(), attr.getCmptUseInx(), JSONObject.toJSONString(itemProbabilityMap));
        }
        // 随机抽奖
        int index = RANDOM.nextInt(startProbability);
        Map.Entry<Integer, String> fakeItemEntry = probabilityTreeMap.floorEntry(index);
        if (fakeItemEntry == null) {
            log.error("makeFakeAwardRecord random error, ,actId={},cmptUseInx={},index ={} probabilityTreeMap={}"
                    , attr.getActId(), attr.getCmptUseInx(), index, JSONObject.toJSONString(probabilityTreeMap));
            return;
        }
        index = RANDOM.nextInt(uidList.size());
        long uid = uidList.get(index);

        String awardName = fakeItemEntry.getValue();
        HdztAwardLotteryMsg.Award awardRecord = new HdztAwardLotteryMsg.Award();
        awardRecord.setUid(uid);
        awardRecord.setGiftName(awardName);
        //记录假数据产生的时间
        awardRecord.setRecordId(System.currentTimeMillis());
        saveAwardRecord(Lists.newArrayList(awardRecord), attr);
        log.info("makeFakeAwardRecord done,actId:{},cmptUseInx:{},uid:{},prize:{}"
                , attr.getActId(), attr.getCmptUseInx(), awardRecord.getUid(), awardName);

    }

    /**
     * 获取滚屏记录
     *
     * @param actId
     * @param index
     * @param count
     * @param useCache
     * @return
     */
    public List<JSONObject> getRollAwardRecords(long actId, int index, Integer count, boolean useCache) {

        // 后台配置的使用缓存开关，当发现性能问题时，可打开！默认打开
        boolean globalCacheFlag = Const.isGeOneFlag(GeParamName.GET_ROLL_AWARD_RECORDS_BY_CACHE, 1);

        if (globalCacheFlag || useCache) {
            return myself.getRollAwardRecordsCache(actId, index, count);
        } else {
            return getRollAwardRecords(actId, index, count);
        }
    }

    /**
     * 从jvm缓存中获取轮播中奖纪录（默认缓存30秒）
     *
     * @param actId
     * @param index
     * @param count
     * @return
     */
    @Cached(timeToLiveMillis = 30 * 1000)
    public List<JSONObject> getRollAwardRecordsCache(long actId, int index, Integer count) {
        return getRollAwardRecords(actId, index, count);
    }

    public List<JSONObject> getRollAwardRecords(long actId, int index, Integer count) {
        RollAwardRecordComponentAttr attr = getComponentAttr(actId, index);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + index);

        count = Math.max(1, Convert.toInt(count, attr.getQueryRecordCount()));

        List<HdztAwardLotteryMsg.Award> awardRecord = getAwardRecord(count, attr);
        List<JSONObject> data = new ArrayList<>();

        if (!CollectionUtils.isEmpty(awardRecord)) {
            int keepNickCount = attr.getKeepNickCount();
            for (HdztAwardLotteryMsg.Award award : awardRecord) {
                JSONObject item = new JSONObject();
                String nick = commonService.getUserInfo(award.getUid(), false).getNick();

                item.put("nick", dim(nick, keepNickCount));
                item.put("prize", award.getGiftName());
                data.add(item);
            }
        }
        return data;
    }

    /**
     * 昵称模糊处理,只保留前n位，至少返回  keepNickCount+2位，尾部用 * 填充
     *
     * @param source
     * @param keepNickCount
     * @return
     */
    private String dim(String source, int keepNickCount) {
        source = Convert.toString(source);
        String keepNick = source.length() <= keepNickCount ? source : source.substring(0, keepNickCount);

        return StringUtils.rightPad(keepNick, keepNickCount + 2, "*");
    }


    /**
     * 保存中奖记录
     *
     * @param awardRecords
     * @param attr
     */
    private void saveAwardRecord(List<HdztAwardLotteryMsg.Award> awardRecords, RollAwardRecordComponentAttr attr) {
        String recordKey = makeKey(attr, AWARD_RECORD_KEY);
        StringRedisTemplate template = actRedisDao.getRedisTemplate(getRedisGroupCode(attr.getActId()));
        List<String> records = awardRecords.stream().map(JSON::toJSONString).collect(Collectors.toList());
        template.opsForList().leftPushAll(recordKey, records);
        int maxCount = Math.max(attr.getRecordSaveMaxCount(), 10) - 1;
        template.opsForList().trim(recordKey, 0, maxCount);
    }

    /**
     * 获取中奖记录
     *
     * @param count
     * @param attr
     * @return
     */
    private List<HdztAwardLotteryMsg.Award> getAwardRecord(int count, RollAwardRecordComponentAttr attr) {
        count = Math.max(count, 1) - 1;
        String recordKey = makeKey(attr, AWARD_RECORD_KEY);
        StringRedisTemplate template = actRedisDao.getRedisTemplate(getRedisGroupCode(attr.getActId()));
        List<String> awardRecords = template.opsForList().range(recordKey, 0, count);
        if (CollectionUtils.isEmpty(awardRecords)) {
            return Lists.newArrayList();
        }
        String arrayString = "[" + String.join(",", awardRecords) + "]";
        return JSONArray.parseArray(arrayString, HdztAwardLotteryMsg.Award.class);
    }

    /**
     * 定时产生数据，每六分钟产生一条
     */
    @Scheduled(fixedDelay = 6 * 60 * 1000)
    @ScheduledExt(historyRun = false)
    @NeedRecycle(author = "chenxiazhuan", notRecycle = true)
    public void makeFakeAwardRecord() {

        timerSupport.work("makeFakeAwardRecord", 120, () -> {
            getActivityIds().stream()
                    .filter(it -> actInfoService.inActTime(it.longValue()))
                    .map(this::getAllComponentAttrs)
                    .flatMap(Collection::stream)
                    .forEach(this::makeFakeAwardRecord);

            log.info("makeFakeAwardRecord done");
        });

    }

    @Override
    public void initResources() {
        init();
    }
}
