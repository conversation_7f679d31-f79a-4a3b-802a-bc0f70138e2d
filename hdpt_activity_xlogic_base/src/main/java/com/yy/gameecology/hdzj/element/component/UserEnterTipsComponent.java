package com.yy.gameecology.hdzj.element.component;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.gameecology.activity.bean.UserEnterTemplateEvent;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.UserEnterTipsComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;


/**
 * @Author: CXZ
 * @Desciption: 用户进入模板tips组件
 * @Date: 2021/7/16 15:39
 * @Modified:
 */
@Component
public class UserEnterTipsComponent extends BaseActComponent<UserEnterTipsComponentAttr> {

    @Autowired
    private ActRedisGroupDao actRedisDao;

    public final static String FIRST_KEY = "first_key";


    @Override
    public Long getComponentId() {
        return ComponentId.USER_ENTER_TIPS;
    }

    @HdzjEventHandler(value = UserEnterTemplateEvent.class, canRetry = true)
    public void userEnterTips(UserEnterTemplateEvent event, UserEnterTipsComponentAttr attr) {
        if (!ArrayUtils.contains(attr.getBusiIds(), event.getBusiId())) {
            return;
        }
        long actId = attr.getActId();
        long uid = event.getUid();
        //需要限制首次进入
        if (attr.isNeedFirst()) {
            String key = makeKey(attr, FIRST_KEY);

            String groupCode = getRedisGroupCode(attr.getActId());
            boolean first = actRedisDao.hsetnx(groupCode, key, String.valueOf(uid), DateUtil.getNowYyyyMMddHHmmss());
            if (!first) {
                log.info("userEnterTips ignore not first@uid:{} actId:{} index:{}", uid, actId, attr.getCmptUseInx());
                return;
            }
        }

        RetryTool.withRetryCheck(actId, event.getSeq(), () -> {
            String noticeExt = StringUtil.trim(attr.getNoticeExt());
            Map<String, Object> extJsonMap = noticeExt.isEmpty() ? Maps.newHashMap() : JSON.parseObject(noticeExt);
            extJsonMap.put("tell_you_my_uid_is", uid);
            noticeExt = JSON.toJSONString(extJsonMap);

            GameecologyActivity.CommonNoticeResponse.Builder tips = GameecologyActivity.CommonNoticeResponse.newBuilder()
                    .setActId(actId)
                    .setNoticeType(attr.getNoticeType())
                    .setNoticeValue(attr.getNoticeValue())
                    .setExtJson(noticeExt);

            GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                    .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                    .setCommonNoticeResponse(tips).build();
            svcSDKService.unicastUid(uid, msg);

            log.info("userEnterTips ignore done@uid:{} index:{} msg:{}",
                    uid, attr.getCmptUseInx(), JsonFormat.printToString(tips.build()));
        });
    }


}
