package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022.07.01 18:06
 */
@Data
public class SeedAnchorComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "种子榜单")
    private long seedRankId;
    @ComponentAttrField(labelText = "种子阶段")
    private long seedPhaseId;

    /**
     * 需要用当前种子主播结算发奖的榜单和阶段
     **/
    @ComponentAttrField(labelText = "种子主播结算发奖的榜单和阶段", subFields = {
            @SubField(labelText = "榜单id", type = Long.class, fieldName = Constant.KEY1),
            @SubField(labelText = "阶段id", type = Long.class, fieldName = Constant.VALUE)
    })
    private Map<Long, Long> awardRankPhase;

    /**
     * 胜利方式 1-> pk, 2->踢馆
     **/
    @ComponentAttrField(labelText = "胜利方式", dropDownSourceBeanClass = SeedWinTypeSource.class)
    private int winType;

    /**
     * 该阶段种子选手来源 <rankId,<phaseId,count>>
     **/
    @ComponentAttrField(labelText = "种子选手来源", subFields = {
            @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "榜单id"),
            @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "阶段id"),
            @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "种子主播数量")
    })
    private Map<Long, Map<Long, Integer>> seedSourceCount = new HashMap<>();
    @ComponentAttrField(labelText = "种子奖包id")
    private long seedAwardPackageId;
    @ComponentAttrField(labelText = "种子奖池id")
    private long seedAwardTaskId;

    /**
     * 种子主播奖励加成 10% = * 10/100
     **/
    @ComponentAttrField(labelText = "种子主播奖励加成", remark = "10%则填10")
    private long seedAwardRate;

    /**
     * 种子选手最大奖励值,单位:元
     **/
    @ComponentAttrField(labelText = "种子选手最大奖励值", remark = "单位:元")
    private long seedAwardMax = 20000;
    @ComponentAttrField(labelText = "业务id", dropDownSourceBeanClass = BizSource.class)
    private long busiId;

    /**
     * 发放失败尝试的次数，总共最多调用 1 + retry 次，让发放尽量成功
     **/
    @ComponentAttrField(labelText = "发放重试次数", remark = "发放失败尝试的次数，总共最多调用 1 + retry 次，让发放尽量成功")
    private int retry = 2;

    /**
     * 奖励发放最长重试秒数，超过后丢弃
     **/
    @ComponentAttrField(labelText = "奖励发放最长重试秒数", remark = "超过后丢弃")
    private int issueRetrySeconds = 1800;
}
