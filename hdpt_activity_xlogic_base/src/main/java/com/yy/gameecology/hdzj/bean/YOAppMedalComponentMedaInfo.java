package com.yy.gameecology.hdzj.bean;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrCollector;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class YOAppMedalComponentMedaInfo {

    @ComponentAttrField(labelText = "勋章图标", propType = ComponentAttrCollector.PropType.IMAGE)
    private String medalPic;

    @ComponentAttrField(labelText = "勋章名")
    private String medalName;

    @ComponentAttrField(labelText = "app下载QR code", remark = "LV3是礼物图", propType = ComponentAttrCollector.PropType.IMAGE)
    private String qrCode;
}
