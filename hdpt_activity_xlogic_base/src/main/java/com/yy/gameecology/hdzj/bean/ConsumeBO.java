package com.yy.gameecology.hdzj.bean;

import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2021/7/6
 */
@Getter
@Builder
@ToString
public class ConsumeBO {

    private final Integer appId;
    // required
    private final Long uid;
    // required
    private final Long anchorUid;
    // required
    private final Long sid;
    // required
    private final Long ssid;
    // required
    private final Integer productId;
    // required
    private final Integer productType;
    // required
    private final Long amount;
    // required
    private final Integer usedChannel;
    // required
    private final Long targetUid;
    // required
    private final String seqId;
    // required
    private final String description;
    // required
    private final String expand;

}
