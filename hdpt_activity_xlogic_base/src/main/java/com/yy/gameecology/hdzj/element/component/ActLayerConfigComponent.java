package com.yy.gameecology.hdzj.element.component;

import com.yy.gameecology.activity.bean.hdzt.MemberScoreChanged;
import com.yy.gameecology.activity.service.layer.BroActLayerService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.ActLayerConfigComponentAttr;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * desc:挂件配置组件，专门用于挂件信息配置
 * 由于主动广播目前不方便指定组件索引，所以暂时约定1个活动内只能配置1个组件
 *
 * @createBy 曾文帜
 * @create 2022-07-01 16:35
 **/
@Component
public class ActLayerConfigComponent extends BaseActComponent<ActLayerConfigComponentAttr> {
    @Lazy
    @Autowired
    private BroActLayerService broActLayerService;

    @Override
    public Long getComponentId() {
        return ComponentId.ACT_LAYER_CONFIG;
    }

    @Override
    public boolean isUniq1UseIndex() {
        return true;
    }

    /**
     * 成员分值变化,更新挂件
     **/
    @HdzjEventHandler(value = MemberScoreChanged.class, canRetry = true)
    public void invokeMemberScoreChangedRefreshLayer(MemberScoreChanged event, ActLayerConfigComponentAttr attr) {
        // 中台报名数据异步保存，延迟广播200毫秒，等中台报名数据准备就绪
        Const.EXECUTOR_DELAY_GENERAL.schedule(() -> broActLayerService.memberScoreChangedRefreshLayer(event), 200, TimeUnit.MILLISECONDS);
    }
}
