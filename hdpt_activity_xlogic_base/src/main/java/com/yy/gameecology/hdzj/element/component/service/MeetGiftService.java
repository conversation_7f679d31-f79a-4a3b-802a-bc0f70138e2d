package com.yy.gameecology.hdzj.element.component.service;

import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5146Record;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.element.component.attr.MeetGiftComponentAttr;
import com.yy.gameecology.hdzj.element.component.dao.MeetGiftDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class MeetGiftService {

    public static final String CONSUMED_KEY = "meetGiftConsumed";

    @Autowired
    private MeetGiftDao meetGiftDao;

    @Autowired
    private CommonDataDao commonDataDao;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    protected HdztAwardServiceClient hdztAwardServiceClient;

    public long queryRemaining(MeetGiftComponentAttr attr, Date now) {
        String hashKey = DateFormatUtils.format(now, DateUtil.PATTERN_TYPE2);
        String value = commonDataDao.hashValueGet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), CONSUMED_KEY, hashKey);
        long consumed = 0;
        if (StringUtils.isNumeric(value)) {
            consumed = Long.parseLong(value);
        }

        return Math.max(attr.getDailyLimitCount() - consumed, 0);
    }

    public boolean queryUserReceived(MeetGiftComponentAttr attr, long uid, String hdid) {
        List<Cmpt5146Record> records = meetGiftDao.selectByUidOrHdid(attr.getActId(), uid, hdid);
        return CollectionUtils.isNotEmpty(records);
    }

    public Integer trySendMeetGift(MeetGiftComponentAttr attr, long uid, String hdid, long sid, long ssid, Date date) {
        List<Cmpt5146Record> records = meetGiftDao.selectByUidOrHdid(attr.getActId(), uid, hdid);
        if (CollectionUtils.isNotEmpty(records)) {
            return 1;
        }

        long remaining = queryRemaining(attr, date);
        if (remaining <= 0) {
            return 2;
        }


        final String seq = String.format("mg:%d:%s:%d", attr.getActId(), hdid, uid);
        final String hashKey = DateFormatUtils.format(date, DateUtil.PATTERN_TYPE2);

        return transactionTemplate.execute(status -> {
            CommonDataDao.ValueIncResult result = commonDataDao.hashValueIncreaseIgnoreWithLimit(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), seq, CONSUMED_KEY, hashKey, 1, attr.getDailyLimitCount());
            if (result.isViolateLimit()) {
                return 2;
            }

            Cmpt5146Record record = new Cmpt5146Record();
            record.setActId(attr.getActId());
            record.setUid(uid);
            record.setHdid(hdid);
            record.setSid(sid);
            record.setSsid(ssid);
            record.setCreateTime(date);

            int rs = meetGiftDao.insertIgnore(record);
            if (rs <= 0) {
                status.setRollbackOnly();
                return 1;
            }

            String awardScene = attr.getActId() + StringUtil.VERTICAL_BAR + attr.getAwardScene();
            try {
                hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), attr.getBuisId(), uid, attr.getTAwardTskId(), 1, attr.getTAwardPkgId(), seq, Map.of(HdztAwardServiceClient.AWARD_SCENE, awardScene));
            } catch (Exception e) {
                log.error("trySendMeetGift doWelfareV2 fail:", e);
                status.setRollbackOnly();
                return -1;
            }

            return 0;
        });
    }
}
