package com.yy.gameecology.hdzj.bean;


import com.yy.gameecology.common.consts.PBCommonBannerId;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

/**
 * PC横幅广播配置
 **/

@Data
public class TaskBroadcastConfig{

    /**
     * 横幅id
     */
    @ComponentAttrField(labelText = "横幅id")
    private int bannerId;

    /**
     * 描述描述备注
     */
    @ComponentAttrField(labelText = "描述备注")
    private String desc;
    /**
     * 广播范围类型 2 = 子频道广播，3=顶级频道广播，4=本业务模板内广播，5=活动业务广播
     */
    @ComponentAttrField(labelText = "广播范围", remark = "2 = 子频道广播，3=顶级频道广播，4=本业务模板内广播，5=活动业务广播， 6=家族广播")
    private int broType;


    /**
     * 横幅特效底图url
     */
    @ComponentAttrField(labelText = "横幅特效底图url")
    private String bannerUrl;

    /**
     * 延迟广播毫秒
     */
    @ComponentAttrField(labelText = "延迟广播毫秒")
    private long delayMillSeconds;


    /**
     * 配置扩展字段
     */
    @ComponentAttrField(labelText = "扩展字段")
    private String ext;

    public static enum BroType {
        SUB_CHANNEL(2), TOP_CHANNEL(3), BUSI_TEMPLATE(4), ACT_BUSI(5);
        public final int code;
        BroType(int code) {
            this.code = code;
        }
    }


}
