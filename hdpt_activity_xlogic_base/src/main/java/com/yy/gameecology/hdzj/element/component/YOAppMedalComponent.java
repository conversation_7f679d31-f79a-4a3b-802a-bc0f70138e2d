package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableSet;
import com.yy.gameecology.activity.bean.GiftSourceChannel;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.SendGiftEvent;
import com.yy.gameecology.activity.bean.mq.ZhuiwanLoginEvent;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.UserMedalInfo;
import com.yy.gameecology.hdzj.bean.YOAppMedalComponentMedaInfo;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.YOAppMedalComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@RestController
@RequestMapping("/cmpt/appMedal")
public class YOAppMedalComponent extends BaseActComponent<YOAppMedalComponentAttr> {

    private static final String PRIMARY_MEDAL_TAG = "medal_grant:%d:primary";

    private static final String SENIOR_MEDAL_TAG = "medal_grant:%d:senior";

    private static final String EXPERT_MEDAL_TAG = "medal_grant:%d:expert";

    private static final String EXPERT_ACCUMULATED = "expert_accumulated_amount";

    @Override
    public Long getComponentId() {
        return ComponentId.YO_APP_MEDAL;
    }

    @HdzjEventHandler(value = ZhuiwanLoginEvent.class, canRetry = true)
    public void onZhuiwanLogin(ZhuiwanLoginEvent loginEvent, YOAppMedalComponentAttr attr) {
        final long actId = attr.getActId(), uid = loginEvent.getUid();
        String app = loginEvent.getApp();

        if (!attr.getApps().contains(app)) {
            log.info("onZhuiwanLogin app:{} not match!", app);
            return;
        }

        final long packageId = attr.getSeniorPackageId();
        final String groupCode = redisConfigManager.getGroupCode(actId);
        String grantKey = makeKey(attr, String.format(SENIOR_MEDAL_TAG, packageId));
        boolean set = actRedisDao.hsetnx(groupCode, grantKey, String.valueOf(uid), "1");
        if (!set) {
            log.warn("onZhuiwanLogin no medal to grant, granted already uid:{}, app:{}", uid, app);
            return;
        }

        String seq = "senior:" + packageId + ":" + uid;
        if (SysEvHelper.isDev()) {
            seq = UUID.randomUUID().toString();
        }
        BatchWelfareResult welfareResult = hdztAwardServiceClient.doWelfare(attr.getBusiId(), uid, attr.getMedalTaskId(), 1, packageId, seq);
        log.info("onZhuiwanLogin welfare medal with seq:{}, result:{}", seq, welfareResult);

        //TODO: 去掉发单播，前端不处理noticeType，产品不要lv2弹窗，这里先把这个单播去掉
//        YOAppMedalComponentMedaInfo highMedalInfo = attr.getMedalInfos().get(packageId);
//        YOAppMedalComponentMedaInfo medalInfo = attr.getMedalInfos().get(attr.getPrimaryPackageId());
//        JSONObject json = new JSONObject(6);
//        json.put("medalLevel", "senior");
//        json.put("medalName", medalInfo.getMedalName());
//        json.put("medalPic", medalInfo.getMedalPic());
//        json.put("packageId", packageId);
//        json.put("highMedalName", highMedalInfo.getMedalName());
//        json.put("highMedalPic", highMedalInfo.getMedalPic());
//        json.put("qrCode", medalInfo.getQrCode());
//
//        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
//                .setActId(actId)
//                .setNoticeType("medal_grant_senior")
//                .setExtJson(json.toJSONString());
//
//        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
//                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
//                .setCommonNoticeResponse(panel).build();
//
//        svcSDKService.unicastUid(uid, msg);
        log.info("onZhuiwanLogin success with uid:{}, app:{}, packageId:{}", uid, app, packageId);

    }

    @HdzjEventHandler(value = SendGiftEvent.class, canRetry = true)
    public void onSendGiftEvent(SendGiftEvent sendGiftEvent, YOAppMedalComponentAttr attr) {
        final long actId = attr.getActId(), uid = sendGiftEvent.getSendUid();

        // 只处理PC消息
        if (!GiftSourceChannel.PC.equals(sendGiftEvent.getSourceChannel())) {
            log.info("not pc send gift,uid={}", uid);
            return;
        }

        boolean isNewTemplate = sendGiftEvent.getJsonMap().getBooleanValue("isNewTemplate");
        final String giftId = sendGiftEvent.getGiftId();
        Set<String> giftIds = ImmutableSet.copyOf(attr.getGiftIds());
        if (!giftIds.contains(giftId)) {
            return;
        }

        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(redisConfigManager.getGroupCode(actId));
        String hashKey = String.valueOf(uid);

        // 处理3级勋章
        long expertPackageId = attr.getExpertPackageId();
        if (expertPackageId > 0) {
            String expertKey = makeKey(attr, String.format(EXPERT_MEDAL_TAG, expertPackageId));
            if (redisTemplate.opsForHash().hasKey(expertKey, hashKey)) {
                log.warn("onSendGiftEvent no medal to grant, expert granted already uid:{}", uid);
                return;
            }

            // 3级勋章只能通过新模板礼物点亮
            if (isNewTemplate) {
                long priceSum = sendGiftEvent.getGiftAmount() * sendGiftEvent.getGiftNum();
                if (priceSum >= attr.getThreshold() || accumulated(attr, redisTemplate, hashKey, priceSum) >= attr.getThreshold()) {
                    // grant level 3
                    grantExpertMedal(attr, redisTemplate, expertKey, uid);
                    return;
                }
            }
        }

        //先判断是否已经获得了2级勋章
        long seniorPackageId = attr.getSeniorPackageId();
        String seniorKey = makeKey(attr, String.format(SENIOR_MEDAL_TAG, seniorPackageId));
        if (redisTemplate.opsForHash().hasKey(seniorKey, hashKey)) {
            log.warn("onSendGiftEvent no medal to grant, senior granted already uid:{}", uid);
            return;
        }

        long packageId = attr.getPrimaryPackageId();
        String grantKey = makeKey(attr, String.format(PRIMARY_MEDAL_TAG, packageId));
        boolean set = redisTemplate.opsForHash().putIfAbsent(grantKey, hashKey, DateUtil.getNowYyyyMMddHHmmss());
        if (!set) {
            log.warn("onSendGiftEvent no medal to grant, granted already uid:{}", uid);
            return;
        }

        String seq = "primary:" + packageId + ":" + uid;
        if (SysEvHelper.isDev()) {
            seq = UUID.randomUUID().toString();
        }
        BatchWelfareResult welfareResult = hdztAwardServiceClient.doWelfare(attr.getBusiId(), uid, attr.getMedalTaskId(), 1, packageId, seq);
        log.info("onSendGiftEvent welfare medal with seq:{}, result:{}", seq, welfareResult);
        YOAppMedalComponentMedaInfo medalInfo = attr.getMedalInfos().get(packageId);
        YOAppMedalComponentMedaInfo highMedalInfo = attr.getMedalInfos().get(attr.getSeniorPackageId());
        YOAppMedalComponentMedaInfo expertMedalInfo = attr.getMedalInfos().get(attr.getExpertPackageId());
        JSONObject json = new JSONObject(15);
        json.put("medalLevel", "primary");
        json.put("medalName", medalInfo.getMedalName());
        json.put("medalPic", medalInfo.getMedalPic());
        json.put("packageId", packageId);
        json.put("highMedalName", highMedalInfo.getMedalName());
        json.put("highMedalPic", highMedalInfo.getMedalPic());
        json.put("qrCode", medalInfo.getQrCode());

        if (expertMedalInfo != null) {
            json.put("expertMedalName", expertMedalInfo.getMedalName());
            json.put("expertMedalPic", expertMedalInfo.getMedalPic());
            json.put("expertMedalGift", expertMedalInfo.getQrCode());
        }

        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(actId)
                .setNoticeType("medal_grant_primary")
                .setExtJson(json.toJSONString());

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();

        RetryTool.withRetryCheck(actId, seq, () -> {
            svcSDKService.unicastUid(uid, msg);
            log.info("onSendGiftEvent success with uid:{}, packageId:{}", uid, packageId);
        });
    }

    /**
     * 累计uid已送了多少金额的礼物（单位：厘）
     * @param attr
     * @param redisTemplate
     * @param hashKey String.valueOf(uid)
     * @param priceSum 当前增加的新模板礼物金额（单位：厘）
     * @return 增加后当前已累计的新模板礼物金额（单位：厘）
     */
    private long accumulated(YOAppMedalComponentAttr attr, StringRedisTemplate redisTemplate, String hashKey, long priceSum) {
        String key = makeKey(attr, EXPERT_ACCUMULATED);
        return redisTemplate.opsForHash().increment(key, hashKey, priceSum);
    }

    private void grantExpertMedal(YOAppMedalComponentAttr attr, StringRedisTemplate redisTemplate, String expertKey, long uid) {
        String hashKey = String.valueOf(uid);
        final long actId = attr.getActId(), packageId = attr.getExpertPackageId();
        boolean set = redisTemplate.opsForHash().putIfAbsent(expertKey, hashKey, DateUtil.getNowYyyyMMddHHmmss());
        if(!set) {
            log.warn("grantExpertMedal no medal to grant, granted already uid:{}", uid);
            return;
        }

        String seq = "expert:" + packageId + ":" + uid;
        if (SysEvHelper.isDev()) {
            seq = UUID.randomUUID().toString();
        }
        BatchWelfareResult welfareResult = hdztAwardServiceClient.doWelfare(attr.getBusiId(), uid, attr.getMedalTaskId(), 1, packageId, seq);
        log.info("grantExpertMedal welfare medal with seq:{}, result:{}", seq, welfareResult);

        //send notice
        YOAppMedalComponentMedaInfo medalInfo = attr.getMedalInfos().get(packageId);
        JSONObject json = new JSONObject(10);
        json.put("medalLevel", "expert");
        json.put("medalName", medalInfo.getMedalName());
        json.put("medalPic", medalInfo.getMedalPic());

        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(actId)
                .setNoticeType("medal_grant_expert")
                .setExtJson(json.toJSONString());

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();

        RetryTool.withRetryCheck(actId, seq, () -> {
            svcSDKService.unicastUid(uid, msg);
            log.info("grantExpertMedal success with uid:{}, packageId:{}", uid, packageId);
        });
    }

    @GetMapping("/queryMyMedalInfo")
    public Response<UserMedalInfo> queryMyMedalInfo(@RequestParam(name = "actId") long actId, @RequestParam(name = "cmptInx") long cmptInx,
                                                    HttpServletRequest request, HttpServletResponse response) {
        long uid = getLoginYYUid(request, response);

        YOAppMedalComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "Invalid actId!");
        }

        final long primaryPackageId = attr.getPrimaryPackageId();
        final long seniorPackageId = attr.getSeniorPackageId();
        final long expertPackageId = attr.getExpertPackageId();

        UserMedalInfo userMedalInfo = new UserMedalInfo();
        userMedalInfo.setAppName(attr.getAppName());
        //添加礼物icon、勋章资源信息
        userMedalInfo.setPrimaryMedal(attr.getMedalInfos().get(primaryPackageId));
        userMedalInfo.setSeniorMedal(attr.getMedalInfos().get(seniorPackageId));
        userMedalInfo.setExpertMedal(attr.getMedalInfos().get(expertPackageId));
        List<String> giftIcons = attr.getGiftIds().stream().map(giftId -> attr.getGiftIcons().get(giftId)).filter(Objects::nonNull).collect(Collectors.toList());
        userMedalInfo.setGiftIcons(giftIcons);

        final String groupCode = redisConfigManager.getGroupCode(actId);
        String expertKey = makeKey(attr, String.format(EXPERT_MEDAL_TAG, expertPackageId));
        boolean exist = actRedisDao.hExists(groupCode, expertKey, String.valueOf(uid));
        if (exist) {
            userMedalInfo.setMedalLevel("expert");
            return Response.success(userMedalInfo);
        }
        String grantKey = makeKey(attr, String.format(SENIOR_MEDAL_TAG, seniorPackageId));
        exist = actRedisDao.hExists(groupCode, grantKey, String.valueOf(uid));
        if (exist) {
            userMedalInfo.setMedalLevel("senior");
            return Response.success(userMedalInfo);
        }
        grantKey = makeKey(attr, String.format(PRIMARY_MEDAL_TAG, primaryPackageId));
        exist = actRedisDao.hExists(groupCode, grantKey, String.valueOf(uid));
        if (exist) {
            userMedalInfo.setMedalLevel("primary");
        }

        return Response.success(userMedalInfo);
    }

    @GetMapping("/clearMyMedalInfo")
    public Response<String> clearMyMedalInfo(@RequestParam(name = "actId") long actId, @RequestParam(name = "cmptInx") long cmptInx,
                                             HttpServletRequest request, HttpServletResponse response) {
        if (SysEvHelper.isDeploy()) {
            return Response.ok("线上环境不允许操作");
        }
        long uid = getLoginYYUid(request, response);
        YOAppMedalComponentAttr attr = getUniqueComponentAttr(actId);
        if (attr != null) {
            String groupCode = getRedisGroupCode(actId);
            String grantKey = makeKey(attr, String.format(PRIMARY_MEDAL_TAG, attr.getPrimaryPackageId()));
            actRedisDao.hdel(groupCode, grantKey, String.valueOf(uid));
            String seniorKey = makeKey(attr, String.format(SENIOR_MEDAL_TAG, attr.getSeniorPackageId()));
            actRedisDao.hdel(groupCode, seniorKey, String.valueOf(uid));
            String expertKey = makeKey(attr, String.format(EXPERT_MEDAL_TAG, attr.getExpertPackageId()));
            actRedisDao.hdel(groupCode, expertKey, String.valueOf(uid));
            String accumulatedKey = makeKey(attr, EXPERT_ACCUMULATED);
            actRedisDao.hdel(groupCode, accumulatedKey, String.valueOf(uid));
        }
        return Response.ok();
    }


}
