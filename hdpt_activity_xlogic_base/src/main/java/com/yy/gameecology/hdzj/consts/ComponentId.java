package com.yy.gameecology.hdzj.consts;


/**
 * 组件标识，取值为 [1000, 9999]，按通用性和稳定性又分成3组
 * [1001 ~ 1999] - 最通用稳定
 * [2000 ~ 3999] - 较通用稳定
 * [5000 ~ 9999] - 不通用不稳定
 */
public class ComponentId {
    /**
     * 示例奖组件ID
     */
    public static final long EXAMPLE = 1000;

    //----------------------------------------------------------------------------------------
    // 1. 组件ID取值范围 [1001 ~ 1999]，这类组件一般功能专一内聚，高频使用，逻辑稳固少变
    //----------------------------------------------------------------------------------------
    /**
     * 按榜单所在名次做奖励的部件
     */
    public static final long RANKING_RANK_AWARD = 1001;

    /**
     * 按榜单中的分值做奖励的部件
     */
    public static final long RANKING_SCORE_AWARD = 1002;

    /**
     * 按阶段所在名次做奖励的部件
     */
    public static final long PHASE_RANK_AWARD = 1003;

    /**
     * 按阶段所在分值做奖励的部件
     */
    public static final long PHASE_SCORE_AWARD = 1004;

    /**
     * 赛点广播组件ID
     */
    public static final long MATCH_POINT_NOTIFY = 1005;

    /**
     * 赛果广播组件ID
     */
    public static final long MATCH_RESULT_NOTIFY = 1006;


    /**
     * 挂件配置组件
     */
    public static final long ACT_LAYER_CONFIG = 1008;


    public static final long RANK_2_RANK = 1011;

    /**
     * 阶段top n 瓜分固定数量奖励
     */
    public static final long TOP_N_SPILT_AWARD = 1012;

    /**
     * 给前端用的计数器，用于实现首次进入页面展示XXX，第几次进入页面展示XXX等
     */
    public static final long COUNTER = 1013;

    /**
     * 给榜单贡献者top n发奖
     */
    public static final long RANKING_CONTRIBUTE_AWARD = 1014;

    /**
     * 日任务组件--支持子任务
     */
    public static final long DAY_TASK = 1015;

    /**
     * 从5066迁移而来，改造成MySQL
     */
    public static final long YO_MEDAL_MYSQL = 1016;
    /**
     * 日任务组件--支持子任务(mysql)
     */
    public static final long DAY_TASK2 = 1017;

    /**
     * 限额控制(mysql)
     */
    public static final long LIMIT_CONTROL = 1018;

    public static final long ONLINE_NOTICE = 1019;


    //末尾提示：
    //
    //！！！！！注意，在代码新增组件id前，必须先在这个表新增记录避免不同代码组件id冲突！！！！！！！
    //select *from hdzk.hdzj_component_define


    //----------------------------------------------------------------------------------------
    // 2. 组件ID取值范围 [2000 ~ 3999]，这类组件一般实现较复杂功能，含活动业务逻辑，使用参数化应对需求变化
    //----------------------------------------------------------------------------------------


    /**
     * 告白动效组件ID
     */
    public static final long SHOW_LOVE = 2002;

    /**
     * 送礼抽奖组件ID
     */
    public static final long SEND_GIFT_LOTTERY = 2003;

    /**
     * 礼包组件：YB支付
     */
    public static final long GIFT_PACKAGE_BUY_YB_PAY = 2004;

    /**
     * 尾灯组件ID
     */
    public static final long TAILLIGHT_TASK = 2005L;

    /**
     * 主播过任务提醒组件ID
     */
    public static final long ANCHOR_TASK_TIP = 2006L;

    /**
     * 发奖广播组件ID
     */
    public static final long AWARD_BROADCAST = 2007L;

    /**
     * 发奖单播组件ID
     *
     * 改用 2065L
     */
    public static final long AWARD_UNICAST = 2008L;

    /**
     * 开播横幅组件ID
     */
    public static final long ANCHOR_START_SHOW_BANNER = 2009;

    /**
     * 积分卡、荣耀卡组件ID
     */
    public static final long RANKING_CARD = 2010;

    /**
     * 抽奖记录组件ID
     */
    public static final long LOTTERY_RECORD = 2011;

    /**
     * 榜单展示配置 交友右侧公屏榜单tab配置
     */
    public static final long RANKING_SHOW = 2012;

    /**
     * 榜单-送礼频道数标签
     * 这个废弃掉，新的用mysql版本---> 2066
     */
    public static final long CHANNEL_COUNT_LABEL = 2013;

    /**
     * 活动飞机
     */
    public static final long ACTIVITY_PLANE = 2014;

    /**
     * 礼包组件
     */
    public static final long GIFT_PACKAGE_BUY_V2 = 2015;


    /**
     * 用户进入模板tips组件
     */
    public static final long USER_ENTER_TIPS = 2016;

    /**
     * 主播上麦tips组件
     */
    public static final long ANCHOR_START_SHOW_TIPS = 2017;


    /**
     * 按榜单中的分值做奖励的领奖资格
     */
    public static final long RANKING_SCORE_AWARD_QUA = 2019;

    /**
     * 奖励领取组件
     */
    public static final long RECEIVE_REWARD = 2020;


    /**
     * 礼包组件 新用户礼包:扫码支付
     */
    public static final long GIFT_PACKAGE_BUY_QRCODE_PAY = 2021;


    /**
     * 送礼发奖组件。送礼的价值，取模发奖
     */
    public static final long SEND_GIFT_AWARD = 2022;


    /**
     * PK ko组件
     */
    public static final long PK_KO = 2023;

    /**
     * 通知活动开始组件
     */
    public static final long NOTIFY_ACTIVITY_START = 2024;

    /**
     * 通知活动结束组件
     */
    public static final long NOTIFY_ACTIVITY_END = 2025;

    /**
     * 根据榜单瓜分奖池
     */
    public static final long CARVE_POOL = 2026;

    /**
     * 中奖记录滚屏组件
     */
    public static final long ROLL_AWARD_RECORD = 2027;

    /**
     * 根据榜单分值,按阶梯触发横幅广播
     */
    public static final long RANKING_SCORE_BANNER = 2028;

    /**
     * 查询用户任务进度组件
     **/
    public static final long RANKING_TASK_INFO_QUERY = 2029;

    /**
     * 榜单-送礼频道数标签---mysql版本
     */
    public static final long CHANNEL_COUNT_LABEL2 = 2066;

    /**
     * 榜单迁移主键
     * 将一个榜单的数值累榜到另一个榜单
     * 需求场景：交友积分赛分4个赛季,有一个总榜需要累计4个赛季的积分
     * 考虑到灰度数据的清理,在新赛季开始时,将上一个赛季的榜单数据迁移到当前赛季的总榜
     **/
    public static final long RANK_MIGRATION = 2030;

    /**
     * 手动抽奖组件, 送礼等产生抽奖机会（也可绝对指定hash或zset存的抽奖机会key），用户手动选择奖池抽奖
     */
    public static final long MANUAL_LOTTERY = 2032;

    /**
     * 挑战擂主组件, 打擂、踢馆，胜者成新擂主，败者又成挑战者
     */
    public static final long TI_GUAN = 2033;

    /**
     * 个人任务（又名：个人专属任务）
     */
    public static final long PERSONAL_TASK = 2034;

    /**
     * 交友送礼抽奖,使用交友连送结束事件
     */
    public static final long JY_SEND_GIFT_LOTTERY = 2035;


    /**
     * 活动战报自动化组件
     */
    public static final long ACTIVITY_GLORY_REPORT = 2036;

    public static final long ACTIVITY_MILESTONE_REPORT = 2037;

    /**
     * 种子主播结合踢馆组件
     */
    public static final long SEED_ANCHOR_WITH_TIGUAN = 2039L;

    /**
     * 种子主播
     */
    public static final long SEED_ANCHOR = 2041L;

    public static final long MATCH_RESULT_BRO_ONLINE = 2042L;

    public static final long SUPPER_PC_BANNER = 2043L;


    /**
     * 奖池余额组件
     */
    public static final long AWARD_POOL_BALANCE = 2044L;

    /**
     * 完成任务自动上报组件
     * 监听到任务变化，重新组织数据上报榜单，用于多个子任务组合起来可完成1个新任务
     */
    public static final long TASK_CHANGE_UPDATE_RANK = 2045;

    /**
     * 多个子任务组合成1个新任务展示
     */
    public static final long MULTIPLE_TASK_SHOW = 2046;

    /**
     * 过任务抽奖组件
     * 过任务的时候自动触发抽奖
     */
    public static final long TASK_CHANGE_LOTTERY = 2047;

    /**
     * App 活动飞机
     **/
    public static final long TURNOVER_COMMON_PLANE = 2048L;

    /**
     * app 神豪荣耀
     **/
    public static final long SUPPER_APP_BANNER = 2049L;

    /**
     * 通用任务进度信息查询接口（逐步取代 RANKING_TASK_INFO_QUERY）
     */
    public static final long RANKING_TASK_QUERY = 2050L;


    /**
     * 根据榜单任务横幅广播组件
     * 霸屏横幅、高光横幅，同时支持pc和app，用于替代2028和2049组件
     **/
    public static final long RANKING_TASK_BANNER = 2051L;

    /**
     * 发奖广播组件ID
     */
    public static final long AWARD_APP_BROADCAST = 2052L;


    /**
     * 榜单展示配置
     * 有的参数不适合前端传递，可配置在这个组件控制榜单特定展示
     */
    public static final long RANK_BUILDER = 2053L;

    /**
     * 交友大额充返
     */
    public static final long RECHARGE_REBATE = 2054;

    /**
     * 白名单组件
     */
    public static final long WHITE_LIST = 2055;

    public static final long ACT_SHOP = 2056;

    public static final long ANCHOR_START_SHOW_BANNER_MYSQL = 2060;

    /**
     * 记录某个主持给他送礼的最后一个神豪，支持多个绑定合为一组记录
     */
    public static final long LATEST_CP = 2061;

    /**
     * 直播间公屏口令抽奖（rewrite from 5114）
     */
    public static final long WATCHWORD_LOTTERY = 2062;

    /**
     * 任务变化发奖
     */
    public static final long CP_TASK_CHANGE_WELFARE = 2063;

    /**
     * 收集cp信息，查询与某个uid组成的所有cp
     */
    public static final long CP_INFO = 2064;

    /**
     * 发奖单播组件ID
     * from 2008L
     */
    public static final long AWARD_UNICAST2 = 2065;

    /**
     * yo首页弹窗控制组件
     */
    public static final long YO_POP_CONTROL = 2068;

    //末尾提示：
    //
    //！！！！！注意，在代码新增组件id前，必须先在这个表新增记录避免不同代码组件id冲突！！！！！！！
    //select *from gameecology.hdzj_component_define


    //----------------------------------------------------------------------------------------
    // 3. 组件ID取值范围 [5000 ~ 9999]，临时定制使用 或没想清楚怎么完美实现的组件，
    //    可含特定活动的硬编码逻辑，后期可总结提炼为更通用的组件（用 1001 ~ 3999 的组件ID重新实现）
    //----------------------------------------------------------------------------------------

    /**
     * 交友用户公会数量组件
     */
    public static final long JY_USER_CHANNEL_COUNT_LABEL = 5000L;

    /**
     * 勋章任务组件ID
     */
    public static final long MEDAL_TASK = 5001L;

    /**
     * 商店组件
     */
    public static final long SHOP = 5002L;


    /**
     * 陪玩充能条
     */
    public static final long POWER_CHARGE = 5003L;

    /**
     * pk宣战组件
     */
    public static final long PK_FIRE = 5004L;

    /**
     * 桃花还愿屋组件
     */
    public static final long VOTIVE_HOUSE = 5005L;

    /**
     * 追玩战力团
     */
    public static final long WARFARE_RAGIMENT = 5006L;

    /**
     * 新用户首次进模板
     */
    public static final long NEW_USER_FIRST_ENTER = 5007L;


    /**
     * 勋章任务组件ID -新
     */
    public static final long MEDAL_TASK_V2 = 5008L;

    /**
     * 里程碑组件ID
     */
    public static final long MILE_STONE = 5009L;

    /**
     * 小时真爱榜
     */
    public static final long TRUE_LOVE_CP = 5010L;

    public static final long PK_REWARD = 5011L;

    /**
     * 追玩约会插件任务
     */
    public static final long ZHUIWAN_DATING_TASK = 5015L;

    /**
     * 赏金pk
     */
    public static final long ANCHOR_BONUS_PK = 5018L;

    /**
     * 首次进入模板 弹窗
     */
    public static final long FIRST_ENTER_TEMP = 5019L;

    /**
     * 商店组件
     */
    public static final long ZHUIWAN_DATING_EXCHANGE = 5017L;


    /**
     * 里程碑V2组件ID
     */
    public static final long MILE_STONE_V2 = 5020L;

    /**
     * 里程碑V2组件ID
     */
    public static final long JIN_NANG_BRO = 5021L;

    /**
     * 荣耀殿堂-名人堂
     */
    public static final long HONOR_HALL_RANK = 5022L;

    /**
     * 荣耀殿堂-pk记录
     */
    public static final long HONOR_HALL_PK_RECORD = 5023L;


    public static final long SUNSHINE_TASK = 5025L;

    public static final long ACT_COMBINE = 5016L;

    /**
     * 荣耀殿堂-名人堂
     */
    public static final long HONOR_HALL_RANK_V2 = 5027L;

    public static final long HONOR_HALL_PK_RECORD_V2 = 5028L;

    public static final long MEMBER_ROLE_CHANGE = 5029L;

    /**
     * 
     */
    public static final long SUPER_WINNER = 5030L;

    public static final long CHANNEL_ARENA = 5034L;

    public static final long MILE_STONE_V3 = 5035L;

    /**
     * 高光任务:每天凌晨结算前一天的奖励
     */
    public static final long SUNSHINE_TASK_DAILY_AWARD = 5036L;

    /**
     * 临时调整-淘汰成员掉段
     */
    public static final long MEMBER_ROLE_CHANGE_V2 = 5037L;

    /**
     * 许愿瓶组件-一种用户送礼触发特效，点击手工抽奖组件
     */
    public static final long WISHING_BOTTLE = 5038;

    public static final long NIANSHOU_CHANNEL = 5039;

    public static final long GAMEBABY_MONTH_ACT = 5040;

    public static final long KING_LIFT = 5041;

    /**
     * 用户付费成长组件
     **/
    public static final long USER_PAID_GROWTH = 5044;


    /**
     * 榜单分数变化通知-目前是交友积分赛定制
     */
    public static final long RANK_SCORE_NOTICE = 5045;

    /**
     * 里程碑横幅
     **/
    public static final long MILESTONE_BANNER = 5046;

    /**
     * PK反超
     **/
    public static final long PK_OVERTAKE = 5047;

    /**
     * 按照白名单晋级不同赛道并进行pk对阵编排
     */
    public static final long WHITE_LIST_PROMOTE = 5048;


    /**
     * 公会白名单任务/公会高光任务
     */
    public static final long CHANNEL_WHITE_LIST_TASK = 5049;

    /**
     * 能量站
     */
    public static final long ENERGY_STATION = 5050;

    /**
     * 主播高光任务:完成任务后自动发奖
     */
    public static final long SUNSHINE_TASK_AUTO_AWARD = 5051;

    /**
     * 主播战队赛
     **/
    public static final long ANCHOR_TEAM = 5052;

    /**
     * 强厅任务
     */
    public static final long SUB_CHANNEL_TASK = 5053;

    /**
     * 黑马
     * 淘汰主播转移至分赛场
     */
    public static final long KNOCKOUT_ANCHOR_TRANSFER = 5054;

    /**
     * PK调整组件
     */
    public static final long TOP_N_DO_NOT_PK = 5055;

    public static final long WHITE_LIST_GROUP = 5056;

    public static final long PURE_WHITE_LIST_PROMOTE = 5057;

    /**
     * 突破任务、主播任务 可支持配置是否自动更新前一天最高值
     */
    public static final long ANCHOR_CUSTOM_TASK = 5058;


    public static final long PK_PLUNDER = 5059;

    public static final long ENTER_CHANNEL_POPUP = 5060;

    public static final long NIANSHOW_ANCHOR = 5061;

    /**
     * 星际宝藏
     */
    public static final long INTERSTELLAR_TREASURE = 5062;

    public static final long BLACK_LIST = 5063;

    /**
     * 乱斗墙组件
     **/
    public static final long CHANNEL_FIGHT_WALL = 5065;

    public static final long YO_APP_MEDAL = 5066;

    /**
     * 新用户礼包
     * 需求地址：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/4xX96ppH_s/epTzgBOKWe_Qo0
     **/
    public static final long NEW_USER_PACKAGE = 5064;

    public static final long NOVICE_PACKAGE = 5068;

    /**
     * 补刀玩法，全服循环过任务，完成任务的cp获取抽奖奖励。类似玩法曾出现在：23年语音房情人节;语音房520;交友520;语音房夏日庆典;交友七夕活动定制组件，因此提取为组件
     */
    public static final long ALL_USER_TASK = 5069;

    /**
     * 王者明星赛组件
     **/
    public static final long KING_OF_STAR = 5067;

    /**
     * Yo礼包 多业务礼包
     */
    public static final long MULTI_NOVICE_PACKAGE = 5070;

    /**
     * 过任务-贡献者抽奖
     * 过任务，参与任务的角色可参与抽奖，首次出现于23年交友、宝贝国庆活动
     */
    public static final long TASK_CONTRIBUTE_LOTTERY = 5071;

    /**
     * 过任务-弹窗
     * 过任务，完成任务的角色弹窗
     */
    public static final long TASK_COMPLETE_POP_UP = 5072;

    public static final long WATCHWORD_STAKE = 5073;

    /**
     * 神豪成就
     * 神豪过任务，和收礼最多的主持一起得奖
     */
    public static final long USER_TASK_ACHIEVE = 5074;


    public static final long MANUAL_BANNER = 5077;


    public static final long LUCK_LOTTERY = 5075;
    public static final long FLIPPING_CARD = 5076;

    /**
     * 王者荣耀赏金赛
     */
    public static final long WZRY_GAME = 5078;


    /**
     * 王者荣耀任务
     */
    public static final long WZRY_TASK = 5080;


    /**
     * 神龙秘宝
     * 首次活动需求：<a href="https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/SB5xNxJC_l/4xX96ppH_s/u8V3GVmUMrbBXg">语音房春节活动</a>
     * actId：2024012001
     */
    public static final long DRAGON_TREASURE = 5079;

    /**
     * 烟火玩法（星光庆典玩法）<br/>
     * 活动需求：<a href="https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/SB5xNxJC_l/4xX96ppH_s/1oMej8CyYCMCUs">交友宝贝春节活动</a>
     */
    public static final long FIREWORK = 5081;

    /**
     * 小时榜星光玩法
     */
    public static final long HOUR_STAR = 5082;

    /**
     * 赛果霸屏
     * 支持不同榜单合并下发广播
     */
    public static final long MATCH_BIG_BANNER = 5083;


    /**
     * 过任务发奖提醒
     */
    public static final long TASK_COMPLETE_AWARD_NOTICE = 5084;


    /**
     * 弹幕游戏定制组件
     */
    public static final long DANMAKU_GAME = 5085;


  /**
     * 挚爱CP-夏季冠军杯
     */
    public static final long CHAMPION_CP = 5087;

    /**
     * 爱情卡位战组件 <br/>
     * 活动需求：<a href="https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/SB5xNxJC_l/4xX96ppH_s/e_5_a4LwrNYG3T">520烂漫爱情盛典</a>
     */
    public static final long SLOT_GRAB = 5088;

    /**
     * 恋爱日记组件
     */
    public static final long CP_DIARY = 5089;

    /**
     * 决赛拍卖提醒
     */
    public static final long AUCTION_NOTICE = 5090;

    /**
     * 主持福利
     */
    public static final long ANCHOR_WELFARE_TASK = 5091;

    /**
     * 主持福利入口引导
     */
    public static final long ANCHOR_WELFARE_TASK_ENTRY = 5093;


    /**
     * 漂流瓶
     */
    public static final long DRIFT_BOTTLE= 5094;


    /**
     * cp集拼图玩法
     */
    public static final long CP_PUZZLE = 5095;
	
	 /**
     * 殿堂CP
     */
    public static final long HALL_CP = 5097;

    /**
     * 鹊桥竞速赛
     */
    public static final long CP_RACE = 5096;


    /**
     * 七夕相会
     */
    public static final long CP_MEET = 5098;

    /**
     * cp多拼图玩法
     */
    public static final long CP_MULTI_PUZZLE = 5099;


    /**
     * 风筝系情缘
     */
    public static final long CP_KITE = 5100;

    /**
     * 开黑皮肤
     */
    public static final long KAI_HEI_SKIN = 5108;


    public static final long KAI_HEI_SKIN_PORTAL = 5109;

    /**
     * 开黑皮肤-发放Q币
     */
    public static final long KAI_HEI_QCOINS = 5110;

    /**
     * 都市漫游
     */
    public static final long CP_CITY_WALK = 5105;

    /**
     * 旅行日记
     */
    public static final long CP_TRAVEL_DIARY = 5106;

    /**
     * 神豪成就
     */
    public static final long USER_ACHIEVEMENT = 5111;

    /**
     * 浪漫巅峰
     */
    public static final long  CP_PEAK_ROMANCE = 5112;


    /**
     * 口令抽奖
     */
    public static final long  CHANNEL_CHAT_LOTTERY = 5114;


    /**
     * 一飞冲天
     * 起飞的时候才能累的任务
     */
    public static final long CP_SHORT_TIME_TASK = 5113;

    /**
     * 主持小时pk玩法
     */
    public static final long ANCHOR_HOUR_PK = 5116;

    /**
     * 花火之约
     */
    public static final long SPARK_PROMISE = 5123;

    /**
     * 阵营争夺
     */
    public static final long CAMP_COMPETE = 5124;

    public static final long AOV_GAME_PHASE = 5115;

    public static final long AOV_MATCH = 5117;

    public static final long AOV_GAME = 5118;

    public static final long AOV_AWARD = 5119;

    public static final long AOV_RANK = 5120;

    public static final long AOV_PUSH = 5121;

    public static final long AOV_TEAM = 5126;

    /**
     * 高光时刻
     */
    public static final long HEADLINE_TIME = 5125;

    /**
     * 魔兽世界入口控制
     */
    public static final long WOW_ACTIVITY_ENTER_CONTROL = 5127;

    /**
     * 魔兽金币活动登录任务组件
     */
    public static final long WOW_ACTIVITY_LOGIN = 5128;

    /**
     * 魔兽金币push推送组件
     */
    public static final long WOW_ACTIVITY_MESSAGE_PUSH = 5129;
    /**
     * 魔兽金币打卡任务
     */
    public static final long WOW_TASK = 5130;

    public static final long TASK_COMPLETE_AWARD_NOTICE_MYSQL = 5131;

    public static final long AUCTION_NOTICE_MYSQL = 5132;

    public static final long CP_LOTTERY_MYSQL = 5133;
    /**
     * 和平精英巅峰赛---赛程相关组件
     */
    public static final long PEPC_PHASE = 5140;

    /**
     *和平精英巅峰赛---与腾讯游戏对接结算相关
     */
    public static final long PEPC_GAME = 5141;

    /**
     * 和平精英巅峰赛---队伍组件
     */
    public static final long PEPC_TEAM = 5142;

    /**
     * 和平精英巅峰赛--领奖接口
     */
    public static final long PEPC_AWARD = 5143;

    /**
     * 和平精英巅峰赛--推送接口
     */
    public static final long PEPC_PUSH= 5144;

    /**
     * 和平精英巅峰赛--APP弹窗接口
     */
    public static final long PEPC_APP_POP= 5145;

    /**
     * 和平精英巅峰赛--榜单
     */
    public static final long PEPC_RANK= 5147;

    /**
     * 拼图玩法-- mysql
     */
    public static final long PUZZLE = 5150;

    /**
     * cp挂件，每个人显示的数据不一样
     */
    public static final long LAYER_INDIVIDUAL_CP = 5151;


    public static final long MEET_PRESENT = 5146;

    /**
     * 恋爱乐园
     * <a href="https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/SB5xNxJC_l/4xX96ppH_s/0NzC87rRxQleZN">25年聊天室520活动</a>
     */
    public static final long ROMANCE_WONDERLAND = 5148;

    /**
     * 甜蜜摩天轮
     * <a href="https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/SB5xNxJC_l/4xX96ppH_s/0NzC87rRxQleZN">25年聊天室520活动</a>
     */
    public static final long SWEET_FERRIS_WHEEL = 5149;

    public static final long WATCHWORD_LOTTERY_QUERY = 5152;

    /**
     * 邀请拉新任务
     */
    public static final long INVITE_TASK = 5156;

    public static final long AOV_INVITE_ACT_SHOP = 5157;

    /**
     * 皮肤组件
     */
    public static final long SKIN_SELECT = 5158;


    //末尾提示：
    //
    //！！！！！注意，在代码新增组件id前，必须先在这个表新增记录避免不同代码组件id冲突！！！！！！！
    // select * from hdzk.hdzj_component_define;
}
