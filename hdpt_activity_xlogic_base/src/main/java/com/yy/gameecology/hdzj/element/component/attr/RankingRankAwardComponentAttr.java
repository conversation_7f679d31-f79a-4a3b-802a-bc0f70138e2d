package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.RankTypeSource;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.attrconfig.YesNoSource;

import java.util.Map;

/**
 * 功能描述:按榜单所在名次做奖励的组件
 *
 * <AUTHOR>
 * @date 2021/4/8 22:27
 */
public class RankingRankAwardComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "重试次数", remark = "每个发放错误尝试的次数，总共最多调用 1 + retry 次, 让发放尽量成功,默认值为2")
    // 每个发放错误尝试的次数，总共最多调用 1 + retry 次， 让发放尽量成功
    private int retry = 2;

    @ComponentAttrField(labelText = "榜单类型", remark = "1-当前比拼的榜 2-阶段晋级过来的初始名单（此时dateStr不生效） 3- 榜单贡献来源", dropDownSourceBeanClass = RankTypeSource.class)
    //1-当前比拼的榜 2-阶段晋级过来的初始名单（此时dateStr不生效） 3- 榜单贡献来源
    private String rankType = "1";

    @ComponentAttrField(labelText = "成员下标", remark = "榜单成员有可能是用 | 分隔的组合值,用来指示取哪个组合部分,默认为0（取第一个）")
    //榜单成员有可能是用 | 分隔的组合值，用来指示取哪个组合部分，默认为0（取第一个）
    private int receiverInx = 0;

    @ComponentAttrField(labelText = "榜单ID", remark = "筛选榜单ID,输入榜单ID和此处不同不处理")
    // 筛选榜单ID，输入榜单ID和此处不同不处理
    private long rankId = 0;

    @ComponentAttrField(labelText = "主榜单ID", remark = "主榜单ID,若大于0,则根据 主榜的名次 位置找出成员,用于 榜单id 形成贡献榜完整 key")
    // 主榜单ID， 若大于0，则根据 primaryRankPos 位置找出成员，用于 rankId 形成贡献榜完整 key
    private long primaryRankId = 0;

    @ComponentAttrField(labelText = "主榜的名次", remark = "大于0 时,表示主榜的名次位置,默认值1")
    // 当 primaryRankId>0 时， 表示主榜的名次位置
    private int primaryRankPos = 1;

    @ComponentAttrField(labelText = "主榜榜单类型", remark = "用于指示主榜要查询的榜单类型", dropDownSourceBeanClass = RankTypeSource.class)
    // 当 primaryRankId>0 时， 用于指示主榜要查询的榜单类型
    private String primaryRankType = "1";

    @ComponentAttrField(labelText = "是否是公会", remark = "是否是公会榜1->是 0->否,默认0", dropDownSourceBeanClass = YesNoSource.class)
    //是否是公会ID, 1-> 是  如果是公会则发给公会的OW
    private int isChannel = 0;

    public int getIsChannel() {
        return isChannel;
    }

    public void setIsChannel(int isChannel) {
        this.isChannel = isChannel;
    }

    /*
            名次奖励配置

            第一层key：位置说明，第一层value：指定位置发放的奖励， 第二层key：taskId，第三层key：奖包ID， 第三层值：奖包ID发放的数量
            第一层key内容组合示例：1,3,8,9-20,31 ，每个值用 , 分开，连续值用 - 指示，同一个位置不能出现多次奖励，否则报错
         */
    @ComponentAttrField(
            labelText = "名次奖励配置"
            , remark = "第一层key:位置说明;第二层key:奖池id;第三层key:奖包ID;第三层值:奖包ID发放的数量,第一层key内容组合示例：1,3,8,9-20,31;每个值用 , 分开,连续值用 - 指示,同一个位置不能出现多次奖励,否则报错"
            , subFields = {
            @SubField(fieldName = "key1", type = String.class, labelText = "榜单名次")
            , @SubField(fieldName = "key2", type = Long.class, labelText = "奖池id")
            , @SubField(fieldName = "key3", type = Long.class, labelText = "奖包id")
            , @SubField(fieldName = "value", type = Integer.class, labelText = "发放奖包数量")}
    )
    Map<String, Map<Long, Map<Long, Integer>>> rankAwardConfig = Maps.newHashMap();

    /*
        名次插入语句配置

        key：要执行的sql，只支持 insert 语句， 上面有占位符，
        示例: INSERT INTO `ge_award_record`(`seq`,`act_id`,`rank_id`,`user_uid`,`receive_uid`,`score`,`rank`,`c_time`,`u_time`) VALUES ('##seq##', ##actId##, ##rankId##, '##member[1]##', '##member[2]##', ##score##, ##rank##, now(),now());

        value：位置(名次)说明, 每个值用 , 分开，连续值用 - 指示，同一个位置不能出现多次奖励，否则报错
        示例：1,3,8,9-20,31
    */
    @ComponentAttrField(
            labelText = "名次插入语句",
            remark = "key:要执行的sql,只支持 insert 语句,上面有占位符,示例: INSERT INTO `ge_award_record`(`seq`,`act_id`,`rank_id`,`user_uid`,`receive_uid`,`score`,`rank`,`c_time`,`u_time`) VALUES ('##seq##', ##actId##, ##rankId##, '##member[1]##', '##member[2]##', ##score##, ##rank##, now(),now()); value：位置(名次)说明, 每个值用 , 分开，连续值用 - 指示，同一个位置不能出现多次奖励，否则报错;示例：1,3,8,9-20,31"
            , subFields = {
            @SubField(fieldName = "key1", type = String.class, labelText = "插入sql")
            , @SubField(fieldName = "value", type = String.class, labelText = "榜单名次")
    }
    )
    Map<String, String> rankMysqlConfig = Maps.newHashMap();

    public int getRetry() {
        return retry;
    }

    public void setRetry(int retry) {
        this.retry = retry;
    }

    public String getRankType() {
        return rankType;
    }

    public void setRankType(String rankType) {
        this.rankType = rankType;
    }

    public int getReceiverInx() {
        return receiverInx;
    }

    public void setReceiverInx(int receiverInx) {
        this.receiverInx = receiverInx;
    }

    public long getRankId() {
        return rankId;
    }

    public void setRankId(long rankId) {
        this.rankId = rankId;
    }

    public long getPrimaryRankId() {
        return primaryRankId;
    }

    public void setPrimaryRankId(long primaryRankId) {
        this.primaryRankId = primaryRankId;
    }

    public int getPrimaryRankPos() {
        return primaryRankPos;
    }

    public void setPrimaryRankPos(int primaryRankPos) {
        this.primaryRankPos = primaryRankPos;
    }

    public String getPrimaryRankType() {
        return primaryRankType;
    }

    public void setPrimaryRankType(String primaryRankType) {
        this.primaryRankType = primaryRankType;
    }

    public Map<String, Map<Long, Map<Long, Integer>>> getRankAwardConfig() {
        return rankAwardConfig;
    }

    public void setRankAwardConfig(Map<String, Map<Long, Map<Long, Integer>>> rankAwardConfig) {
        this.rankAwardConfig = rankAwardConfig;
    }

    public Map<String, String> getRankMysqlConfig() {
        return rankMysqlConfig;
    }

    public void setRankMysqlConfig(Map<String, String> rankMysqlConfig) {
        this.rankMysqlConfig = rankMysqlConfig;
    }

    public boolean isMyDuty(long rankId) {
        return rankId < 1 ? false : this.rankId == rankId;
    }
}
