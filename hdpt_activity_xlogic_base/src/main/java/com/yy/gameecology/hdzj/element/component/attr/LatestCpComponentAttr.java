package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;

@Getter
@Setter
public class LatestCpComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "榜单ID", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class), remark = "多个使用英文逗号隔开")
    protected Set<Long> rankIds;

    @ComponentAttrField(labelText = "阶段ID", remark = "指定阶段，-1 -> 不指定阶段")
    protected long phaseId;

    @ComponentAttrField(labelText = "忽略时间", remark = "忽略按时间分榜的榜单，记录的数据不按时间做隔离")
    protected boolean ignoreDateCode;

    @ComponentAttrField(labelText = "子频道角色ID", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class), remark = "多个使用英文逗号隔开")
    protected Set<Long> hallActorIds;
}
