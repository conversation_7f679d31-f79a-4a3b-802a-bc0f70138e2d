package com.yy.gameecology.hdzj.element.component.attr;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.boot.starter.util.JsonUtils;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.hdzt.ActShopExchangeResult;
import com.yy.gameecology.activity.client.thrift.UserinfoThriftClient;
import com.yy.gameecology.activity.client.thrift.ZhuiWanPrizeIssueServiceClient;
import com.yy.gameecology.activity.client.yrpc.ZhuiyaShopServerClient;
import com.yy.gameecology.activity.client.yrpc.ZhuiyaShopServerYrpc;
import com.yy.gameecology.activity.exception.BusinessException;
import com.yy.gameecology.common.bean.ClientInfo;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.bean.ExchangeResult;
import com.yy.gameecology.hdzj.bean.ItemInfo;
import com.yy.gameecology.hdzj.bean.ShopInfo;
import com.yy.gameecology.hdzj.bean.ShopInfoResp;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.KaiHeiQcoinsComponent;
import com.yy.protocol.pb.shop.ZhuiyaShopServer;
import com.yy.thrift.hdztaward.AwardIssueRecordInfo;
import com.yy.thrift.hdztaward.AwardIssueRecordResult;
import com.yy.thrift.zhuiwan.BaseRsp;
import com.yy.thrift.zhuiwan.UserBatchReleaseRsp;
import com.yy.thrift.zhuiwan.UserReleaseWithAccountInfo;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequestMapping("/5157/shop")
@RestController
public class AovInviteActShopComponent extends BaseActComponent<AovInviteActShopComponentAttr> {

    @Autowired
    private UserinfoThriftClient userinfoThriftClient;

    @Autowired
    private ZhuiyaShopServerClient zhuiyaShopServerClient;

    @Autowired
    private ZhuiWanPrizeIssueServiceClient zhuiWanPrizeIssueServiceClient;


    @Override
    public Long getComponentId() {
        return ComponentId.AOV_INVITE_ACT_SHOP;
    }

    public static final int QCOIN_PRESENT_TYPE = 17;

    public static final int SAIBAO_PRESENT_TYPE = 135;

    /**
     * 商城信息
     *
     * 0-库存充足(d) 1-库存不足(f) 2-用户已兑换(c)
     * 3-用户已兑换未填写信息(a) 4-用户已兑换未发放成功(b)
     *
     * @param actId 活动id, nginx rewrite
     */
    @RequestMapping("/shopInfo")
    public Response<ShopInfoResp> shopInfo(Long actId, Long cmptIndex){
        long uid = getLoginYYUid();
        if(uid <=0L){
            return Response.fail(400, "未登录");
        }
        final AovInviteActShopComponentAttr attr;
        if (cmptIndex == null || cmptIndex < 0) {
            attr = tryGetUniqueComponentAttr(actId);
        } else {
            attr = getComponentAttr(actId, cmptIndex);
        }

        var req = ZhuiyaShopServer.ListShopItemReq.newBuilder()
                .setShop(ZhuiyaShopServer.Shop.HDPT_ACTIVITY)
                .setUid(uid)
                .setShopId(actId.toString())
                .addAllItemId(Collections.emptyList())
                .build();
        var resp = zhuiyaShopServerClient.listShopItem(req);

        if (resp.getCode() != 0) {
            return Response.fail(resp.getCode(), resp.getMessage());
        }

        Map<String, ActShopExchangeResult> map = attr.getShopExchangeResult().stream()
                .collect(Collectors.toMap(ActShopExchangeResult::getItemId, Function.identity()));

        List<ItemInfo> list = Lists.newArrayList();
        List<Long> packageIds = Lists.newArrayList();
        Map<String, Long> packageItemMap = new HashMap<>();
        Map<String, ItemInfo> itemInfoMap = new HashMap<>();
        for (ZhuiyaShopServer.ShopItemInfo shopItem : resp.getShopItemInfoList()) {
            ItemInfo item = new ItemInfo();
            item.setItemId(shopItem.getItemId());
            item.setItemName(shopItem.getItemName());
            item.setItemImage(shopItem.getItemImage());
            item.setPrice(shopItem.getItemPrice().getPrice());
            ActShopExchangeResult shopExchangeResult = map.get(shopItem.getItemId());
            if (shopExchangeResult != null) {
                item.setMoney(shopExchangeResult.getMoney());
                item.setNum(shopExchangeResult.getNum());
                item.setUnit(shopExchangeResult.getUnit());
                String areaSort =shopExchangeResult.getAreaSort();
                item.setRewardType(shopExchangeResult.getRewardType());
                if(StringUtil.isNotBlank(areaSort)){
                    item.setAreaSort(JSON.parseObject(areaSort,new TypeReference<HashMap<String,Long>>(){}));
                }
                item.setTaskId(shopExchangeResult.getTaskId());
                item.setPackageId(shopExchangeResult.getPackageId());
            }

            long remain = shopItem.getItemCostList().stream()
                    .mapToLong(ZhuiyaShopServer.ItemCost::getRemain)
                    .filter(r -> r >= 0)
                    .min().orElse(-1);
            item.setRemain(remain);
            if (remain == 0) {
                item.setStatus(1);
            }

            //库存充足检查是否是心愿皮肤 同时检查是否在时间范围内
            if(attr.getItemId() == Convert.toLong(item.getItemId())){
                Date now = commonService.getNow(actId);
                boolean inSellTime = inSellTime(attr, now.getTime());
                if(!inSellTime) {
                    item.setMsg(attr.getUnOpenToast());
                } else {
                    if (item.getRemain() == 0) {
                        item.setMsg(attr.getNoEnoughToast());
                    }
                }
                item.setStatus(inSellTime ? 0 : 1);
            }

            //已经兑换了查下追玩状态
            long userRemain = shopItem.getItemCostList().stream()
                    .mapToLong(ZhuiyaShopServer.ItemCost::getUserRemain)
                    .filter(r -> r >= 0)
                    .min().orElse(-1);
            if (userRemain == 0) {
                item.setStatus(2);
                if(!attr.getNoNeedFillAccountItems().contains(Convert.toInt(item.getItemId()))) {
                    packageIds.add(item.getPackageId());
                    packageItemMap.put(item.getItemId(), item.getPackageId());
                }
            }
            itemInfoMap.put(item.getItemId(), item);
            list.add(item);
        }

        if(!packageIds.isEmpty()) {
            List<UserReleaseInfo> userReleaseInfos = getTaskAwardState(actId, uid, (int)attr.getTaskId(), packageIds);
            Map<Long, UserReleaseInfo> packageIdUserReleaseInfoMap = Maps.newHashMap();

            if(userReleaseInfos != null && !userReleaseInfos.isEmpty()) {
                for (UserReleaseInfo userReleaseInfo : userReleaseInfos) {
                    packageIdUserReleaseInfoMap.put(userReleaseInfo.getPackageId(), userReleaseInfo);
                }
            }
            for (String itemIdStr : packageItemMap.keySet()) {
                long packageId = packageItemMap.get(itemIdStr);
                ItemInfo itemInfo = itemInfoMap.get(itemIdStr);
                UserReleaseInfo userReleaseInfo = packageIdUserReleaseInfoMap.get(packageId);
                if(userReleaseInfo != null) {
                    if(itemInfo.getRewardType() == SAIBAO_PRESENT_TYPE){
                        Map<String, String> extMap = new HashMap<>(){{put("url", userReleaseInfo.getExchangeUrl());}};
                        itemInfo.setExtJson(JsonUtil.toJson(extMap));
                    }
                    if (userReleaseInfo.getStatus() == UserReleaseInfoState.SUCCEED) {
                        itemInfo.setStatus(2);
                    } else if (userReleaseInfo.getStatus() == UserReleaseInfoState.PENDING) {
                        itemInfo.setStatus(4);
                    } else if(userReleaseInfo.getStatus() == UserReleaseInfoState.FAILED) {
                        itemInfo.setStatus(3);
                    }
                }
            }
        }

        ShopInfoResp vo = new ShopInfoResp();
        Map<String, List<ItemInfo>> areaItemInfoList = sortGroupByArea(attr, list);
        var shopInfo = new ShopInfo(actId.toString(), list, areaItemInfoList);
        vo.setShopInfo(shopInfo);
        return Response.success(vo);
    }

    private Map<String, List<ItemInfo>> sortGroupByArea(AovInviteActShopComponentAttr attr, List<ItemInfo> items) {
        Map<String, List<ItemInfo>> listMap = Maps.newLinkedHashMap();
        //分组
        for (ItemInfo item : items) {
            Map<String, Long> areaSort = item.getAreaSort();
            for (String area : areaSort.keySet()) {
                List<ItemInfo> areaItems = listMap.getOrDefault(area, Lists.newArrayList());
                areaItems.add(item);
                listMap.put(area, areaItems);
            }
        }
        //排序
        for (String area : listMap.keySet()) {
            List<ItemInfo> itemList = listMap.get(area);
            itemList.sort(Comparator.comparingLong(o -> o.getAreaSort().getOrDefault(area, 0L)));
        }

        //分区排序
        Map<String, List<ItemInfo>> result = Maps.newLinkedHashMap();
        for (String area : attr.getAreaInfo()) {
            if(listMap.containsKey(area)){
                result.put(area,listMap.get(area));
            }
        }

        return result;
    }

    /**
     * 荣耀积分 gameNick=gameServerName=gameAreaName=
     * q币 gameAccount=11&buyerMobile=***********
     */
    @RequestMapping("/fillInAccount")
    public Response fillInAccount(Long actId, Long cmptIndex, String itemId,
                                  String gameAccount,
                                  String buyerMobile,
                                  String gameServerName,
                                  String gameNick,
                                  String gameAreaName) {
        long uid = getLoginYYUid();
        if(uid <=0L){
            return Response.fail(400, "未登录");
        }
        final AovInviteActShopComponentAttr attr;
        if (cmptIndex == null || cmptIndex < 0) {
            attr = tryGetUniqueComponentAttr(actId);
        } else {
            attr = getComponentAttr(actId, cmptIndex);
        }
        String rsp = checkFillInAccountParam(attr, itemId, gameAccount, buyerMobile, gameServerName, gameNick, gameAreaName);
        if (rsp != null) {
            return Response.fail(400, rsp);
        }
        ActShopExchangeResult exchangeResult = attr.getShopExchangeResult().stream()
                .filter(result -> result.getItemId().equals(itemId))
                .findFirst()
                .orElse(null);
        try {
            AwardIssueRecordResult issueResult = hdztAwardServiceClient.queryUserAwardIssues(attr.getBusiId(),
                    uid, (int)exchangeResult.getTaskId(), Lists.newArrayList(exchangeResult.getPackageId()));
            if (issueResult == null) {
                return Response.fail(1, "网络异常");
            }
            if (issueResult.getRetCode() != 0) {
                return Response.fail(1, "查询获奖记录失败");
            }

            List<AwardIssueRecordInfo> issueRecords = issueResult.getAwardList();
            if (CollectionUtils.isEmpty(issueRecords)) {
                return Response.fail(1, "没有获奖记录需要填写账号信息");
            }
            BaseRsp baseRsp = zhuiWanPrizeIssueServiceClient.fillInUserAccountInfoV2(uid,
                    issueResult.getAwardList().get(0).getIssueSeq(), gameAccount, "",
                    buyerMobile, gameNick, gameServerName, gameAreaName, 0);
            if(baseRsp == null) {
                return Response.fail(1, "网络异常");
            }
            return Response.ok("填写成功");
        }  catch (BusinessException e) {
            log.error("updateAccountInfo e:{}", e.getMessage(), e);
            return Response.fail(1, e.getMessage());
        } catch (Exception e) {
            log.error("updateAccountInfo e:{}", e.getMessage(), e);
            return Response.fail(1, "网络异常");
        }
    }

    public String checkFillInAccountParam(AovInviteActShopComponentAttr attr,
                                          String itemId,
                                          String gameAccount,
                                          String buyerMobile,
                                          String gameServerName,
                                          String gameNick,
                                          String gameAreaName) {
        ActShopExchangeResult exchangeResult = attr.getShopExchangeResult().stream()
                .filter(result -> result.getItemId().equals(itemId))
                .findFirst()
                .orElse(null);
        if (exchangeResult == null) {
            return "商品不存在";
        }
        if (exchangeResult.getRewardType() == QCOIN_PRESENT_TYPE) {
            if (StringUtil.isBlank(gameAccount) || StringUtil.isBlank(buyerMobile)) {
                return "q币兑换需要填写游戏账号和手机号";
            }
        } else if (exchangeResult.getRewardType() == SAIBAO_PRESENT_TYPE) {
            if (StringUtil.isBlank(gameServerName) || gameNick == null || StringUtil.isBlank(gameAreaName)) {
                return "荣耀积分兑换需要填写游戏区服和角色昵称";
            }
        }
        return null;
    }

    /**
     * 查询任务奖励发状态
     */
    public List<UserReleaseInfo> getTaskAwardState(long actId, long uid, int awardTaskId, List<Long> awardPackageId) {
        AovInviteActShopComponentAttr attr = tryGetUniqueComponentAttr(actId);
        if (CollectionUtils.isEmpty(awardPackageId)) {
            throw new BusinessException(401, "奖包参数不能为空");
        }
        AwardIssueRecordResult issueResult = hdztAwardServiceClient.queryUserAwardIssues(810, uid, awardTaskId, awardPackageId);
        if (issueResult == null) {
            throw new BusinessException(500, "网络异常");
        }

        if (issueResult.getRetCode() != 0) {
            throw new BusinessException(500, "查询发奖记录失败");
        }

        List<AwardIssueRecordInfo> issueRecords = issueResult.getAwardList();
        if (CollectionUtils.isEmpty(issueRecords)) {
            log.warn("getTaskAwardState issueRecords is empty,actId:{},uid:{},awardTaskId:{},awardPackageId:{}", actId, uid, awardTaskId, JsonUtil.toJson(awardPackageId));
            return Collections.EMPTY_LIST;
        }
        Map<String, Long> seqItemIdMap = new HashMap<>();
        for (AwardIssueRecordInfo issueRecord : issueRecords) {
            seqItemIdMap.put(issueRecord.getIssueSeq(), issueRecord.getPackageId());
        }

        List<String> orderSeqs = issueRecords.stream().map(v -> v.getIssueSeq()).collect(Collectors.toList());
        UserBatchReleaseRsp releaseResult = zhuiWanPrizeIssueServiceClient.batchGetUserReleaseInfo(uid, orderSeqs);
        if (releaseResult == null) {
            throw new BusinessException(500, "网络异常");
        }
        if (releaseResult.getCode() != 0) {
            throw new BusinessException(500, "查询发放记录失败");
        }

        List<UserReleaseInfo> userReleaseList = getTaskAwardReleaseState(releaseResult.getReleaseInfoList(), seqItemIdMap);
        return userReleaseList;
    }

    private List<UserReleaseInfo> getTaskAwardReleaseState(List<UserReleaseWithAccountInfo> releaseInfoList,
                                                           Map<String, Long> seqItemIdMap) {

        List<UserReleaseInfo> userReleaseList = Collections.EMPTY_LIST;
        if (CollectionUtils.isNotEmpty(releaseInfoList)) {
            userReleaseList = releaseInfoList.stream().map(v -> {
                UserReleaseInfo item = new UserReleaseInfo();
                item.setStatus(v.getStatus());
                UserAccountInfo accountInfo = new UserAccountInfo();
                if (v.getAccountInfo() != null) {
                    accountInfo.setGameAccount(v.getAccountInfo().getGameAccount());
                    accountInfo.setBuyerMobile(v.getAccountInfo().getBuyerMobile());
                    accountInfo.setGameServerName(v.getAccountInfo().getGameServerName());
                    accountInfo.setGameAreaName(v.getAccountInfo().getGameSubAccount());
                    accountInfo.setGameNick(v.getAccountInfo().getGameNick());
                }
                item.setAccountInfo(accountInfo);
                item.setExchangeUrl(v.getExchangeUrl());
                String seq = v.getSeqId().replace("issue:", "");
                if(seqItemIdMap.containsKey(seq)) {
                    item.setSeq(seq);
                    item.setPackageId(seqItemIdMap.get(seq));
                };
                return item;
            }).collect(Collectors.toList());
        }

        return userReleaseList;
    }

    @RequestMapping("/exchange")
    public Response<ExchangeResult> exchange(Long actId, Long cmptIndex, String itemId, HttpServletRequest request){
        long uid = getLoginYYUid();
        if(uid <=0L){
            return Response.fail(400, "未登录");
        }
        int status = actInfoService.actTimeStatus(actId);
        if (status > 0) {
            return Response.fail(3, "活动已结束!");
        }
        if (status < 0) {
            return Response.fail(3, "活动未开始!");
        }
        //是否实名
        String idHash = userinfoThriftClient.getIdHash(uid);
        if(StringUtil.isEmpty(idHash)) {
            return Response.fail(471, "您还没有完成实名认证，请先完成实名认证");
        }

        final AovInviteActShopComponentAttr attr;
        if (cmptIndex == null || cmptIndex < 0) {
            attr = tryGetUniqueComponentAttr(actId);
        } else {
            attr = getComponentAttr(actId, cmptIndex);
        }

        String key = makeKey(attr, "exchange_" + uid);
        boolean rs = actRedisDao.setNX(getRedisGroupCode(actId), key, System.currentTimeMillis() + "", 1);
        if (!rs) {
            return Response.fail(1, "操作太频繁，请稍后重试");
        }

        List<String> itemIds = Lists.newArrayList(itemId);
        var req = ZhuiyaShopServer.ListShopItemReq.newBuilder()
                .setShop(ZhuiyaShopServer.Shop.HDPT_ACTIVITY)
                .setUid(uid)
                .setShopId(actId.toString())
                .addAllItemId(itemIds)
                .build();
        var resp = zhuiyaShopServerClient.listShopItem(req);

        if (resp.getCode() != 0) {
            return Response.fail(resp.getCode(), resp.getMessage());
        }

        for (ZhuiyaShopServer.ShopItemInfo shopItem : resp.getShopItemInfoList()) {
            long remain = shopItem.getItemCostList().stream()
                    .mapToLong(ZhuiyaShopServer.ItemCost::getRemain)
                    .filter(r -> r >= 0)
                    .min().orElse(-1);
            if(attr.getItemId() == Convert.toLong(shopItem.getItemId())) {
                if(!inSellTime(attr, commonService.getNow(actId).getTime())) {
                    return Response.fail(1, attr.getUnOpenToast());
                }
                if (remain == 0 || !inSellTime(attr, commonService.getNow(actId).getTime())) {
                    return Response.fail(1, attr.getNoEnoughToast());
                }
            }
            if (remain == 0) {
                return Response.fail(1, "库存不足");
            }
        }

        ClientInfo clientInfo = WebUtil.getClientInfo(request);
        var buyClientInfo = ZhuiyaShopServer.ClientInfo.newBuilder()
                .setApp(ZhuiyaShopServerYrpc.getApp(clientInfo.app()))
                .setDeviceId(Convert.toString(clientInfo.hdid()))
                .setIp(Convert.toString(clientInfo.ip()))
                .setPlatform(ZhuiyaShopServerYrpc.getPlatform(clientInfo.platform()))
                .build();

        var buyReq = ZhuiyaShopServer.BuyShopItemReq.newBuilder()
                .setShop(ZhuiyaShopServer.Shop.HDPT_ACTIVITY)
                .setUid(uid)
                .setShopId(actId.toString())
                .setItemId(itemId)
                .setCount(1)
                .setClientInfo(buyClientInfo)
                .build();
        var buyResp = zhuiyaShopServerClient.buyShopItem(buyReq);
        log.info("exchange uid:{} actId:{} itemId:{} resp:{}", uid, actId, itemId, JsonUtils.serialize(resp));

        if (buyResp.getCode() == ZhuiyaShopServer.BuyCode.SUCCESS) {
            var result = new ExchangeResult();
            result.setResult(String.format("成功获得【%s】奖励", buyResp.getItemName()));
            result.setTip("已发送给您");

            // 活动自定义文案
            if (!attr.getShopExchangeResult().isEmpty()) {
                Optional<ActShopExchangeResult> shopExchangeResult = attr.getShopExchangeResult().
                        stream().filter(v -> v.getItemId().equals(itemId)).findFirst();
                if (shopExchangeResult.isPresent()) {
                    result.setResult(shopExchangeResult.get().getResult());
                    result.setTip(shopExchangeResult.get().getTip());
                }
            }
            return Response.success(result);
        }

        final int codeValue = buyResp.getCodeValue();
        // 根据不同code配置不同返回语
        if (buyResp.getCode() == ZhuiyaShopServer.BuyCode.ACCOUNT_NOT_ENOUGH) {
            return Response.fail(codeValue, "金币数量不足");
        }
        if (buyResp.getCode() == ZhuiyaShopServer.BuyCode.DAY_LIMIT
                || buyResp.getCode() == ZhuiyaShopServer.BuyCode.TOTAL_LIMIT
                || buyResp.getCode() == ZhuiyaShopServer.BuyCode.USER_DAY_LIMIT
                || buyResp.getCode() == ZhuiyaShopServer.BuyCode.USER_TOTAL_LIMIT) {
            return Response.fail(codeValue, "当前兑换人数过多，请稍后再试");
        }
        if (buyResp.getCode() == ZhuiyaShopServer.BuyCode.ACCOUNT_PAY_FAIL) {
            return Response.fail(codeValue, "扣费失败，请稍后再试");
        }

        return Response.fail(codeValue, "兑换失败：" + buyResp.getMessage());
    }

    public boolean inSellTime(AovInviteActShopComponentAttr attr, long now) {
        for (String time : attr.getOpenTime()) {
            String[] times = time.split("&");
            Date startTime = DateUtil.getDate(times[0]);
            Date endTime = DateUtil.getDate(times[1]);
            if(now >= startTime.getTime() && now <= endTime.getTime()) {
                return true;
            }
        }
        return false;
    }

    @Data
    public static class UserReleaseInfo {
        /**
         * 追玩订单状态,1=有效，4=发放中，6=等待完善信息
         * Q币这里没有2和5，追玩转化成了6
         */
        private int status;
        private UserAccountInfo accountInfo;
        private String exchangeUrl;
        private String seq;
        private long packageId;
    }

    interface UserReleaseInfoState {
        /**
         * 发放成功
         */
        int SUCCEED = 1;
        /**
         * 发放中
         */
        int PENDING = 4;
        /*
         *未填写或发放失败
         */
        int FAILED = 6;
    }

    @Data
    public static class UserAccountInfo {
        private String gameAccount;//充值账号或游戏账号
        private String buyerMobile;//手机号
        /**
         * 区服
         */
        private String gameServerName;

        private String gameAreaName;

        private String gameNick;
    }


/*    【商品兑换情况】
    商品AAA 限量XXX 已兑换XXXX 剩余XXXX
    商品AAA 限量XXX 已兑换XXXX 剩余XXXX
    商品AAA 限量XXX 已兑换XXXX 剩余XXXX*/

}
