package com.yy.gameecology.hdzj.element.component;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableSortedMap;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.AccumulatedRecord;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.SendGiftEvent;
import com.yy.gameecology.activity.exception.BadRequestException;
import com.yy.gameecology.activity.exception.ParameterException;
import com.yy.gameecology.activity.service.BroadCastHelpService;
import com.yy.gameecology.activity.service.UserAccumulatedService;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.BoxLotteryComponentAttr;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.Rank;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @Author: CXZ
 * @Desciption: 业务送礼宝箱
 * @Date: 2021/4/15 16:39
 * @Modified:
 */
@Component
public class SendGiftLotteryComponent extends BaseActComponent<BoxLotteryComponentAttr> {

    /**
     * 连送累计的key
     */
    private static final String GIFT_COMBO_KEY = "gift_combo";
    /**
     * 保存用户送礼触发抽奖的顺序
     */
    private static final String USER_SEND_GIFT_RANK = "gift_combo_user_send_gift_rank";

    private static final int DAILY_GIFT_ORDER = 1;

    private static final int ACT_GIFT_ORDER = 2;

    private static final int RANK_ORDER = 3;


    @Autowired
    private BroadCastHelpService broadCastHelpService;

    @Autowired
    private UserAccumulatedService userAccumulatedService;


    @Override
    public Long getComponentId() {
        return ComponentId.SEND_GIFT_LOTTERY;
    }

    @HdzjEventHandler(value = SendGiftEvent.class, canRetry = false)
    public void sendGiftLottery(SendGiftEvent event, BoxLotteryComponentAttr attr) {
        long actId = attr.getActId();
        if (!actInfoService.inActTime(actId)) {
            //不在时间内
            log.info("sendGiftLottery info not in activity time event = {}", JSON.toJSONString(event));
            return;
        }
        Long userUid = event.getSendUid();
        Long anchorUid = event.getRecvUid();
        String giftId = event.getGiftId();
        Long giftNum = event.getGiftNum();
        String seq = event.getSeq();
        BusiId busiId = broadCastHelpService.changeBroTemplate2BusiId(event.getTemplate());
        if (attr.getBussId() != busiId.getValue() || !attr.getGiftValueMap().containsKey(event.getGiftId())) {
            return;
        }
        Integer giftValue = attr.getGiftValueMap().get(event.getGiftId());
        Map.Entry<Long, Integer> boxType = ImmutableSortedMap.copyOf(attr.getValueBoxTypeMap()).floorEntry(giftValue * giftNum);
        if (boxType == null) {
            log.info("sendGiftLottery ignore@giftId:{} giftNum:{} seq:{},attr:{}", event.getGiftId(), giftNum, seq, JSON.toJSONString(attr));
            return;
        }
        int count = 1;
        //按真实的时间判断用户送礼排名
        long startTime = System.currentTimeMillis();
        //连抽-本次送礼会等下到连抽时间结束后再执行，本次执行的送礼事件是上次的
        if (attr.getContinuousLotteryTime() > 0) {
            //连抽数据保存key
            String key = makeKey(attr, GIFT_COMBO_KEY);
            //判断是否是同一个行为的依据
            String judge = anchorUid + "_" + giftId + "_" + giftNum;
            AccumulatedRecord accumulatedRecord = userAccumulatedService.accumulatedUserData(attr.getActId(), key, userUid, JSON.toJSONString(event), judge, attr.getContinuousLotteryTime(), seq);
            if (accumulatedRecord == null) {
                //等待连抽
                log.info("sendGiftLottery ignore@wait last giftId:{} giftNum:{} seq:{},attr:{}", event.getGiftId(), giftNum, seq, JSON.toJSONString(attr));
                return;
            }
            event = JSON.parseObject(accumulatedRecord.getData(), SendGiftEvent.class);
            count = accumulatedRecord.getCount();
            startTime = accumulatedRecord.getStartTime();
        }

        lottery(attr, event, count, startTime);
    }

    private void lottery(BoxLotteryComponentAttr attr, SendGiftEvent event, int count, long time) {
        Long userUid = event.getSendUid();
        try {
            BusiId busiId = broadCastHelpService.changeBroTemplate2BusiId(event.getTemplate());

            Integer giftValue = attr.getGiftValueMap().get(event.getGiftId());
            long giftTotalValue = giftValue * event.getGiftNum();
            Map.Entry<Long, Integer> boxType = ImmutableSortedMap.copyOf(attr.getValueBoxTypeMap()).floorEntry(giftTotalValue);
            long taskId = attr.getBoxTypeTaskIdMap().get(boxType.getValue());
            Assert.isTrue(taskId > 0, "config error,cat find taskId,boxType=" + boxType);
            long lotteryCount = (giftTotalValue / boxType.getKey()) * count;
            //获取必中
            long bingo = 0;
            if (ArrayUtils.contains(attr.getMustWinBoxType(), boxType.getValue())) {
                log.info("try get bingo");
                bingo = getMustWinPackageId(attr, userUid, time);
            }
            //调抽奖接口
            String seq = UUID.randomUUID().toString();
            hdztAwardServiceClient.doLottery(DateUtil.getNowYyyyMMddHHmmss(), busiId.getValue(), userUid, taskId, (int) lotteryCount, bingo, seq);
            log.info("lottery done@ uid:{} giftId:{} giftNum:{} lotteryCount:{} taskId:{} bingo:{} seq:{},attr:{}",
                    userUid, event.getGiftId(), count, lotteryCount, taskId, bingo, seq, JSON.toJSONString(attr));
        } catch (Exception e) {
            log.error("lottery error@uid:{} attr:{},event:{},count:{} {}", userUid, JSON.toJSONString(attr), JSON.toJSONString(event), count, e.getMessage(), e);
        }
    }

    /**
     * 必中逻辑
     *
     * @param attr
     * @param uid
     * @param time
     * @return
     */
    private long getMustWinPackageId(BoxLotteryComponentAttr attr, long uid, Long time) {

        long actId = attr.getActId();
        int mustWin = attr.getMustWin();
        //按送礼顺序必中
        if (mustWin == DAILY_GIFT_ORDER || mustWin == ACT_GIFT_ORDER) {
            //榜单排名限制
            if (attr.getLimitRank() > 0) {
                Assert.isTrue(attr.getMustWinRankId() > 0, "mustWinRankId error");
                Rank rank = hdztRankingThriftClient.queryPointedMembersRanking(actId, attr.getMustWinRankId(), attr.getMustWinPhaseId(), "", uid + "", Maps.newHashMap());
                //排名之外
                if (rank.getRank() < 1 || rank.getRank() > attr.getLimitRank()) {
                    return 0L;
                }
            }
            //获取用户送礼排名
            String day = DateUtil.getPattenStrFromTime(time, DateUtil.PATTERN_TYPE2);
            //1是日排名
            String userSendGiftRankKeyName = mustWin == DAILY_GIFT_ORDER ? USER_SEND_GIFT_RANK + "_" + day : USER_SEND_GIFT_RANK;
            String userSendGiftRankKey = makeKey(attr, userSendGiftRankKeyName);
            DefaultRedisScript<Long> script = new DefaultRedisScript<>();
            script.setScriptText("if tonumber(redis.call('sadd', KEYS[1], ARGV[1])) == 1 then\n return redis.call('scard', KEYS[1])\n end");
            script.setResultType(Long.class);
            String groupCode = redisConfigManager.getGroupCode(actId);
            Long card = actRedisDao.getRedisTemplate(groupCode).execute(script, Arrays.asList(userSendGiftRankKey), String.valueOf(uid));
            int rank = Optional.ofNullable(card).map(Long::intValue).orElse(-1);


            return attr.getRankMustWinMap().getOrDefault(rank, 0L);

        } else if (mustWin == RANK_ORDER) {
            Assert.isTrue(attr.getMustWinRankId() > 0, "mustWinRankId error");
            int rank = queryUserRank(attr, uid);
            return attr.getRankMustWinMap().getOrDefault(rank, 0L);
        }
        return 0L;
    }

    /**
     * 设置送礼顺序排名，预设前 mustCount-1个空位 ，测试接口，不允许生产访问
     *
     * @param actId
     * @param index
     * @param mustCount
     * @param day
     * @return
     */
    public Response setSendGiftRank(long actId, long index, int mustCount, String day) {
        if (!isMyDuty(actId)) {
            throw new BadRequestException("无效活动");
        }
        BoxLotteryComponentAttr attr = getComponentAttr(actId, index);
        if (attr == null) {
            throw new ParameterException("index error");
        }
        if (SysEvHelper.isDeploy() && !commonService.isGrey(actId)) {
            throw new BadRequestException("活动状态异常");
        }
        int mustWin = attr.getMustWin();
        if (mustWin != DAILY_GIFT_ORDER && mustWin != ACT_GIFT_ORDER) {
            throw new ParameterException("非送礼顺序必中");
        }
        String cmptTitle = getHdzjComponent(actId, index).getCmptTitle();
        if (StringUtils.isBlank(day) && mustWin == DAILY_GIFT_ORDER) {
            day = DateUtil.getPattenStrFromTime(System.currentTimeMillis(), DateUtil.PATTERN_TYPE2);
        }
        String userSendGiftRankKeyName = mustWin == DAILY_GIFT_ORDER ? USER_SEND_GIFT_RANK + "_" + day : USER_SEND_GIFT_RANK;
        String userSendGiftRankKey = makeKey(attr, userSendGiftRankKeyName);
        String groupCode = redisConfigManager.getGroupCode(actId);
        actRedisDao.del(groupCode, userSendGiftRankKey);
        if (mustCount > 1) {
            String[] members = IntStream.range(1, mustCount).mapToObj(String::valueOf).toArray(String[]::new);
            actRedisDao.getRedisTemplate(groupCode).opsForSet().add(userSendGiftRankKey, members);
        }
        Set<String> memberSet = actRedisDao.sMembers(groupCode, userSendGiftRankKey);
        return Response.success(ImmutableSortedMap.of("title", cmptTitle, "members", memberSet));
    }

    /**
     * @param actId
     * @param index
     * @param day
     * @return
     */
    public Response getSendGiftRank(long actId, long index, String day) {
        if (!isMyDuty(actId)) {
            throw new BadRequestException("无效活动");
        }
        BoxLotteryComponentAttr attr = getComponentAttr(actId, index);
        if (attr == null) {
            throw new ParameterException("index error");
        }
        if (SysEvHelper.isDeploy() && !commonService.isGrey(actId)) {
            throw new BadRequestException("活动状态异常");
        }
        int mustWin = attr.getMustWin();
        if (mustWin != DAILY_GIFT_ORDER && mustWin != ACT_GIFT_ORDER) {
            throw new ParameterException("非送礼顺序必中");
        }
        String cmptTitle = getHdzjComponent(actId, index).getCmptTitle();
        if (StringUtils.isBlank(day) && mustWin == DAILY_GIFT_ORDER) {
            day = DateUtil.getPattenStrFromTime(System.currentTimeMillis(), DateUtil.PATTERN_TYPE2);
        }
        String userSendGiftRankKeyName = mustWin == DAILY_GIFT_ORDER ? USER_SEND_GIFT_RANK + "_" + day : USER_SEND_GIFT_RANK;
        String userSendGiftRankKey = makeKey(attr, userSendGiftRankKeyName);
        String groupCode = redisConfigManager.getGroupCode(actId);

        Set<String> memberSet = actRedisDao.sMembers(groupCode, userSendGiftRankKey);
        return Response.success(ImmutableSortedMap.of("title", cmptTitle, "members", memberSet));
    }

    private int queryUserRank(BoxLotteryComponentAttr attr, long uid) {
        long actId = attr.getActId();
        Rank rank = hdztRankingThriftClient.queryPointedMembersRanking(actId, attr.getMustWinRankId(), attr.getMustWinPhaseId(), "", uid + "", Maps.newHashMap());
        return rank.getRank();
    }

    /**
     * 执行连抽等待抽奖的是数据
     *
     * @param attr
     */
    private void finishGiftCombo(BoxLotteryComponentAttr attr) {
        String lockKey = makeKey(attr, "finishGiftCombo_lock");
        timerSupport.work(lockKey, 30, () -> {
            String key = makeKey(attr, GIFT_COMBO_KEY);

            List<AccumulatedRecord> accumulatedRecords = userAccumulatedService.getUseAccumulatedRecord(attr.getActId(), key);
            accumulatedRecords.stream().forEach(record -> {
                SendGiftEvent event = JSON.parseObject(record.getData(), SendGiftEvent.class);
                lottery(attr, event, record.getCount(), record.getStartTime());
            });
            log.info("finishGiftCombo done.attr:{},record size:{}", JSON.toJSONString(attr), accumulatedRecords.size());
        });
    }

    @Scheduled(cron = "*/1 * * * * ? ")
    @NeedRecycle(notRecycle = true, author = "chenxiazhuan")
    public void finishGiftCombo() {

        Set<Long> activityIds = getActivityIds();

        List<BoxLotteryComponentAttr> attrs = activityIds.stream().filter(actId -> actInfoService.inActTime(actId))
                .map(this::getAllComponentAttrs).flatMap(Collection::stream)
                .collect(Collectors.toList());
        attrs.stream().forEach(this::finishGiftCombo);
        log.info("finishGiftCombo Scheduled done@act size:{} valid attrs size:{}", activityIds.size(), attrs.size());

    }

}
