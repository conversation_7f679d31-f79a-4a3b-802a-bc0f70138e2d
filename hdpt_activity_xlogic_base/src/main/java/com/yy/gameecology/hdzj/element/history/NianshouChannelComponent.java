package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.bean.AnchorStartShowEvent;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.UserEnterTemplateEvent;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.client.thrift.CulClient;
import com.yy.gameecology.activity.client.thrift.UserinfoThriftClient;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.consts.PBCommonNoticeType;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.CommonAwardInfo;
import com.yy.gameecology.hdzj.bean.GiftPlaneBO;
import com.yy.gameecology.hdzj.bean.NianshouInfo;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.history.attr.NianshouComponentAttr;
import com.yy.gameecology.hdzj.utils.RandomUtil;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztaward.AwardModelInfo;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.yy.thrift.broadcast.Template.*;

/**
 * 年兽大作战 -
 * 注意：这里的实现假定奖励发放没有限额（若有限额会很复杂，需要和产品明确限额发放处理逻辑）
 *
 * <AUTHOR>
 * @date 2021/11/02 14:25
 */
@Deprecated
@Component
public class NianshouChannelComponent extends BaseActComponent<NianshouComponentAttr> {

    @Autowired
    private CulClient culClient;

    //频道年兽数量 ns_channel_count_map  {sid_ssid:count}  当0时删除
    public static final String NS_CHANNEL_COUNT_MAP = "ns_channel_count_map";

    // 存放年兽的 redis list key ns_ns_list:sid_ssid {list<string>}
    // 时间| id | totalVolume
    // timestamp|id
    public static final String NS_BOX_LIST = "ns_box_list:%s";

    //年兽血量 ns_blood_map:day {nsId:血量} hincrby
    public static final String NS_BLOOD_MAP = "ns_blood_map:%s";

    // 存放年兽详情的 redis key ns_ns_detail:day {nsId: info}
    public static final String NS_BOX_DETAIL = "ns_ns_detail:%s";

    // 年兽开启状态标记 redis hash key， 防止一个年兽重复开启
    public static final String NS_BOX_OPEN = "ns_ns_open";

    // 奖励发放 redis list key， 用于削峰、出错重试 等 用户的
    public static final String NS_PLAYER_AWARD_ISSUE = "ns_player_award_issue";

    // 奖励发放 redis list key， 用于削峰、出错重试 等 主播
    public static final String NS_ANCHOR_AWARD_ISSUE = "ns_anchor_award_issue";

    // 记录年兽参与抽奖的用户 redis string key，时间为score，用来标记参与顺序， 变化部分是 年兽ID
    public static final String NS_ATTEND = "ns_attend:%s";

    // 年兽报名倒计时 redis string key， 变化部分是 年兽ID 判断是否可点击的key 只有在可打年兽的时候存在
    public static final String NS_COUNTDOWN = "ns_countdown:%s";

    // 年兽抽奖历史 redis zset key， 变化部分是 年兽ID
    public static final String NS_PLAYER_HIT_HISTORY = "ns_player_hit_history:%s";

    // 年兽抽奖历史 redis zset key， 变化部分是 年兽ID
    public static final String NS_ANCHOR_HIT_HISTORY = "ns_anchor_hit_history:%s";

    //年兽上一次的血量,用于播放鞭炮数 ns_last_blood:day {nsId:blood}
    public static final String NS_LAST_BLOOD = "ns_last_blood:%s";

    //总拥有的鞭炮 player_total_banger
    public static final String PLAYER_TOTAL_BANGER = "player_total_banger";

    //用掉的鞭炮 player_used_banger
    public static final String PLAYER_USED_BANGER = "player_used_banger";

    //用户每日首次进入记录 player_daily_first_enter_set_day
    public static final String PLAYER_DAILY_FIRST_ENTER_SET = "player_daily_first_enter_set_%s";

    //主播每日获得的队友金统计 anchor_daily_award_count {day:used}
    public static final String ANCHOR_HAS_AWARD_COUNT = "anchor_has_award_count";

    //主播奖池用完了通知
    public static final String ANCHOR_AWARD_RUN_OUT = "anchor_award_run_out";

    //全局(三业务)用户每日获得的队友金统计 anchor_daily_award_count {day:used}
    public static final String PLAYER_HAS_AWARD_COUNT = "player_has_award_count";

    public static final String ANCHOR_FIRST_SHOW_TIPS = "anchor_first_show_tips";

    /**
     * 当前频道是否在打龙,仅包含 集合阶段和打龙阶段 hash {sid_ssid: time_nsId}
     */
    public static final String NS_DISMISS_TIME = "ns_dismiss_time";

    /**
     * 年兽被打死时刻 hash  ns_dead_time:day {nsId:second}
     */
    public static final String NS_DEAD_TIME = "ns_dead_time:%s";

    /**
     * 奖包每日限制 package_daily_limit:%s   hash {packageId:count}
     */
    public static final String PACKAGE_DAILY_LIMIT = "package_daily_limit:%s";

    /**
     * 主播每日抽奖限制 hash 每个主播每天只能抽取两次  anchor_daily_lottery_limit:day {uid:count}
     */
    public static final String ANCHOR_DAILY_LOTTERY_LIMIT = "anchor_daily_lottery_limit:%s";

    /**
     * 用户参与年兽限制 set player_attend_mark:nsId {uid,uid,uid} expire 过期时间1小时
     */
    public static final String PLAYER_ATTEND_MARK = "player_attend_mark:%s";

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    protected UserinfoThriftClient userinfoThriftClient;

    @Override
    public Long getComponentId() {
        return ComponentId.NIANSHOU_CHANNEL;
    }

    //主播首次上麦,提示召唤年兽可得红包奖励
    @HdzjEventHandler(value = AnchorStartShowEvent.class, canRetry = false)
    public void createAnchorRegiment(AnchorStartShowEvent event, NianshouComponentAttr attr) {
        long uid = event.getUid();

        String key = makeKey(attr, ANCHOR_FIRST_SHOW_TIPS);
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        boolean first = actRedisDao.hsetnx(groupCode, key, String.valueOf(uid), DateUtil.getNowYyyyMMddHHmmss());
        if (!first) {
            return;
        }
        svcSDKService.unicastUid(uid, getNoticeMsg(attr.getActId(), "anchor_first_tip", attr.getAnchorFirstTip()));
        log.info("createAnchorRegiment done@uid:{} first:{} tip:{}", uid, first, attr.getAnchorFirstTip());
    }

    private GameecologyActivity.GameEcologyMsg getNoticeMsg(long actId, String type, String noticeMsg) {
        GameecologyActivity.CommonNoticeResponse.Builder tip = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(actId)
                .setNoticeType(type)
                .setNoticeMsg(noticeMsg);

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(tip).build();
        return msg;

    }


    @HdzjEventHandler(value = UserEnterTemplateEvent.class, canRetry = false)
    public void onUserEnterTemplateEvent(UserEnterTemplateEvent event, NianshouComponentAttr attr) {
        long uid = event.getUid();
        //等待模板加载完
        SysEvHelper.waiting(1000);
        userBanger(attr.getActId(), attr.getCmptUseInx(), uid);
    }

    /**
     * 用户参与抽奖
     */
    public int attend(long actId, long uid, JSONObject jsonObject) throws Exception {
        String nsId = jsonObject.getString("nsId");
        long cmptUseInx = getCmptUseInx(nsId);
        NianshouComponentAttr attr = this.getComponentAttr(actId, cmptUseInx);
        String groupCode = redisConfigManager.getGroupCode(actId);

        String key = makeKey(attr, String.format(PLAYER_ATTEND_MARK, nsId));
        if (actRedisDao.sAdd(groupCode, key, uid + "") != 1) {
            return -7;
        }
        actRedisDao.setExpire(groupCode, key, 3600);


        // 先做手机检查
        if (Const.isOk1(attr.getCheckMobileFlag())) {
            boolean strict = Const.isOk1(attr.getStrickCheckMobile());
            if (!userinfoThriftClient.checkUserMobile(uid, strict)) {
                return -1;
            }
        }

        // 年兽key不存在 或者 已经过期了

        String nsKey = makeKey(attr, String.format(NS_COUNTDOWN, nsId));

        //年兽开始时间 过期时间  22+8
        String expireTime = actRedisDao.get(groupCode, nsKey);
        if (expireTime == null) {
            return -2;
        }
        long nowSecond = DateUtil.getSeconds();
        long left = Convert.toLong(expireTime) - nowSecond;
        //不在战斗时间
        if (left > 0 && left > attr.getFightTime()) {
            return -3;
        }

        // 若已经报名了
        String zsetKey = makeKey(attr, String.format(NS_ATTEND, nsId));

        Long rank = actRedisDao.zRevRank(groupCode, zsetKey, String.valueOf(uid));
        if (rank != null) {
            return -4;
        }

        String totalKey = makeKey(attr, PLAYER_TOTAL_BANGER);
        String usedKey = makeKey(attr, PLAYER_USED_BANGER);
        long total = Convert.toLong(actRedisDao.hget(groupCode, totalKey, uid + ""));
        long used = Convert.toLong(actRedisDao.hget(groupCode, usedKey, uid + ""));
        //没鞭炮了
        if (total <= used) {
            return -5;
        }

        //打年兽
        String day = getDayByNsId(nsId);
        String detail = makeKey(attr, String.format(NS_BOX_DETAIL, day));
        NianshouInfo info = JSON.parseObject(actRedisDao.hget(groupCode, detail, nsId), NianshouInfo.class);
        int iconLevel = info.getAge() * info.getLevel();
        jsonObject.put("iconLevel", iconLevel + "");

        String bloodKey = makeKey(attr, String.format(NS_BLOOD_MAP, day));
        long blood = actRedisDao.hIncrByKey(groupCode, bloodKey, nsId, info.getDeduct());
        //已经被打死了,手慢了
        long totalVolume = info.getTotalVolume();
        long src = blood - info.getDeduct();
        //原来小于总血量才能可参与
        if (src >= totalVolume) {
            log.info("user attend fail, nianshou is dead!seq:{} uid:{}, ns:{} ", info.getSeq(), uid, nsId);
            return -6;
        }

        // 添加用户
        boolean flag = actRedisDao.zAdd(groupCode, zsetKey, String.valueOf(uid), nowSecond);
        long usedNew = actRedisDao.hIncrByKey(groupCode, usedKey, uid + "", 1);
        broUserBanger(attr.getActId(), uid, PBCommonNoticeType.NIANSHOU_USER_BANGER_LEFT, total - usedNew, "");
        log.info("user attend success seq:{} uid:{}, ns:{}", info.getSeq(), uid, nsId);
        bigDataService.saveNoRankDataToFile(actId, BusiId.findByValue(attr.getBusiId()), System.currentTimeMillis(), uid + "", RoleType.USER, 1
                , 5039, "use_banger", 0, 0);
        return flag ? 1 : 0;
    }

    /**
     * 用户/主播在线时长任务
     */
    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = false)
    public void onTaskProgressChanged(TaskProgressChanged event, NianshouComponentAttr attr) throws Exception {
        log.info("onTaskProgressChanged busi:{} uid:{} event:{}", attr.getBusiId(), event.getMember(), event);
        //1.榜单判断
        long rankId = event.getRankId();
        long actId = attr.getActId();
        long currTaskIndex = event.getCurrTaskIndex();
        String member = event.getMember();

        //主播开播时长任务
        long uid = Convert.toLong(member);
        long cmptUseInx = attr.getCmptUseInx();
        int busiId = attr.getBusiId();
        String seq = event.getSeq();
        anchorTask(event, attr, rankId, actId, currTaskIndex, member, uid, cmptUseInx, busiId, seq);

        //用户在线时长任务, 不存在连过
        final long rankId32 = 32;
        if (rankId == rankId32) {
            String groupCode = redisConfigManager.getGroupCode(actId);
            String totalKey = makeKey(attr, PLAYER_TOTAL_BANGER);
            //当前等级 + 1 个
            final int taskIndex1 = 1, taskIndex2 = 2, taskIndex3 = 3;
            long awardCount = 1;
            long time;
            //2分钟
            if (currTaskIndex == taskIndex1) {
                time = 2;
                //5分钟
            } else if (currTaskIndex == taskIndex2) {
                time = 5;
                // 10分钟
            } else if (currTaskIndex == taskIndex3) {
                time = 10;
            } else { //30分钟
                time = 30;
                awardCount = 2;
            }

            long totalBanger = actRedisDao.hIncrByKey(groupCode, totalKey, member, awardCount);
            String usedKey = makeKey(attr, PLAYER_USED_BANGER);
            long used = Convert.toLong(actRedisDao.hget(groupCode, usedKey, member));
            long left = totalBanger - used;

            //user_banger
            broUserBanger(actId, uid, PBCommonNoticeType.NIANSHOU_USER_BANGER_AWARD, awardCount, "观看直播" + time + "分钟");
            broUserBanger(actId, uid, PBCommonNoticeType.NIANSHOU_USER_BANGER_LEFT, left, "");
            bigDataService.saveNoRankDataToFile(actId, BusiId.findByValue(busiId), System.currentTimeMillis(), member, RoleType.USER, awardCount
                    , 5039, "online_Task:" + time, 0, 0);
            log.info("onTaskProgressChanged user finish 32 task,busi:{} uid:{}, currTaskIndex:{}, totalBanger:{}, left:{}", busiId, member, currTaskIndex, totalBanger, left);
        }

        //用户送礼
        //累计送十个,500个,一次性任务
        final long rankId36 = 36;
        if (rankId == rankId36) {
            long left = 0;
            long startTaskIndex = event.getStartTaskIndex() + 1;
            for (long i = startTaskIndex; i <= event.getCurrTaskIndex(); i++) {
                String groupCode = redisConfigManager.getGroupCode(actId);
                String totalKey = makeKey(attr, PLAYER_TOTAL_BANGER);
                long totalBanger = actRedisDao.hIncrByKey(groupCode, totalKey, member, 3);
                String usedKey = makeKey(attr, PLAYER_USED_BANGER);
                long used = Convert.toLong(actRedisDao.hget(groupCode, usedKey, member));
                left = totalBanger - used;
                long count = i == 1 ? 10 : 500;
                broUserBanger(actId, uid, PBCommonNoticeType.NIANSHOU_USER_BANGER_AWARD, 3, "赠送" + count + "个恭喜发财");
                log.info("onTaskProgressChanged user finish 36 task,busi:{} uid:{}, seq:{} currTaskIndex:{}, totalBanger:{},left:{}", busiId, member, seq, currTaskIndex, totalBanger, left);
                bigDataService.saveNoRankDataToFile(actId, BusiId.findByValue(busiId), System.currentTimeMillis(), member, RoleType.USER, 3
                        , 5039, "sendGift_Task:" + i, 0, 0);
            }
            broUserBanger(actId, uid, PBCommonNoticeType.NIANSHOU_USER_BANGER_LEFT, left, "");
        }
    }

    private List<Long> channelInfo(TaskProgressChanged event, long uid) {
        List<Long> channel = new ArrayList<>();
        final boolean match = Const.isGeOneFlag("get_ting_actor", 1);
        if (match) {
            Long tingActor = 20052L;
            try {
                String ting = event.getActors().get(tingActor);
                final boolean match1 = ting != null && !ting.contains("und");
                if (match1) {
                    String[] sid_ssid = ting.split("_");
                    final boolean match2 = sid_ssid.length == 2 && !"0".equals(sid_ssid[0]) && !"0".equals(sid_ssid[1]);
                    if (match2) {
                        channel.add(Convert.toLong(sid_ssid[0]));
                        channel.add(Convert.toLong(sid_ssid[1]));
                        return channel;
                    }
                }
            } catch (Exception e) {
                log.warn("channelInfo get channel by actor error");
            }
        }
        ChannelInfoVo channelInfoVo = culClient.queryUserChannel(uid);

        Long sid = 0L;
        Long ssid = 0L;
        if (channelInfoVo == null) {
            UserCurrentChannel userCurrentChannel = commonService.getUserCurrentChannel(uid);
            if (userCurrentChannel != null) {
                sid = userCurrentChannel.getTopsid();
                ssid = userCurrentChannel.getSubsid();
            }
        } else {
            sid = channelInfoVo.getSid();
            ssid = channelInfoVo.getSsid();
        }
        channel.add(sid);
        channel.add(ssid);
        return channel;
    }

    private void anchorTask(TaskProgressChanged event, NianshouComponentAttr attr, long rankId, long actId, long currTaskIndex, String member, long uid, long cmptUseInx, int busiId, String seq) throws Exception {
        final boolean notMatch = rankId != 31 && rankId != 33 && rankId != 34 && rankId != 35;
        if (notMatch) {
            return;
        }

        List<Long> channelInfo = channelInfo(event, uid);
        long sid = channelInfo.get(0);
        long ssid = channelInfo.get(1);

        final long rankId31 = 31, rankId33 = 33, rankId34 = 34, rankId35 = 35;
        final int taskIndex1 = 1, taskIndex2 = 2;
        if (rankId == rankId31) {
            String nsId = createNsId(cmptUseInx);
            if (currTaskIndex == taskIndex1) {
                anchorLottery(attr, member, nsId, "开播30分钟任务", seq);
            } else if (currTaskIndex == taskIndex2) {
                anchorLottery(attr, member, nsId, "开播60分钟任务", seq);
            }
            bigDataService.saveNoRankDataToFile(actId, BusiId.findByValue(busiId), System.currentTimeMillis(), member, RoleType.USER, 1
                    , 5039, "show_Task:" + currTaskIndex, 0, 0);
            if (sid == 0) {
                log.error("onTaskProgressChanged 主播不在频道内,无法下发年兽, member:{}, seq:{}", member, seq);
                return;
            }
            //不存在连过
            if (currTaskIndex == taskIndex1) {
                NianshouInfo nianshouInfo = buildNianshouInfo(1, seq, nsId, sid, ssid, uid, attr);
                addNianshou(nianshouInfo, attr);
            } else if (currTaskIndex == taskIndex2) {
                NianshouInfo nianshouInfo = buildNianshouInfo(2, seq, nsId, sid, ssid, uid, attr);
                addNianshou(nianshouInfo, attr);
            }

        }
        long roundComplete = event.getRoundComplete();
        //或福气值任务
        if (rankId == rankId33) {

            List<String> nsIds = new ArrayList<>();
            //主播抽奖
            for (int i = 0; i < roundComplete; i++) {
                String nsId = createNsId(cmptUseInx);
                nsIds.add(nsId);
                anchorLottery(attr, member, nsId, "累计收礼5000任务", seq);
                bigDataService.saveNoRankDataToFile(actId, BusiId.findByValue(busiId), System.currentTimeMillis(), member, RoleType.USER, 1
                        , 5039, "fuqi_Task_33", 0, 0);
            }

            if (sid == 0) {
                log.error("onTaskProgressChanged 主播不在频道内,无法下发年兽, member:{}, seq:{}", member, seq);
                return;
            }
            for (String nsId : nsIds) {
                NianshouInfo nianshouInfo = buildNianshouInfo(1, seq, nsId, sid, ssid, uid, attr);
                addNianshou(nianshouInfo, attr);
            }

        }
        if (rankId == rankId34) {
            //主播抽奖
            List<String> nsIds = new ArrayList<>();
            //主播抽奖
            for (int i = 0; i < roundComplete; i++) {
                String nsId = createNsId(cmptUseInx);
                nsIds.add(nsId);
                anchorLottery(attr, member, nsId, "累计收礼100000任务", seq);
                bigDataService.saveNoRankDataToFile(actId, BusiId.findByValue(busiId), System.currentTimeMillis(), member, RoleType.USER, 1
                        , 5039, "fuqi_Task_34", 0, 0);
            }

            if (sid == 0) {
                log.error("onTaskProgressChanged 主播不在频道内,无法下发年兽, member:{}, seq:{}", member, seq);
                return;
            }

            for (String nsId : nsIds) {
                NianshouInfo nianshouInfo = buildNianshouInfo(2, seq, nsId, sid, ssid, uid, attr);
                addNianshou(nianshouInfo, attr);
            }
        }
        if (rankId == rankId35) {
            log.info("anchor finish 35 task busi:{} uid:{}, currTaskIndex:{}, roundComplete:{}", busiId, member, currTaskIndex, roundComplete);
            List<String> nsIds = new ArrayList<>();
            //主播抽奖
            for (int i = 0; i < roundComplete; i++) {
                String nsId = createNsId(cmptUseInx);
                nsIds.add(nsId);
                anchorLottery(attr, member, nsId, "累计收礼200000任务", seq);
                bigDataService.saveNoRankDataToFile(actId, BusiId.findByValue(busiId), System.currentTimeMillis(), member, RoleType.USER, 1
                        , 5039, "fuqi_Task_35", 0, 0);
            }

            if (sid == 0) {
                log.error("onTaskProgressChanged 主播不在频道内,无法下发年兽, member:{}, seq:{}", member, seq);
                return;
            }
            for (String nsId : nsIds) {
                NianshouInfo nianshouInfo = buildNianshouInfo(3, seq, nsId, sid, ssid, uid, attr);
                addNianshou(nianshouInfo, attr);
            }
        }
    }

    public JSONObject userBanger(long actId, long cmptUseIndex, long uid) {
        long left = countUserBanger(actId, cmptUseIndex, uid);
        broUserBanger(actId, uid, PBCommonNoticeType.NIANSHOU_USER_BANGER_LEFT, left, "");
        JSONObject data = new JSONObject();
        data.put("result", 0);
        data.put("left", left);
        data.put("msg", "ok");
        return data;
    }


    public long countUserBanger(long actId, long cmptUseIndex, long uid) {
        NianshouComponentAttr attr = getComponentAttr(actId, cmptUseIndex);
        String groupCode = redisConfigManager.getGroupCode(actId);
        String totalKey = makeKey(attr, PLAYER_TOTAL_BANGER);
        long total = Convert.toLong(actRedisDao.hget(groupCode, totalKey, uid + ""));
        String usedKey = makeKey(attr, PLAYER_USED_BANGER);
        long used = Convert.toLong(actRedisDao.hget(groupCode, usedKey, uid + ""));
        long left = total - used;
        if (SysEvHelper.isDev() || SysEvHelper.isLocal()) {
            log.info("groupCode={},totalKey={},usedKey={},total={},used={}", groupCode, totalKey, usedKey, total, used);
        }

        return left;
    }


    private void broUserBanger(long actId, long uid, String type, long count, String desc) {
        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(actId)
                .setNoticeType(type)
                .setNoticeMsg(desc)
                .setNoticeValue(count + "")
                .setExtJson(uid + "");

        GameecologyActivity.GameEcologyMsg msgPanel = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();
        svcSDKService.unicastUid(uid, msgPanel);
        log.info("doBoxBroadcast broUserBanger ok msg:{}  ", JsonFormat.printToString(msgPanel));
    }

    private UserCurrentChannel userCurrentChannel(String json, long uid) {
        UserCurrentChannel userCurrentChannel = commonService.getUserCurrentChannel(uid);
        if (userCurrentChannel != null) {
            return userCurrentChannel;
        }
        JSONObject jsonObject = JSON.parseObject(json);
        UserCurrentChannel currentChannel = new UserCurrentChannel();
        currentChannel.setTopsid(jsonObject.getLongValue("sid"));

        currentChannel.setSubsid(jsonObject.getLongValue("ssid"));
        return currentChannel;
    }

    private void addNianshou(NianshouInfo nianshouInfo, NianshouComponentAttr attr) throws Exception {
        String nsId = nianshouInfo.getNsId();
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String channel = nianshouInfo.getChannel();
        String day = getDayByNsId(nsId);
        actRedisDao.getRedisTemplate(groupCode).execute(new RedisCallback<List<Object>>() {
            @Override
            public List<Object> doInRedis(RedisConnection connection) throws DataAccessException {
                String listKey = makeKey(attr, String.format(NS_BOX_LIST, channel));
                String hashKey = makeKey(attr, String.format(NS_BOX_DETAIL, day));
                String countKey = makeKey(attr, NS_CHANNEL_COUNT_MAP);
                connection.openPipeline();
                connection.hSet(hashKey.getBytes(), nsId.getBytes(), JSON.toJSONString(nianshouInfo).getBytes());
                connection.rPush(listKey.getBytes(), ("0|" + nsId + "|" + nianshouInfo.getTotalVolume()).getBytes());
                connection.hIncrBy(countKey.getBytes(), channel.getBytes(), 1);
                return connection.closePipeline();
            }
        });
        log.info("addNianshou done uid:{} seq:{} channel:{}, nsInfo:{}", nianshouInfo.getUid(), nianshouInfo.getSeq(), channel, JSON.toJSONString(nianshouInfo));
    }

    private NianshouInfo buildNianshouInfo(int level, String seq, String nsId, long sid, long ssid, long uid, NianshouComponentAttr attr) {
        String nsInfoJson = attr.getNsLevelMap().get(level);
        //每个等级固定信息的配置
        NianshouInfo info = JSON.parseObject(nsInfoJson, NianshouInfo.class);
        info.setNsId(nsId);
        info.setSeq(seq);
        info.setSid(sid);
        info.setSsid(ssid);
        info.setChannel(sid + "_" + ssid);
        info.setUid(uid);
        info.setBusiId(attr.getBusiId());
        info.setCmptUseInx(attr.getCmptUseInx());

        Date now = commonService.getNow(attr.getActId());
        Date date = DateUtil.getDate("2022-01-31 00:00:00");
        if (now.before(date)) {
            info.setAge(1);
        } else {
            info.setAge(2);
        }
        return info;
    }

    private String createNsId(long cmptUseIndex) {
        String uuid = java.util.UUID.randomUUID().toString().replace("-", "");
        String day = DateUtil.nowYyyyMMdd();
        return cmptUseIndex + "_" + day + "_" + uuid;
    }

    /**
     * 广播超级年兽，scanBox()的实现解决了并发问题，锁超时也没有问题的
     * 交友
     */
    // @Scheduled(cron = "*/1 * * * * ? ")
    public void broadcastBox500() {
        Clock clock = new Clock();
        String timerName = "locker_broadcast_ns_500_" + this.getComponentId();
        timerSupport.work(timerName, 3, () -> scanBox(500));
        log.info("broadcastBox500 Scheduled done@{}", clock.tag());
    }

    // 约战
    // @Scheduled(cron = "*/1 * * * * ? ")
    public void broadcastBox600() {
        Clock clock = new Clock();
        String timerName = "locker_broadcast_ns_600_" + this.getComponentId();
        timerSupport.work(timerName, 3, () -> scanBox(600));
        log.info("broadcastBox600 Scheduled done@{}", clock.tag());
    }

    // 宝贝
    // @Scheduled(cron = "*/1 * * * * ? ")
    public void broadcastBox400() {
        Clock clock = new Clock();
        String timerName = "locker_broadcast_ns_400_" + this.getComponentId();
        timerSupport.work(timerName, 3, () -> scanBox(400));
        log.info("broadcastBox400 Scheduled done@{}", clock.tag());
    }

    /**
     * 发放奖励， 使用 list 的 pop、push， 避免了定时器锁的需求
     */
    // @Scheduled(cron = "*/5 * * * * ? ")
    public void giveAwards() {
        Clock clock = new Clock();
        Set<Long> activityIds = this.getActivityIds();
        for (Long actId : activityIds) {
            String groupCode = redisConfigManager.getGroupCode(actId);
            List<NianshouComponentAttr> attrs = this.getAllComponentAttrs(actId);
            for (NianshouComponentAttr attr : attrs) {
                //处理主播和用户的奖励下发
                processAward(groupCode, NS_PLAYER_AWARD_ISSUE, attr);
                processAward(groupCode, NS_ANCHOR_AWARD_ISSUE, attr);
            }
        }
        log.info("giveAwards Scheduled done@act size:{} {}", activityIds.size(), clock.tag());
    }

    private void processAward(String groupCode, String awardIssueKey, NianshouComponentAttr attr) {
        String listKey = makeKey(attr, awardIssueKey);

        // 记录队列当前长度, 若为0直接结束循环
        long len = actRedisDao.llen(groupCode, listKey);
        if (len == 0) {
            return;
        }

        long total = len;
        String content = null;
        while ((content = actRedisDao.lpop(groupCode, listKey)) != null) {
            giveOneAward(groupCode, listKey, content, attr);
            // 因为giveOneAward中会将失败的重新入队，这里用来防止无限循环，多机情况下会有一些冗余尝试，影响不大
            if (--len < 0) {
                log.warn("giveAwards break@key:{}, total:{}, len:{}", listKey, total, len);
                break;
            }
        }
    }

    /**
     * todo 上报数据到海度
     */
    private void updateToHive(String nsId, GiftPlaneBO bo, NianshouComponentAttr attr) {
        if (StringUtil.isNotBlank(nsId)) {
            long actId = attr.getActId();
            Date now = commonService.getNow(actId);
            long total = bo.getCount() * bo.getGiftNum();
            BusiId busiId = broadCastHelpService.changeBroTemplate2BusiId(Template.findByValue(attr.getTemplate()));
            bigDataService.saveNoRankDataToFile(nsId, actId, busiId, now.getTime(), bo.getSendUid() + "", RoleType.USER, total
                    , 14, bo.getGiftId(), bo.getCount(), bo.getRecvUid());
        }
    }


    /**
     * 是否处理该gift
     *
     * @return true:符合 false:不符合
     */
    private long getCmptUseInx(String nsId) {
        return Long.parseLong(nsId.split("_")[0]);
    }

    public void scanBox(int busi) {
        Clock clock = new Clock();
        Set<Long> activityIds = this.getActivityIds();
        int i = 0;
        for (Long actId : activityIds) {
            List<NianshouComponentAttr> attrs = this.getAllComponentAttrs(actId);
            for (NianshouComponentAttr attr : attrs) {
                if (busi == attr.getBusiId()) {
                    processNsns(attr);
                    i++;
                }
            }
        }
        log.info("scanBox Scheduled done@ns size:{} {}", i, clock.tag());
    }

    /**
     * 判断年兽条目是否有效
     */
    private boolean isValidBoxItem(String nsItem) {
        if (StringUtil.isBlank(nsItem)) {
            return false;
        }
        String[] values = nsItem.split("\\|");
        final int three = 3;
        if (values.length != three) {
            return false;
        }
        return true;
    }

    private void processNsns(NianshouComponentAttr attr) {
        Clock clock = new Clock();
        // 1. 先查看队队首的年兽，若为无效元素，直接返回
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String listKey = makeKey(attr, NS_CHANNEL_COUNT_MAP);
        Map<Object, Object> hGetAll = actRedisDao.hGetAll(groupCode, listKey);
        ExecutorService service = getServiceByBusi(attr.getBusiId());
        CountDownLatch countDownLatch = new CountDownLatch(hGetAll.size());
        int nsPlayingCount = 0;
        for (Map.Entry<Object, Object> entry : hGetAll.entrySet()) {
            long count = Convert.toLong(entry.getValue());
            if (count > 0) {
                service.execute(() -> {
                    processBox(attr, String.valueOf(entry.getKey()));
                    countDownLatch.countDown();
                });
                nsPlayingCount++;
            } else {
                countDownLatch.countDown();
            }
        }
        try {
            countDownLatch.await(3, TimeUnit.SECONDS);
            noticeNsCount(attr, nsPlayingCount);
        } catch (InterruptedException e) {
            log.error("countDownLatch.await error:{}", e.getMessage(), e);
        }

        log.info("processNsns done act:{}  busi:{}, size:{} clock:{}", attr.getActId(), attr.getBusiId(), hGetAll.size(), clock.tag());
    }

    private void noticeNsCount(NianshouComponentAttr attr, int nsPlayingCount) {
        int busiId = attr.getBusiId();
        if (busiId != BusiId.GAME_BABY.getValue() && busiId != BusiId.MAKE_FRIEND.getValue() && busiId != BusiId.YUE_ZHAN.getValue()) {
            return;
        }
        final int gameBabyCount = 200, makeFriendCount = 700, yueZhanCount = 200;
        if (busiId == BusiId.GAME_BABY.getValue() && nsPlayingCount < gameBabyCount) {
            return;
        }
        if (busiId == BusiId.MAKE_FRIEND.getValue() && nsPlayingCount < makeFriendCount) {
            return;
        }
        if (busiId == BusiId.YUE_ZHAN.getValue() && nsPlayingCount < yueZhanCount) {
            return;
        }
        String makeKey = makeKey(attr, "notice_ns_count");
        String hour = DateUtil.format(new Date(), DateUtil.PATTERN_TYPE7);
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        if (actRedisDao.hsetnx(groupCode, makeKey, hour, nsPlayingCount + "")) {
            String msgFormat = "年兽玩法消息告警,业务:%s\n" +
                    "正在玩的年兽数量为 %s\n" +
                    " 请检测该日志输出: processNsns done act:{actId}";
            String msg = String.format(msgFormat, busiId, nsPlayingCount);
            //baiduInfoFlowRobotService.asyncSendNotifyByActAttrKey(attr.getActId(), "act_notice", msg, Lists.newArrayList());
            baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Lists.newArrayList());
        }
    }

    private ExecutorService getServiceByBusi(int busi) {
        return null;
    }


    private void processBox(NianshouComponentAttr attr, String sid_ssid) {
        Clock clock = new Clock();
        String nsItem = null;
        try {
            // 1. 先查看队队首的年兽，若为无效元素，直接返回
            String groupCode = redisConfigManager.getGroupCode(attr.getActId());
            String listKey = makeKey(attr, String.format(NS_BOX_LIST, sid_ssid));
            nsItem = actRedisDao.lindex(groupCode, listKey, 0);
            if (nsItem == null) {
                return;
            }
            String nsChannelCountKey = makeKey(attr, NS_CHANNEL_COUNT_MAP);
            // 2. 去掉无效的年兽条目
            if (!isValidBoxItem(nsItem)) {
                long size = actRedisDao.lrem(groupCode, listKey, nsItem);
                if (size < 1) {
                    log.error("processBox invalid1@nsItem:{}, listKey:{} modified by other!!! {}", nsItem, listKey, clock.tag());
                } else {
                    log.error("processBox invalid2@nsItem:{}, listKey:{} removed size:{} {}", nsItem, listKey, size, clock.tag());
                }
                //当前频道减1
                long l = actRedisDao.hIncrByKey(groupCode, nsChannelCountKey, sid_ssid, -1);
                log.info("isValidBoxItem is true reduce 1 channel:{}, nsItem:{}", sid_ssid, nsItem);
                return;
            }

            // 3. 准备数据，在 redis lua 中做倒计时
            Date now = new Date();
            String[] values = nsItem.split("\\|");
            long startTime = Long.parseLong(values[0]);
            String nsId = values[1];
            long totalVolume = Long.parseLong(values[2]);
            //总展示时间
            int nsShowSeconds = attr.getPreTime() + attr.getFightTime() + attr.getShowResultTime();

            //获取倒计时
            long left = getLeft(now, startTime, nsShowSeconds);

            //创建是否可点击的判断key
            String nsCountdownKey = makeKey(attr, String.format(NS_COUNTDOWN, nsId));
            String bloodKey = makeKey(attr, String.format(NS_BLOOD_MAP, getDayByNsId(nsId)));
            //当前频道是否正在打龙
            String dismissKey = makeKey(attr, NS_DISMISS_TIME);

            String deadTimeKey = makeKey(attr, String.format(NS_DEAD_TIME, getDayByNsId(nsId)));

            List<String> keys = Lists.newArrayList(listKey, nsCountdownKey, bloodKey, nsChannelCountKey, dismissKey, deadTimeKey);
            List<String> argv = Lists.newArrayList(left + "", nsId, startTime + "", attr.getPreTime() + "",
                    attr.getFightTime() + "", attr.getShowResultTime() + "", sid_ssid, totalVolume + "", DateUtil.getSeconds(now) + "");
            long ret = actRedisDao.executeLua(groupCode, "ns_countdown.lua", Long.class, keys, argv);
            if (ret < 0) {
                log.error("processNsBox wrong@nsItem:{}, listKey:{}, nsKey:{}, left:{}, ret:{} {}", nsItem, listKey, nsCountdownKey, left, ret, clock.tag());
                return;
            }

            // 4. 异步处理年兽广播
            threadPoolManager.get(Const.GENERAL_POOL).execute(() -> doBoxBroadcast(attr, nsId, left, ret));
            log.info("processBox done nsId:{}, clock:{}", nsId, clock.tag());
        } catch (Throwable t) {
            log.error("processBox exception@nsItem:{}, attr:{}, err:{} {}", nsItem, attr, t.getMessage(), clock.tag(), t);
        }
    }

    /**
     * @param nsId busi_day_uuid
     * @return
     * @throws Exception
     */
    private String getDayByNsId(String nsId) throws Exception {
        String[] s = nsId.split("_");
        final int three = 3;
        if (s.length != three) {
            throw new Exception("nianshou ID is wrong:" + nsId);
        }
        return s[1];
    }

    /**
     * 计算倒计时剩余秒数
     *
     * @param startTime     单位 秒
     * @param nsShowSeconds 总时长 准备时间 + 战斗时间 + 展示时间
     */
    private long getLeft(Date now, long startTime, long nsShowSeconds) {
        if (startTime == 0) {
            return nsShowSeconds;
        }
        long sNow = DateUtil.getSeconds(now);
        return nsShowSeconds - (sNow - startTime);
    }

    private void broNSFightTimeOver(NianshouComponentAttr attr, NianshouInfo ns) {
        String timeOver = makeKey(attr, "bro_ns_fight_time_over");
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        if (!actRedisDao.hsetnx(groupCode, timeOver, ns.getNsId(), DateUtil.getNowYyyyMMddHHmmss())) {
            return;
        }
        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(attr.getActId())
                .setNoticeType("nianshouFightTimeOver")
                .setNoticeMsg("年兽未被打败！寻求勇士召唤")
                .setNoticeValue("年兽未被打败！寻求勇士召唤");

        GameecologyActivity.GameEcologyMsg msgPanel = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();
        svcSDKService.broadcastSub(ns.getSid(), ns.getSsid(), msgPanel);
        log.info("nianshou fight time over, seq:{}, nsId:{} ns:{}", ns.getSeq(), ns.getNsId(), JSON.toJSONString(ns));
    }

    /**
     * 做年兽通知：通知横幅 或者 通知开奖结果
     *
     * @param left 剩余时间 含展示时间
     * @param ret  1-> 集合阶段或者战斗阶段
     *             2-> 年兽被打死了
     *             3->年兽未被打死,超时了
     */
    public void doBoxBroadcast(NianshouComponentAttr attr, String nsId, long left, long ret) {
        Clock clock = new Clock();
        NianshouInfo ns = null;
        try {
            String groupCode = redisConfigManager.getGroupCode(attr.getActId());
            String hashKey = makeKey(attr, String.format(NS_BOX_DETAIL, getDayByNsId(nsId)));
            String nsInfo = actRedisDao.hget(groupCode, hashKey, nsId);
            ns = JSON.parseObject(nsInfo, NianshouInfo.class);

            //年兽被打死了, 发奖
            final int two = 2, three = 3;
            if (ret == two) {
                ns.setStage(5);
                //我知道了按钮倒计时
                ns.setLeftSecond(0L);
                ns.setLeftVolume(0L);
                broFightingNianshou(attr, ns);
                openBox(ns, attr);

                return;
            }
            //年兽未被打死,已超时
            if (ret == three) {
                //todo 发结束广播  done
                ns.setStage(4);
                //我知道了按钮倒计时
                ns.setLeftSecond(0L);
                nianshouLeftBlood(attr, ns, groupCode);
                broFightingNianshou(attr, ns);
                //年兽未被打败！寻求勇士召唤
                broNSFightTimeOver(attr, ns);

                return;
            }
            //普通广播 todo 构建年兽信息, done
            //8+5
            int fightTime = attr.getFightTime();
            int showResultTime = attr.getShowResultTime();
            //减掉展示时间
            long leftSecond = left - showResultTime;
            if (leftSecond >= fightTime) {
                broNianshouShowTime(attr, ns);
                //集合阶段倒计时
                ns.setStage(1);
                ns.setLeftSecond(leftSecond - fightTime);
                broFightingNianshou(attr, ns);
                return;
            }
            //结果展示阶段不需做什么处理,结果在发奖的时候已下发, 已删除展示阶段
            if (left <= showResultTime) {
                ns.setStage(3);
                return;
            }
            //正在打年兽
            if (leftSecond <= fightTime) {
                ns.setStage(2);
                ns.setLeftSecond(leftSecond);
                nianshouLeftBlood(attr, ns, groupCode);
            }
            //log.info("fighting nianshow ns:{}", JSON.toJSONString(ns));
            broFightingNianshou(attr, ns);
        } catch (Throwable t) {
            log.info("doBoxBroadcast exception@ns:{}, err:{} {}", ns, t.getMessage(), clock.tag(), t);
        }
    }

    private void nianshouLeftBlood(NianshouComponentAttr attr, NianshouInfo ns, String groupCode) throws Exception {
        String day = getDayByNsId(ns.getNsId());
        String bloodKey = makeKey(attr, String.format(NS_BLOOD_MAP, day));
        String lastBloodKey = makeKey(attr, String.format(NS_LAST_BLOOD, day));
        long blood = Convert.toLong(actRedisDao.hget(groupCode, bloodKey, ns.getNsId()));
        long lastBlood = Convert.toLong(actRedisDao.hget(groupCode, lastBloodKey, ns.getNsId()));
        long leftBlood = ns.getTotalVolume() - blood;
        //更新当前扣除的血量
        actRedisDao.hset(groupCode, lastBloodKey, ns.getNsId(), blood + "");
        if (leftBlood > 0) {
            ns.setLeftVolume(leftBlood);
            ns.setDeductTime((blood - lastBlood) / ns.getDeduct());
        } else {
            ns.setLeftVolume(0L);
        }
    }

    private void broFightingNianshou(NianshouComponentAttr attr, NianshouInfo ns) {
        NianshouInfo broInfo = new NianshouInfo(ns.getNsId(), ns.getLevel(), ns.getTotalVolume(), ns.getLeftVolume(),
                System.currentTimeMillis(), ns.getDeduct(), ns.getDeductTime(), ns.getLeftSecond(), ns.getStage(), ns.getAge());
        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(attr.getActId())
                .setNoticeType(PBCommonNoticeType.NIANSHOU_BOX)
                .setNoticeMsg(commonService.getNickName(ns.getUid(), false))
                .setNoticeValue(String.valueOf(ns.getUid()))
                .setExtJson(JSON.toJSONString(broInfo));

        GameecologyActivity.GameEcologyMsg msgPanel = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();
        svcSDKService.broadcastSub(ns.getSid(), ns.getSsid(), msgPanel);
        //log.info("doBoxBroadcast broadcastSub nsId:{} ok@ns:{}, panel:{} ", ns.getNsId(), JSON.toJSONString(ns), JsonFormat.printToString(msgPanel));
    }

    private void broNianshouShowTime(NianshouComponentAttr attr, NianshouInfo ns) {
        int level = ns.getLevel();
        //1级不发广播
        if (level == 1) {
            return;
        }
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String showTimeKey = makeKey(attr, "nianshou_show_time_bro");
        //一条年兽只播一次
        final boolean rs = actRedisDao.hsetnx(groupCode, showTimeKey, ns.getNsId(), DateUtil.getNowYyyyMMddHHmmss());
        if (!rs) {
            return;
        }

        //UserBaseInfo ubi = broadCastHelpService.getUserBaseInfo(ns.getUid(), BusiId.findByValue(ns.getBusiId()));
        Map<String, Object> data = Maps.newHashMap();
        data.put("sid", ns.getSid());
        data.put("ssid", ns.getSsid());
        data.put("level", level);
        //data.put("nick", Base64.encodeBase64String(ubi.getNick().getBytes()));
        //data.put("icon", icon);
        data.put("age", ns.getAge());

        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(attr.getActId())
                .setNoticeType("nianshouShowTime")
                .setNoticeMsg(commonService.getNickName(ns.getUid(), false))
                .setNoticeValue(String.valueOf(ns.getUid()))
                .setExtJson(JSON.toJSONString(data));

        GameecologyActivity.GameEcologyMsg msgPanel = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();
        final int level2 = 2;
        if (level == level2) {
            svcSDKService.broadcastTop(ns.getSid(), msgPanel);
        } else {
            svcSDKService.broadcastTemplate(findByBusi(attr.getBusiId()), msgPanel);
        }
        log.info("doBoxBroadcast broNianshouShowTime nsId:{} ok@ns:{}, panel:{} ", ns.getNsId(), JSON.toJSONString(ns), JsonFormat.printToString(msgPanel));
    }

    public static Template findByBusi(int value) {
        switch (value) {
            case 600:
                return Yuezhan;
            case 400:
                return Gamebaby;
            case 500:
                return Jiaoyou;
            default:
                return null;
        }
    }

    /**
     * 处理开奖，并广播开奖结果
     */
    public void openBox(NianshouInfo ns, NianshouComponentAttr attr) throws TException {
        Clock clock = new Clock();
        String nsId = ns.getNsId();
        String seq = ns.getSeq();
        long uid = ns.getUid();

        // 检查奖池等级概率设置
        Map<Long, Long> awardProbabilitys = attr.getPlayerShuijingProbabilities().get(ns.getLevel());
        if (MapUtils.isEmpty(awardProbabilitys)) {
            log.error("openBox fail@awardProbabilitys is empty! nsId:{}, seq:{}, uid:{}", nsId, seq, uid);
            return;
        }

        // 检查基础占比设置
       /* int basePercentage = attr.getBasePercentage();
        if (basePercentage < 0 || basePercentage > 100) {
            log.error("openBox fail@basePercentage {} not in [0 ~ 100]! nsId:{}, seq:{}, uid:{}", basePercentage, nsId, seq, uid);
            return;
        }*/

        // 先判断以前是否开过年兽
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String openKey = makeKey(attr, NS_BOX_OPEN);
        if (!actRedisDao.hsetnx(groupCode, openKey, nsId, DateUtil.today())) {
            String at = actRedisDao.hget(groupCode, openKey, nsId);
            log.warn("openBox nsId:{} has done!!!! at:{}, seq:{}, uid:{}", nsId, at, seq, uid);
            return;
        }
        bigDataService.saveNoRankDataToFile(attr.getActId(), BusiId.findByValue(attr.getBusiId()), System.currentTimeMillis(),
                nsId, RoleType.USER, 1
                , 5039, "dead_nianshou:" + ns.getLevel(), 0, 0);

        // 从list中取出前 x 名用户，然后将报名key设置 30分钟后过期，防止数据堆积
        String zsetKey = makeKey(attr, String.format(NS_ATTEND, nsId));
        Set<ZSetOperations.TypedTuple<String>> set = actRedisDao.zrange(groupCode, zsetKey, attr.getMaxHitUserNum());
        actRedisDao.setExpire(groupCode, zsetKey, 1800);

        if (CollectionUtils.isEmpty(set)) {
            return;
        }

        // 按概率先确定选择哪个级别的奖励，随机打乱用户列表
        List<Long> users = set.stream().map(tuple -> new Long(tuple.getValue())).collect(Collectors.toList());
        Collections.shuffle(users);

        //int luckNum = RandomUtils.nextInt(3, 5);
        //List<Long> luckUser = users.subList(0, 1);
        List<Long> normalUser = users.subList(1, users.size());

        List<JSONObject> awardUserList = new ArrayList<>();

        // 随机分配
        int i = randomDistribute(ns, attr, awardProbabilitys, users.get(0), awardUserList);

        //超限额了,全部用户抽免费奖池
        if (i != 1) {
            normalUser = users;
        }
        //todo 普通抽奖,直接在中控实现概率抽奖和限量,统一下发奖励 done
        lotteryAward(ns, attr, normalUser, awardUserList);

        // 开箱通知
        openBoxNotify(ns, attr, clock, nsId, awardUserList);
        log.info("nianshou fight success, seq:{} nsId:{}, award:{} ns:{}", ns.getSeq(), ns.getNsId(), JSON.toJSONString(awardUserList), JSON.toJSONString(ns));
    }

    //todo 用户抽奖 done
    public void lotteryAward(NianshouInfo ns, NianshouComponentAttr attr, List<Long> normalUser, List<JSONObject> awardUserList) {
        Map<Long, Long> probabilities = attr.getPlayerNormalProbabilities().get(ns.getLevel());
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        Date now = commonService.getNow(attr.getActId());
        String day = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        String packageLimit = makeKey(attr, String.format(PACKAGE_DAILY_LIMIT, day));
        Map<Object, Object> packageLimitObj = actRedisDao.hGetAll(groupCode, packageLimit);
        Map<Long, Long> packageLimitMap = Maps.newHashMap();
        if (packageLimitObj != null) {
            packageLimitObj.forEach((k, v) -> packageLimitMap.put(Convert.toLong(k), Convert.toLong(v)));
        }

        List<Long> awardList = Lists.newArrayList();
        List<Long> probabilityList = Lists.newArrayList();
        Map<Long, Long> packageDayLimit = attr.getPackageDayLimit();
        for (Map.Entry<Long, Long> entry : probabilities.entrySet()) {
            if (beyondLimit(packageLimitMap, entry.getKey(), packageDayLimit)) {
                continue;
            }
            awardList.add(entry.getKey());
            probabilityList.add(entry.getValue());
        }
        Map<Long, Long> uid2package = Maps.newHashMap();
        for (Long uid : normalUser) {
            int index = RandomUtil.randomIndex(probabilityList);
            Long packageId = lotteryWithLimit(packageDayLimit, index, awardList, packageLimitMap, groupCode, packageLimit);
            //找不到发奖的项目,直接返回
            if (packageId == null) {
                log.error("player lotteryAward packageId is null, nsId:{}, seq:{} uid:{}", ns.getNsId(), ns.getSeq(), uid);
                return;
            }
            uid2package.put(uid, packageId);
            JSONObject data = new JSONObject();
            data.put("uid", uid);
            data.put("packageId", packageId);
            data.put("luckest", 0);
            awardUserList.add(data);
        }

        savePlayerNormalAward(attr.getBusiId(), ns.getLevel(), ns.getNsId(), attr, uid2package, ns.getSeq());
    }

    /**
     * @param packageLimitMap 已抽取
     * @param packageId
     * @param packageDayLimit 限量,若无限量,这不会超
     * @return
     */
    private boolean beyondLimit(Map<Long, Long> packageLimitMap, Long packageId, Map<Long, Long> packageDayLimit) {
        long dailyLimit = Convert.toLong(packageDayLimit.get(packageId));
        if (dailyLimit == 0) {
            return false;
        }
        long hasAward = Convert.toLong(packageLimitMap.get(packageId));
        return hasAward >= dailyLimit;
    }

    /**
     * 第一次是随机获取,若是有限量且超过限量,则轮询找到下一个无限量或未超额的package
     *
     * @param packageDayLimit 每日限量
     * @param index
     * @param awardList
     * @param packageLimitMap 已抽的数量
     * @param groupCode
     * @param packageLimitKey
     * @return
     */
    public Long lotteryWithLimit(Map<Long, Long> packageDayLimit, int index, List<Long> awardList,
                                 Map<Long, Long> packageLimitMap, String groupCode, String packageLimitKey) {
        if (CollectionUtils.isEmpty(awardList)) {
            return null;
        }
        Long packageId = awardList.get(index);
        Long total = packageDayLimit.get(packageId);
        if (able2award(total, packageLimitMap, groupCode, packageLimitKey, packageId)) {
            return packageId;
        }
        for (Long pid : awardList) {
            total = packageDayLimit.get(pid);
            if (able2award(total, packageLimitMap, groupCode, packageLimitKey, pid)) {
                return pid;
            }
        }
        return null;
    }

    /**
     * @param total           每日总额
     * @param packageLimitMap 每日已用
     * @param groupCode
     * @param packageLimitKey redis key
     * @param packageId
     * @return
     */
    private boolean able2award(Long total, Map<Long, Long> packageLimitMap, String groupCode, String packageLimitKey, Long packageId) {
        if (total == null || total == 0) {
            return true;
        }
        //当天限量已用完,则不能下发
        if (Convert.toLong(total) <= Convert.toLong(packageLimitMap.get(packageId))) {
            return false;
        }
        //每日总额不为0,则必然有限额
        List<Long> list = actRedisDao.hIncrWithLimit(groupCode, packageLimitKey, packageId + "", 1, total, false);
        packageLimitMap.put(packageId, list.get(1));
        return list.get(0) > 0;
    }

    /*public Long lotteryWithLimit(Map<Long, Long> packageDayLimit, int index, List<Long> awardList,
                                 List<Long> probabilityList, Map<Long, Long> packageLimitMap, String groupCode, String packageLimit) {
        if (CollectionUtils.isEmpty(awardList)) {
            return null;
        }
        Long packageId = awardList.get(index);
        Long total = packageDayLimit.get(packageId);
        if (total == null || total == 0) {
            return packageId;
        }
        long used = Convert.toLong(packageLimitMap.get(packageId));
        if (used < total) {
            long nexUsed = actRedisDao.hIncrByKey(groupCode, packageLimit, packageId + "", 1);
            packageLimitMap.put(packageId, nexUsed);
            return packageId;
        }
        awardList.remove(index);
        probabilityList.remove(index);

        return lotteryWithLimit(packageDayLimit, RandomUtil.randomIndex(probabilityList), awardList, probabilityList, packageLimitMap, groupCode, packageLimit);
    }
*/
    //todo 主播抽奖 done
    private void anchorLottery(NianshouComponentAttr attr, String member, String nsId, String missionDesc, String seq) {
        try {

            Date now = commonService.getNow(attr.getActId());
            String day = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
            String limitKey = makeKey(attr, String.format(ANCHOR_DAILY_LOTTERY_LIMIT, day));
            String groupCode = redisConfigManager.getGroupCode(attr.getActId());
            long dayLimit = actRedisDao.hIncrByKey(groupCode, limitKey, member, 1);
            final int two = 2;
            if (dayLimit > two) {
                log.info("anchor has benn lottery 2 times , member:{} seq:{} dayLimit:{}, nsId:{} missionDesc:{}", member, seq, dayLimit, nsId, missionDesc);
                return;
            }

            long anchorTotalAwardCount = attr.getAnchorTotalAwardCount();
            Map<Long, Long> probabilities = attr.getAnchorAwardProbabilities();
            // 构造奖励 和 奖励概率 对应的 list，确保下标对应
            List<Long> awardList = Lists.newArrayList();
            List<Long> probabilityList = Lists.newArrayList();
            for (Map.Entry<Long, Long> entry : probabilities.entrySet()) {
                awardList.add(entry.getKey());
                probabilityList.add(entry.getValue());
            }
            int index = RandomUtil.randomIndex(probabilityList);
            Long step = awardList.get(index);

            List<Long> list = actRedisDao.hIncrWithLimit(groupCode, ANCHOR_HAS_AWARD_COUNT, day, step, attr.getAnchorTotalAwardCount(), true);
            long ret = list.get(0);
            long realInc = 0;
            if (ret == 1) {
                realInc = step;
            } else if (ret == two) {
                realInc = list.get(1);
            }
            if (realInc > 0) {
                saveAnchorAward(attr.getBusiId(), nsId, attr, ImmutableMap.of(member, realInc));
                broAnchorAwardTips(attr, missionDesc, member, realInc);
            }

            if (realInc < step && actRedisDao.hsetnx(groupCode, ANCHOR_AWARD_RUN_OUT, day, anchorTotalAwardCount + "")) {
                String msgFormat = "年兽玩法消息告警:\n" +
                        "主播队友金奖池已用完\n" +
                        " 总队友金数:%s ";
                String msg = String.format(msgFormat, anchorTotalAwardCount);
                //baiduInfoFlowRobotService.asyncSendNotifyByActAttrKey(attr.getActId(), "act_notice", msg, Lists.newArrayList());
                baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Lists.newArrayList());
            }
            log.info("anchorLottery result: busi:{} member:{}, seq:{} mission:{} nsId:{} list:{}, step:{}, realInc:{}, ",
                    attr.getBusiId(), member, seq, missionDesc, nsId, JSON.toJSONString(list), step, realInc);
        } catch (Exception e) {
            log.error("anchorLottery error,member:{} seq:{} mission:{} nsId:{}", member, seq, missionDesc, nsId, e);
        }
    }

    /**
     * 获取每日队友金总数和剩余值
     **/
    public JSONObject getAwardInfo(long actId, long cmptUseIndex) {
        JSONObject jsonObject = new JSONObject();
        NianshouComponentAttr attr = getComponentAttr(actId, cmptUseIndex);
        if (attr == null) {
            return jsonObject;
        }

        long total = attr.getAnchorTotalAwardCount();
        jsonObject.put("totalCount", total);
        Date now = commonService.getNow(actId);
        String day = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        long used = Convert.toLong(actRedisDao.hget(groupCode, ANCHOR_HAS_AWARD_COUNT, day), 0L);
        jsonObject.put("leftCount", Math.max(total - used, 0));

        return jsonObject;
    }

    private void broAnchorAwardTips(NianshouComponentAttr attr, String missionDesc, String member, long award) {
        JSONObject json = new JSONObject();
        json.put("desc", missionDesc);
        json.put("award", award);
        GameecologyActivity.Act202008_AnchorTips.Builder anchorTips = GameecologyActivity.Act202008_AnchorTips.newBuilder()
                .setActId(attr.getActId());
        anchorTips.setExtjson(json.toJSONString());

        GameecologyActivity.GameEcologyMsg anchorTipsMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.kAct202008_AnchorTips_VALUE)
                .setAct202008AnchorTips(anchorTips).build();
        svcSDKService.unicastUid(Convert.toLong(member), anchorTipsMsg);
    }

    @Autowired
    private UserInfoService userInfoService;


    /**
     * 开箱结果通知
     */
    private void openBoxNotify(NianshouInfo ns, NianshouComponentAttr attr, Clock clock, String nsId,
                               List<JSONObject> awardUserList) throws TException {
        List<Long> uids = awardUserList.stream().map(it -> it.getLongValue("uid")).collect(Collectors.toList());
        Map<Long, UserInfoVo> userInfoMap = userInfoService.getUserInfo(uids, com.yy.gameecology.common.bean.Template.getTemplateByBusi(attr.getBusiId()));
        Map<Long, AwardModelInfo> packageInfoMap = hdztAwardServiceClient.queryAwardTasks(attr.getPlayerNormalTaskId().get(ns.getLevel()));

        List<JSONObject> allUserAward = new ArrayList<>();

        for (JSONObject data : awardUserList) {
            Long uid = data.getLong("uid");
            UserInfoVo userInfoVo = userInfoMap.get(uid);
            //跳过找不到信息的用户
            if (userInfoVo == null) {
                continue;
            }
            //有packageId 说明是普通奖励
            Long packageId = data.getLong("packageId");
            JSONObject userData = new JSONObject();
            userData.put("nick", Base64.encodeBase64String(userInfoVo.getNick().getBytes()));
            userData.put("nsId", nsId);
            userData.put("deduct", ns.getDeduct());
            userData.put("luckest", data.getIntValue("luckest"));
            if (packageId != null) {
                AwardModelInfo awardModelInfo = packageInfoMap.get(packageId);
                userData.put("award", awardModelInfo.getPackageName());
                userData.put("img", awardModelInfo.getPackageImage());
            } else { //水晶奖励
                Map<String, Object> map = attr.getBusi2AwardUnitImg().get(attr.getBusiId());
                userData.put("award", data.getLongValue("amount") + "" + map.get("unit"));
                userData.put("img", map.get("img"));
            }
            allUserAward.add(userData);
            GameecologyActivity.GameEcologyMsg msg = broUserAward(attr.getActId(), userData.toJSONString(), PBCommonNoticeType.NIANSHOU_SINGLE_AWARD_RESULT);
            svcSDKService.unicastUid(uid, msg);
        }
        GameecologyActivity.GameEcologyMsg msg = broUserAward(attr.getActId(), JSON.toJSONString(allUserAward), PBCommonNoticeType.NIANSHOU_AWARD_RESULT);

        svcSDKService.broadcastSub(ns.getSid(), ns.getSsid(), msg);
        log.info("openBoxNotify ok@nsId:{}, panel:{} {}", ns.getNsId(), JsonFormat.printToString(msg), clock.tag());
    }


    private GameecologyActivity.GameEcologyMsg broUserAward(long actId, String json, String type) {
        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(actId)
                .setNoticeType(type)
                .setNoticeValue(json);
        return GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();
    }

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    /**
     * 随机分配
     */
    private int randomDistribute(NianshouInfo ns, NianshouComponentAttr attr, Map<Long, Long> awardProbabilitys, long uid, List<JSONObject> awardUserList) {

        Map<String, Long> allHitUsers = Maps.newHashMap();
        // 构造奖励 和 奖励概率 对应的 list，确保下标对应
        List<Long> awardList = Lists.newArrayList();
        List<Long> probabilityList = Lists.newArrayList();
        for (Long key : awardProbabilitys.keySet()) {
            awardList.add(key);
            probabilityList.add(awardProbabilitys.get(key));
        }
        String day = DateUtil.format(new Date(), DateUtil.PATTERN_TYPE2);
        int level = RandomUtil.randomIndex(probabilityList);
        long step = awardList.get(level);
        //todo 限制总量 done
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        List<Long> list = actRedisDao.hIncrWithLimit(groupCode, PLAYER_HAS_AWARD_COUNT, day, step, attr.getPlayerTotalAwardCount(), false);
        long ret = list.get(0);
        if (ret <= 0) {
            final boolean rs = actRedisDao.hsetnx(groupCode, "player_award_run_out", day, attr.getPlayerTotalAwardCount() + "");
            if (rs) {
                //todo 发送如流通知奖池已用完 done
                String msgFormat = "年兽玩法消息告警:\n" +
                        "用户水晶奖池已用完\n" +
                        "业务:%s,总水晶数:%s ";
                String msg = String.format(msgFormat, attr.getBusiId(), attr.getPlayerTotalAwardCount());
                //baiduInfoFlowRobotService.asyncSendNotifyByActAttrKey(attr.getActId(), "act_notice", msg, Lists.newArrayList());
                baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Lists.newArrayList());
            }
            return -1;
        }

        allHitUsers.put(uid + "", step);

        // 奖励发放入redis队列
        saveAward(ns, attr, allHitUsers);

        JSONObject data = new JSONObject();
        data.put("amount", step);
        //用于单播
        data.put("uid", uid);
        data.put("luckest", 1);
        awardUserList.add(data);
        return 1;
    }

    /**
     * 先保存奖励到 redis list 中
     */
    private void saveAward(NianshouInfo ns, NianshouComponentAttr attr, Map<String, Long> allHitUsers) {
        // 先构造记录
        int inx = 1;
        long busiId = ns.getBusiId();
        String time = DateUtil.today();
        final String nsId = ns.getNsId();
        List<CommonAwardInfo> awardList = Lists.newArrayList();
        for (String uid : allHitUsers.keySet()) {
            long amount = allHitUsers.get(uid);
            String seq = nsId + "#" + (inx++);
            awardList.add(new CommonAwardInfo(time, Long.parseLong(uid), busiId, seq, attr.getPlayerTaskId(), attr.getPlayerPackageId(), amount));
            log.info("OPEN_BOX_HIT {} -> {} {} {}", seq, uid, amount, ns.getSeq());
        }

        // 批量入redis
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        actRedisDao.getRedisTemplate(groupCode).execute(new RedisCallback<List<Object>>() {
            @Override
            public List<Object> doInRedis(RedisConnection connection) throws DataAccessException {
                byte[] listKey = makeKey(attr, NS_PLAYER_AWARD_ISSUE).getBytes();
                byte[] zsetkey = makeKey(attr, String.format(NS_PLAYER_HIT_HISTORY, nsId)).getBytes();
                connection.openPipeline();
                for (int i = 0; i < awardList.size(); i++) {
                    CommonAwardInfo award = awardList.get(i);
                    connection.rPush(listKey, JSON.toJSONString(award).getBytes());
                    connection.zAdd(zsetkey, award.getAmount(), String.valueOf(award.getUid()).getBytes());
                }
                return connection.closePipeline();
            }
        });
    }

    /**
     * 先保存奖励到 redis list 中
     */
    private void savePlayerNormalAward(long busiId, Integer level, String nsId, NianshouComponentAttr attr, Map<Long, Long> playerAwardMap, String eventSeq) {
        // 先构造记录
        int inx = 11;
        String time = DateUtil.today();
        List<CommonAwardInfo> awardList = Lists.newArrayList();

        for (Long uid : playerAwardMap.keySet()) {
            long packageId = playerAwardMap.get(uid);
            String seq = nsId + "#" + (inx++);
            awardList.add(new CommonAwardInfo(time, uid, busiId, seq, attr.getPlayerNormalTaskId().get(level), packageId, 1));
            log.info("PLAYER_HIT_NORMAL {} -> {} {} {}", seq, uid, packageId, eventSeq);
        }

        // 批量入redis
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        actRedisDao.getRedisTemplate(groupCode).execute(new RedisCallback<List<Object>>() {
            @Override
            public List<Object> doInRedis(RedisConnection connection) throws DataAccessException {
                byte[] listKey = makeKey(attr, NS_PLAYER_AWARD_ISSUE).getBytes();
                byte[] zsetkey = makeKey(attr, String.format(NS_PLAYER_HIT_HISTORY, nsId)).getBytes();
                connection.openPipeline();
                for (int i = 0; i < awardList.size(); i++) {
                    CommonAwardInfo award = awardList.get(i);
                    connection.rPush(listKey, JSON.toJSONString(award).getBytes());
                    connection.zAdd(zsetkey, award.getPackageId(), String.valueOf(award.getUid()).getBytes());
                }
                //过期 todo 改成30天
                connection.expire(zsetkey, 24 * 60 * 60 * 30);
                return connection.closePipeline();
            }
        });
    }

    /**
     * 先保存奖励到 redis list 中
     */
    private void saveAnchorAward(long busiId, String nsId, NianshouComponentAttr attr, Map<String, Long> anchorAwardMap) {
        // 先构造记录
        int inx = 21;
        String time = DateUtil.today();
        List<CommonAwardInfo> awardList = Lists.newArrayList();

        for (String uid : anchorAwardMap.keySet()) {
            long amount = anchorAwardMap.get(uid);
            String seq = nsId + "#" + (inx++);
            awardList.add(new CommonAwardInfo(time, Long.parseLong(uid), busiId, seq, attr.getAnchorTaskId(), attr.getAnchorPackageId(), amount));
            log.info("ANCHOR FINISH MISSION {} -> {} {}", seq, uid, amount);
        }

        // 批量入redis
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        actRedisDao.getRedisTemplate(groupCode).execute(new RedisCallback<List<Object>>() {
            @Override
            public List<Object> doInRedis(RedisConnection connection) throws DataAccessException {
                byte[] listKey = makeKey(attr, NS_ANCHOR_AWARD_ISSUE).getBytes();
                byte[] zsetkey = makeKey(attr, String.format(NS_ANCHOR_HIT_HISTORY, nsId)).getBytes();
                connection.openPipeline();
                for (int i = 0; i < awardList.size(); i++) {
                    CommonAwardInfo award = awardList.get(i);
                    connection.rPush(listKey, JSON.toJSONString(award).getBytes());
                    connection.zAdd(zsetkey, award.getAmount(), String.valueOf(award.getUid()).getBytes());
                }
                connection.expire(zsetkey, 24 * 60 * 60 * 30);
                return connection.closePipeline();
            }
        });
    }

    /**
     * 发放一个奖励
     */
    private void giveOneAward(String groupCode, String listKey, String content, NianshouComponentAttr attr) {
        Clock clock = new Clock();
        try {
            CommonAwardInfo nsa = JSON.parseObject(content, CommonAwardInfo.class);
            if (nsa == null) {
                log.error("giveOneAward ignore@invalid content:{}", content);
                return;
            }

            // 超过一天的不再处理
            Date now = new Date();
            long diff = DateUtil.getSeconds(now) - nsa.getSeconds();
            if (diff > attr.getIssueRetrySeconds()) {
                log.error("giveOneAward expired@time too long:{}s, {} {}", diff, attr.getIssueRetrySeconds(), nsa.outline());
                return;
            }

            // 进行发放（注意：若有限额控制，则发抽奖发放会变得很复杂）
            String time = DateUtil.format(now);
            long packageId = nsa.getPackageId();
            BatchWelfareResult result = hdztAwardServiceClient.doWelfare(time, nsa.getBusiId(), nsa.getUid(), nsa.getTaskId(), (int) nsa.getAmount(), packageId, nsa.getSeq());
            if (result != null && result.getCode() == 0) {
                Map<Long, Long> recordIds = result.getRecordIds();
                if (MapUtils.isEmpty(recordIds) || !recordIds.containsKey(packageId)) {
                    // 可能超额等原因导致实际没有发成功，这里打日志告警结束，以免堆积
                    final boolean match = StringUtil.trim(result.getReason()).contains("异步发放中");
                    if (match) {
                        log.info("giveOneAward async ok@{} doWelfare:{}", nsa.outline(), result);
                    } else {
                        log.error("giveOneAward wrong@recordIds no packageId:{}, {} doWelfare:{}", packageId, nsa.outline(), result);
                    }
                } else {
                    log.info("giveOneAward ok@{} doWelfare:{}", nsa.outline(), result);
                }
                return;
            }
            clock.tag();

            /*
             发放失败的重新入redis，若还失败，只能打日志告警
             注意 nsa 前面不应有更新，防止入队的内容被错误变化了
             */
            nsa.setUtime(time);
            Long len = actRedisDao.rpush(groupCode, listKey, JSON.toJSONString(nsa));
            log.error("giveOneAward fail@key:{}, content:{}, doWelfare:{}, rightPush ok len:{} {}", listKey, content, result, len, clock.tag());
        } catch (Throwable t) {
            log.error("giveOneAward rightPush exception@key:{}, content:{}, err:{} {}", listKey, content, t.getMessage(), clock.tag(), t);
        }
    }

    public JSONObject fightingNianshou(long actId, long cmptUseIndex, long sid, long ssid) {
        JSONObject data = new JSONObject();
        NianshouComponentAttr attr = getComponentAttr(actId, cmptUseIndex);
        String dismissKey = makeKey(attr, NS_DISMISS_TIME);
        String groupCode = redisConfigManager.getGroupCode(actId);
        String hget = actRedisDao.hget(groupCode, dismissKey, sid + "_" + ssid);
        data.put("result", -1);
        data.put("msg", "该频道无年兽");
        //不存在正在打的年兽
        if (hget == null) {
            return data;
        }
        String[] split = hget.split("\\|");
        long nowSecond = DateUtil.getSeconds();
        long startTime = Long.parseLong(split[0]);
        //当前时间小于过期时间说明正在打
        if (nowSecond < startTime + attr.getPreTime() + attr.getFightTime()) {
            data.put("result", 0);
            data.put("msg", "ok");
            data.put("data", split[1]);
        }
        return data;

    }


    /**
     * 正在打年兽的频道 缓存2秒
     *
     * @param actId
     * @param cmpUseIndex 跟业务ID相同
     * @return
     */
    @Cached(timeToLiveMillis = 2000)
    public List<NianshouInfo> processingNs(long actId, long cmpUseIndex) throws Exception {
        String groupCode = redisConfigManager.getGroupCode(actId);
        NianshouComponentAttr attr = this.getComponentAttr(actId, cmpUseIndex);
        String channelCountKey = makeKey(attr, NS_CHANNEL_COUNT_MAP);
        Map<Object, Object> hGetAll = actRedisDao.hGetAll(groupCode, channelCountKey);

        List<String> nsIds = new ArrayList<>();
        int i = 0;
        boolean flag = true;
        String day = DateUtil.nowYyyyMMdd();
        for (Map.Entry<Object, Object> entry : hGetAll.entrySet()) {
            long count = Convert.toLong(entry.getValue());
            if (count == 0) {
                continue;
            }
            String boxListKey = makeKey(attr, String.format(NS_BOX_LIST, entry.getKey()));
            String lindex = actRedisDao.lindex(groupCode, boxListKey, 0);
            if (lindex == null) {
                continue;
            }
            String[] split = lindex.split("\\|");
            long seconds = DateUtil.getSeconds();
            long expireSecond = Convert.toLong(split[0]) + attr.getPreTime() + attr.getFightTime();
            //已经打完的不展示,在展示结果的不展示
            if (expireSecond <= seconds) {
                continue;
            }
            if (i < 20) {
                nsIds.add(split[1]);
            }
            if (flag) {
                day = getDayByNsId(split[1]);
                flag = false;
            }
            i++;
        }
        List<NianshouInfo> nianshouInfos = new ArrayList<>();
        if (CollectionUtils.isEmpty(nsIds)) {
            return nianshouInfos;
        }
        String detailKey = makeKey(attr, String.format(NS_BOX_DETAIL, day));
        List<Object> objects = actRedisDao.hmGet(groupCode, detailKey, MyListUtils.toObjectList(nsIds));
        for (Object object : objects) {
            nianshouInfos.add(JSON.parseObject(String.valueOf(object), NianshouInfo.class));
        }
        return nianshouInfos;
    }


}
