package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserEnterTemplateEvent;
import com.yy.gameecology.activity.bean.actlayer.LayerMemberItem;
import com.yy.gameecology.activity.bean.event.AppBannerEvent2;
import com.yy.gameecology.activity.bean.event.AppBannerMp4Config;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.client.yrpc.DanmakuActivityClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.CurrencyStatService;
import com.yy.gameecology.activity.service.DelaySvcSDKServiceV2;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.layer.LayerSupport;
import com.yy.gameecology.activity.worker.timer.TimerSupport;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.consts.*;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5124AwardRecord;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5124Coupling;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.AwardResult;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.CampCompeteComponentAttr;
import com.yy.gameecology.hdzj.element.component.service.CampCompeteService;
import com.yy.gameecology.hdzj.utils.BusinessUtils;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztaward.AwardPackageItemInfo;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.UpdateRankingRequest;
import com.yy.thrift.hdztranking.ZsetMember;
import com.yy.thrift.hdztranking.ZsetQueryCondition;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("5124")
public class CampCompeteComponent extends BaseActComponent<CampCompeteComponentAttr> implements LayerSupport {

    private static final int AWARD_BATCH_SIZE = 50;

    private static final String AWARD_NOTICE_KEY = "camp_award_notice";

    private static final String RANK_KEY = "hdzt_ranking:%d:%d:_:%s:_|%d";

    private static final String INFOFLOW_TITLE_TEMPLATE = "【阵营玩法】（%s）----（%s轮次播报）";

    private static final String INFOFLOW_MSG_TEMPLATE = """
            本轮获胜阵营：${campName}
            本轮次发放礼物：${awardCount}个
            本轮TOP5 CP瓜分情况：
            <#list cpList as cp>
                ->第${cp_index + 1}名 ${cp.userNick} & ${cp.anchorNick} 荣耀值：${cp.score} 瓜分${cp.awardCount}礼物
            </#list>
            本轮头像框发放总数：${spareCount}
            活动期间累计发放礼物个数：${awardedCount}
            """;
    private static final freemarker.template.Template TEMPLATE;

    static {
        try {
            TEMPLATE = new freemarker.template.Template("infoflow_5124", INFOFLOW_MSG_TEMPLATE, null);
        } catch (IOException e) {
            throw new IllegalStateException(e);
        }
    }


    @Autowired
    private TimerSupport timerSupport;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private CampCompeteService campCompeteService;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private CurrencyStatService currencyStatService;

    @Autowired
    private DanmakuActivityClient danmakuActivityClient;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Autowired
    private CommonDataDao commonDataDao;

    @Autowired
    private DelaySvcSDKServiceV2 delaySvcSDKServiceV2;

    @Override
    public Long getComponentId() {
        return ComponentId.CAMP_COMPETE;
    }

    /**
     * 主持小时榜单变化 -> 确定主持阵营 -> 保存用户-主持cp对信息 -> 更新阵营总榜、阵营贡献榜
     * @param event
     * @param attr
     */
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void onRankingScoreChanged(RankingScoreChanged event, CampCompeteComponentAttr attr) {
        if (event.getRankId() != attr.getRankId() || event.getPhaseId() != attr.getPhaseId()) {
            return;
        }

        log.info("onRankingScoreChanged with actors:{}", event.getActors());
        long anchorRoleId = 0;
        String anchorMember = null, userMember = null;
        for (var entry : event.getActors().entrySet()) {
            if (attr.getUserRoleId() == entry.getKey()) {
                userMember = entry.getValue();
                continue;
            }

            if (attr.getAnchorRoleIds().contains(entry.getKey())) {
                anchorRoleId = entry.getKey();
                anchorMember = entry.getValue();
            }
        }

        if (!StringUtils.isNumeric(anchorMember) || !StringUtils.isNumeric(userMember)) {
            return;
        }

        Date time;
        try {
            time = DateUtils.parseDate(event.getOccurTime(), DateUtil.DEFAULT_PATTERN);
        } catch (ParseException e) {
            log.warn("onRankingScoreChanged parse occur time fail:", e);
            time = commonService.getNow(attr.getActId());
        }

        String timeCode = TimeKeyHelper.getTimeCode(event.getTimeKey(), time);
        long anchorUid = Long.parseLong(anchorMember);
        Integer camp = campCompeteService.groupingCamp(attr, anchorUid, anchorRoleId, timeCode);
        for (int i = 0; i < 20; i++) {
            if (camp != null) {
                break;
            }

            camp = campCompeteService.groupingCamp(attr, anchorUid, anchorRoleId, timeCode);
        }

        if (camp == null) {
            log.error("onRankingScoreChanged grouping camp fail anchorUid:{} anchorRoleId:{} timeCode:{}", anchorUid, anchorRoleId, timeCode);
            throw new RuntimeException("anchor grouping camp fail");
        }

        long userUid = Long.parseLong(userMember);
        campCompeteService.saveCoupling(attr.getActId(), timeCode, userUid, anchorUid, camp);

        // 更新榜单
        CampCompeteComponentAttr.CampConfig campConfig = attr.fetchCampConfigByCamp(camp);
        UpdateRankingRequest request = new UpdateRankingRequest();
        request.setActId(attr.getActId());
        request.setBusiId(attr.getBusiId());
        request.setSeq("camp:" + event.getSeq());
        request.setActors(Map.of(attr.getCampRoleId(), String.valueOf(camp), anchorRoleId, anchorMember, attr.getUserRoleId(), userMember));
        request.setItemId(campConfig.getRankItem());
        request.setCount(event.getItemCount());
        request.setScore(event.getItemScore());
        request.setTimestamp(System.currentTimeMillis());

        boolean updated = hdztRankingThriftClient.updateRankingWithRetry(request, 5);
        if (!updated) {
            throw new RuntimeException("update rank fail");
        }

        log.info("onRankingScoreChanged done with anchorUid:{} userUid:{}", anchorUid, userUid);

        // 发单播
        JSONObject json = new JSONObject(4);
        json.put("camp", camp);
        json.put("score", event.getItemScore());
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            String jsonData = json.toJSONString();
            Map<Long, UserBaseInfo> userInfoMap = commonService.batchGetUserInfos(List.of(anchorUid, userUid), false);
            GameecologyActivity.BannerBroadcast.Builder builder = GameecologyActivity.BannerBroadcast.newBuilder()
                    .setActId(attr.getActId())
                    .setBannerId(5124001)
                    .setBannerType(0)
                    .setAnchorUid(anchorUid)
                    .setUserUid(userUid)
                    .setJsonData(jsonData);

            UserBaseInfo anchorInfo = userInfoMap.get(anchorUid);
            if (anchorInfo != null) {
                builder.setAnchorNick(anchorInfo.getNick());
                builder.setAnchorLogo(anchorInfo.getHdLogo());
            }

            UserBaseInfo userInfo = userInfoMap.get(userUid);
            if (userInfo != null) {
                builder.setUserNick(userInfo.getNick());
                builder.setUserLogo(userInfo.getHdLogo());
            }

            GameecologyActivity.GameEcologyMsg bannerMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                    .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                    .setBannerBroadcast(builder.build())
                    .build();
            svcSDKService.unicastUid(anchorUid, bannerMsg);
            if (userUid != anchorUid) {
                svcSDKService.unicastUid(userUid, bannerMsg);
            }
            log.info("onRankingScoreChanged anchorUid:{} userUid:{} jsonData:{}", anchorUid, userUid, jsonData);
        });
    }

    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = true)
    public void onCampRankPhaseTimeEnd(PhaseTimeEnd event, CampCompeteComponentAttr attr) {
        if (event.getRankId() != attr.getCampRankId() || event.getPhaseId() != attr.getPhaseId()) {
            return;
        }

        Date time;
        try {
            time = DateUtils.parseDate(event.getEndTime(), DateUtil.DEFAULT_PATTERN);
        } catch (ParseException e) {
            log.warn("onRankingScoreChanged parse occur time fail:", e);
            time = commonService.getNow(attr.getActId());
        }

        final String timeCode = TimeKeyHelper.getTimeCode(event.getTimeKey(), time);

        List<Rank> ranks = hdztRankingThriftClient.queryRanking(attr.getActId(), attr.getCampRankId(), attr.getPhaseId(), timeCode, 2, Collections.emptyMap());
        if (CollectionUtils.isEmpty(ranks)) {
            log.warn("onCampRankPhaseTimeEnd camp rank is empty");
            return;
        }

        final String campMember = ranks.getFirst().getMember();
        final int camp = Integer.parseInt(campMember);

        CampCompeteComponentAttr.CampConfig campConfig = attr.fetchCampConfigByCamp(camp);
        if (campConfig == null) {
            log.error("onCampRankPhaseTimeEnd unexpected campConfig cannot be found:{}", camp);
            throw new IllegalStateException("cannot fetch camp config");
        }

        // 瓜分奖励
        long windCampScore = Math.max(0, ranks.getFirst().getScore());
        int date = Integer.parseInt(DateFormatUtils.format(time, DateUtil.PATTERN_TYPE2));
        CampCompeteComponentAttr.CampAwardConfig awardConfig = attr.fetchCampAwardConfig(date);
        if (awardConfig == null) {
            log.error("onCampRankPhaseTimeEnd cannot get awardConfig:{}", date);
            throw new RuntimeException("cannot get awardConfig");
        }

        long totalCampScore = ranks.stream().map(Rank::getScore).reduce(0L, Long::sum);

        final long campScore;
        if (awardConfig.getBaseMode() == 1) {
            campScore = totalCampScore;
        } else {
            campScore = windCampScore;
        }

        long awardCount = (campScore * awardConfig.getCent() / 10000);
        awardCount = Math.min(awardCount, awardConfig.getAwardCeiling());

        // 只瓜分前x人
        final long awardCampScore;
        if (awardConfig.getMainAwardMembers() > 0) {
            List<ZsetMember> members = queryCampContributeRank(attr.getActId(), campConfig.getRankId(), attr.getPhaseId(), timeCode, 0, awardConfig.getMainAwardMembers());
            awardCampScore = members.stream().map(ZsetMember::getScore).map(Double::longValue).reduce(0L, Long::sum);
        } else {
            awardCampScore = windCampScore;
        }

        final long ignored = campCompeteService.saveCampAwardRecord(attr, timeCode, camp, awardCampScore, awardCount, awardConfig);
//        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> sendInfoflowMsg(attr, timeCode, campConfig, awardConfig, awardCampScore, ac));
    }

    @HdzjEventHandler(value = UserEnterTemplateEvent.class, canRetry = true)
    public void onUserEnterTemplate(UserEnterTemplateEvent event, CampCompeteComponentAttr attr) {
        final long uid = event.getUid();
        if (CollectionUtils.isNotEmpty(attr.getNoticeHostIds())) {
            if (!attr.getNoticeHostIds().contains(event.getHostName().getHostId())) {
                log.warn("onUserEnterTemplate skip unsatisfied uid:{} hostId:{}", uid, event.getHostName().getHostId());
                return;
            }
        }

        String value = commonDataDao.hashValueGet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), AWARD_NOTICE_KEY, String.valueOf(uid));
        if (StringUtils.isEmpty(value)) {
            return;
        }

        int rs = commonDataDao.hashValueDel(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), AWARD_NOTICE_KEY, String.valueOf(uid));
        if (rs > 0) {
            doSendCampAwardDelayNotice(attr.getActId(), uid, value);
        }
    }

    @NeedRecycle(author = "liqingyang", notRecycle = true)
    @Scheduled(initialDelay = 2000, fixedDelay = 10000)
    public void awardCampMembers() {
        Set<Long> actIds = getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }

        for (long actId : actIds) {
            CampCompeteComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                return;
            }

            doAwardCampMembers(attr);
        }
    }

    @NeedRecycle(author = "liqingyang", notRecycle = true)
    @Scheduled(cron = "31 3 11 * * ?")
    public void sendFireworkStat() {
        Set<Long> actIds = getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }

        for (long actId : actIds) {
            CampCompeteComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                return;
            }

            timerSupport.work("sendFireworkStat", 30, () -> currencyStatService.sendInfoflowMsg(actId, attr.getBusiId(), 10));
        }
    }

    private void doAwardCampMembers(CampCompeteComponentAttr attr) {
        List<Cmpt5124AwardRecord> records = campCompeteService.getGrantingAwardRecords(attr.getActId());
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        for (Cmpt5124AwardRecord record : records) {
            boolean updated = campCompeteService.tryGrantAward(record.getId());
            if (!updated) {
                continue;
            }

            try {
                final int camp = record.getCamp();
                CampCompeteComponentAttr.CampConfig campConfig = attr.fetchCampConfigByCamp(camp);
                if (campConfig == null) {
                    log.error("doAwardCampMembers unexpected campConfig cannot be found:{}", camp);
                    throw new IllegalStateException("cannot fetch camp config");
                }

                final String timeCode = record.getDateCode();
                long remain = record.getAwardCount();
                int from = 0;
                final int infoflowSize = record.getAwardMembers() <= 0 ? 1 : record.getAwardMembers();
                // 用来记录聚合的奖励
                Map<Long, Map<Long, AwardResult>> awardMap = new HashMap<>(1024);
                List<DoAwardResult> awardResults = new ArrayList<>(6);
                while (true) {
                    List<ZsetMember> members = queryCampContributeRank(attr.getActId(), campConfig.getRankId(), attr.getPhaseId(), timeCode, from, AWARD_BATCH_SIZE);
                    if (CollectionUtils.isEmpty(members)) {
                        break;
                    }

                    for (int i = 0; i < members.size(); i++) {
                        ZsetMember member = members.get(i);
                        int rank = from * AWARD_BATCH_SIZE + i;
                        final boolean awardMain;
                        if (record.getAwardMembers() > 0) {
                            awardMain = rank < record.getAwardMembers();
                        } else {
                            awardMain = true;
                        }
                        var result = awardCpMember(attr, record, remain, member, awardMain);
                        remain -= result.getAwardCount();

                        // 发第一名广播弹窗
                        if (rank == 0) {
                            threadPoolManager.get(Const.IMPORTANT_POOL).execute(() -> sendCampChampionBroadcast(attr, timeCode, camp, result.getUserUid(), result.getAnchorUid()));
                        }

                        // 聚合奖励
                        awardMap.compute(result.getUserUid(), (k, v) -> {
                           if (v == null) {
                               v = new LinkedHashMap<>(3);
                           }

                           for (var awardResult : result.getAwardList()) {
                               v.compute(awardResult.getPackageId(), (kk, vv) -> {
                                  if (vv == null) {
                                      return new AwardResult(awardResult);
                                  }

                                  vv.setNum(vv.getNum() + awardResult.getNum());
                                  return vv;
                               });
                           }

                           return v;
                        });

                        if (result.getUserUid() != result.getAnchorUid()) {
                            awardMap.compute(result.getAnchorUid(), (k, v) -> {
                                if (v == null) {
                                    v = new LinkedHashMap<>(3);
                                }

                                for (var awardResult : result.getAwardList()) {
                                    v.compute(awardResult.getPackageId(), (kk, vv) -> {
                                        if (vv == null) {
                                            return new AwardResult(awardResult);
                                        }

                                        vv.setNum(vv.getNum() + awardResult.getNum());
                                        return vv;
                                    });
                                }

                                return v;
                            });
                        }

                        if (rank < infoflowSize) {
                            awardResults.add(result);
                        }
                    }

                    from += AWARD_BATCH_SIZE;
                }

                // 发送结算的如流消息
                if (CollectionUtils.isNotEmpty(awardResults)) {
                    threadPoolManager.get(Const.IMPORTANT_POOL).execute(() -> sendInfoflowMsg(attr, timeCode, camp, record.getSparePackageId(), record.getAwardCount(), awardResults));
                }

                // 发送聚合后的瓜分奖励奖单播
                if (MapUtils.isNotEmpty(awardMap)) {
                    threadPoolManager.get(Const.IMPORTANT_POOL).execute(() -> sendCampAwardNotices(attr, timeCode, camp, awardMap));
                }

                // 更新发放记录的状态
                campCompeteService.grantedAwardRecord(record.getId());
            } catch (Exception e) {
                log.error("doAwardCampMembers fail with id:{}", record.getId(), e);
                campCompeteService.restoreGrantingState(record.getId());
            }

        }
    }

    private DoAwardResult awardCpMember(CampCompeteComponentAttr attr, Cmpt5124AwardRecord record, long remain, ZsetMember member, boolean awardMain) {
        Map<Long, Integer> packageIds = new HashMap<>(2);
        if (record.getCommonPackageId() > 0) {
            packageIds.put(record.getCommonPackageId(), 1);
        }

        long singleAwardCount = 0;
        final int camp = record.getCamp();
        final String timeCode = record.getDateCode(), cpMember = member.getMember();
        Assert.isTrue(StringUtils.contains(cpMember, StringUtil.VERTICAL_BAR), "cp member must contains |");

        final long cpScore = (long) member.getScore();
        if (awardMain) {
            final long awardCount = record.getAwardCount(), campScore = record.getCampScore();
            long cpAwardCount = (awardCount * cpScore) / campScore;
            if (cpAwardCount < 2 && remain >= 2) {
                cpAwardCount = 2;
            }

            if (cpAwardCount > remain) {
                log.warn("awardCpMember unexpected timeCode:{} cpMember:{} cpScore:{} campScore:{} awardCount:{} remain:{}", timeCode, cpMember, cpScore, campScore, awardCount, remain);
                cpAwardCount = remain;
            }

            singleAwardCount = cpAwardCount >> 1;

            if (singleAwardCount > 0) {
                packageIds.put(record.getPackageId(), (int) singleAwardCount);
            } else {
                packageIds.put(record.getSparePackageId(), 1);
            }
        }

        Assert.notEmpty(packageIds, "awardCpMember packageIds cannot be empty");

        List<AwardResult> awardList = toAwardResults(attr, packageIds);

        String[] members = StringUtils.split(cpMember, StringUtil.VERTICAL_BAR);
        long userUid = Long.parseLong(members[0]), anchorUid = Long.parseLong(members[1]);

        WelfareViewExt welfareViewExt = new WelfareViewExt();
        welfareViewExt.setCamp(camp);
        welfareViewExt.setAwardList(awardList);
        final String viewExt = JSON.toJSONString(welfareViewExt);

        Map<String, String> extData = Map.of(HdztAwardServiceClient.CP_UID, String.valueOf(anchorUid), HdztAwardServiceClient.VIEW_EXT, viewExt);
        final String seq = String.format("camp_award:%s:%s", timeCode, cpMember);

        BatchWelfareResult result = hdztAwardServiceClient.doBatchWelfare(DateUtil.getNowYyyyMMddHHmmss(), attr.getBusiId(), userUid, attr.getTAwardTskId(), Map.of(attr.getTAwardTskId(), packageIds), seq + ":1", extData);
        if (result == null || result.getCode() != 0) {
            throw new RuntimeException("welfare exception");
        }

        if (anchorUid != userUid) {
            extData = Map.of(HdztAwardServiceClient.CP_UID, String.valueOf(userUid), HdztAwardServiceClient.VIEW_EXT, viewExt);
            result = hdztAwardServiceClient.doBatchWelfare(DateUtil.getNowYyyyMMddHHmmss(), attr.getBusiId(), anchorUid, attr.getTAwardTskId(), Map.of(attr.getTAwardTskId(), packageIds), seq + ":2", extData);
            if (result == null || result.getCode() != 0) {
                throw new RuntimeException("welfare exception");
            }
        }

        log.info("doAwardCampMembers success with userUid:{} anchorUid:{} packageIds:{}", userUid, anchorUid, packageIds);
        return new DoAwardResult(userUid, anchorUid, cpScore, singleAwardCount << 1, awardList);
    }

    /**
     *
     * @param actId
     * @param rankId
     * @param phaseId
     * @param timeCode
     * @param from     start index from 0 included
     * @param size     fetch size
     * @return
     */
    private List<ZsetMember> queryCampContributeRank(long actId, long rankId, long phaseId, String timeCode, int from, int size) {
        ZsetQueryCondition condition = new ZsetQueryCondition();
        condition.setKey(String.format(RANK_KEY, actId, rankId, timeCode, phaseId));
        condition.setWay(1);
        condition.setFromName(from);
        condition.setTo(from + size - 1);

        return hdztRankingThriftClient.queryZsetRawData(actId, condition, Collections.emptyMap());
    }

    private List<AwardResult> toAwardResults(CampCompeteComponentAttr attr, Map<Long, Integer> packageIds) {
        if (MapUtils.isEmpty(packageIds)) {
            return Collections.emptyList();
        }

        var awardTasks = hdztAwardServiceClient.queryAwardTasks(attr.getTAwardTskId());
        if (awardTasks == null) {
            awardTasks = Collections.emptyMap();
        }

        List<AwardResult> results = new ArrayList<>(packageIds.size());

        for (var entry : packageIds.entrySet()) {
            long packageId = entry.getKey();
            var modelInfo = awardTasks.get(packageId);
            if (modelInfo == null) {
                continue;
            }

            var item = new AwardResult();
            item.setPackageId(packageId);
            item.setAwardName(modelInfo.getPackageName());
            item.setAwardIcon(modelInfo.getPackageImage());
            item.setUnit(modelInfo.getUnit());

            long count = 0;
            var items = modelInfo.getAwardPackageItemInfos();
            if (items != null) {
                count = items.stream().map(AwardPackageItemInfo::getGiftNum).mapToLong(Long::valueOf).sum();
            }
            item.setNum((long) entry.getValue() * count);

            results.add(item);
        }

        return results;
    }

    private void sendCampChampionBroadcast(CampCompeteComponentAttr attr, String timeCode, int camp, long userUid, long anchorUid) {
        JSONObject json = new JSONObject(3);
        Date now;
        try {
            now = DateUtils.parseDate(timeCode, DateUtil.PATTERN_TYPE7, DateUtil.PATTERN_TYPE2);
        } catch (ParseException e) {
            now = commonService.getNow(attr.getActId());
            log.error("sendCampChampionBroadcast parse timeCode fail timeCode:{}", timeCode, e);
        }
        boolean newSkin = attr.getSkinTime() != null && now.after(attr.getSkinTime());
        json.put("newSkin", newSkin);
        json.put("camp", camp);

        String jsonData = json.toJSONString();
        Template template = BusinessUtils.getTemplateByBusiId((int) attr.getBusiId());
        Map<Long, UserBaseInfo> userInfoMap = commonService.batchGetUserInfos(List.of(anchorUid, userUid), false);
        GameecologyActivity.BannerBroadcast.Builder builder = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(attr.getActId())
                .setBannerId(5124002)
                .setBannerType(0)
                .setAnchorUid(anchorUid)
                .setUserUid(userUid)
                .setJsonData(jsonData);

        UserBaseInfo userInfo = userInfoMap.get(userUid);
        if (userInfo != null) {
            builder.setUserNick(userInfo.getNick());
            builder.setUserLogo(userInfo.getHdLogo());
        }

        UserBaseInfo anchorInfo = userInfoMap.get(anchorUid);
        if (anchorInfo != null) {
            builder.setAnchorNick(anchorInfo.getNick());
            builder.setAnchorLogo(anchorInfo.getHdLogo());
        }

        GameecologyActivity.GameEcologyMsg bannerMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(builder.build())
                .build();
        if (attr.isExcludeDanmaku()) {
            List<ChannelInfo> danmakuChannel = danmakuActivityClient.queryAllChannelInfo();
            Set<String> exclude = danmakuChannel.stream().map(channel -> channel.getSid() + StringUtil.UNDERSCORE + channel.getSsid()).collect(Collectors.toSet());
            svcSDKService.broadcastTemplateExclude(template, bannerMsg, exclude);
        } else {
            svcSDKService.broadcastTemplate(template, bannerMsg);
        }
        log.info("sendCampChampionBroadcast camp:{} anchorUid:{} userUid:{} jsonData:{}", camp, anchorUid, userUid, jsonData);

        // 发移动端特效
        int date = Integer.parseInt(timeCode.substring(0, 8));
        CampCompeteComponentAttr.CampAppEffectConfig effectConfig = attr.fetchCampEffectConfig(date, camp);
        if (effectConfig == null) {
            return;
        }

        AppBannerMp4Config mp4Config = new AppBannerMp4Config();
        mp4Config.setUrl(effectConfig.getMp4Url());
        mp4Config.setLevel(effectConfig.getMp4Level());
        if (MapUtils.isNotEmpty(attr.getMp4LayerExtKeyValues())) {
            List<Map<String, String>> layerExtKeyValues = new ArrayList<>(attr.getMp4LayerExtKeyValues().size());
            for (var entry : attr.getMp4LayerExtKeyValues().entrySet()) {
                String key = entry.getKey(), text = entry.getValue();
                if (StringUtils.isNotBlank(text)) {
                    text = text.replace("{userNick}", "{" + userUid + ":n}");
                    text = text.replace("{anchorNick}", "{" + anchorUid + ":n}");
                    if (userInfo != null) {
                        text = text.replace("{userAvatar}", userInfo.getHdLogo());
                    }
                    if (anchorInfo != null) {
                        text = text.replace("{anchorAvatar}", anchorInfo.getHdLogo());
                    }
                    text = text.replace("{camp}", camp == 1 ? "福气" : "运气");
                }

                layerExtKeyValues.add(Map.of(key, text));
            }

            mp4Config.setLayerExtKeyValues(layerExtKeyValues);
        }

        String appBannerSeq = makeKey(attr, "seq:appBanner:" + timeCode);
        int business = BusinessUtils.getAppBroBizByBusiId((int) attr.getBusiId());
        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), appBannerSeq, business, FstAppBroadcastType.ALL_TEMPLATE, 0, 0, StringUtils.EMPTY, Collections.emptyList());
        appBannerEvent.setMp4Config(mp4Config);
        appBannerEvent.setContentType(5);
        appBannerEvent.setAppId(commonService.getTurnoverAppId(Convert.toInt(attr.getBusiId())));
        appBannerEvent.setUidList(List.of(userUid, anchorUid));
        if (attr.isExcludeDanmaku()) {
            kafkaService.sendAppBannerKafkaExcludeDanmuku(appBannerEvent);
        } else {
            kafkaService.sendAppBannerKafka(appBannerEvent);
        }
        log.info("sendCampChampionBroadcast app camp:{} anchorUid:{} userUid:{}", camp, anchorUid, userUid);
    }

    private void sendCampAwardNotices(CampCompeteComponentAttr attr, String timeCode, int camp, Map<Long, Map<Long, AwardResult>> awardMap) {
        if (MapUtils.isEmpty(awardMap)) {
            return;
        }

        for (var entry : awardMap.entrySet()) {
            long uid = entry.getKey();
            UserCurrentChannel channel = commonService.getUserCurrentChannel(uid);
            String noticeValue = buildAwardNoticeValue(timeCode, camp, entry.getValue());
            if (channel != null) {
                doSendCampAwardNotice(attr.getActId(), uid, noticeValue);
                continue;
            }

            commonDataDao.hashValueSet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), AWARD_NOTICE_KEY, String.valueOf(uid), noticeValue);
        }
    }

    private String buildAwardNoticeValue(String timeCode, int camp, Map<Long, AwardResult> packageIds) {
        var awardList = packageIds.values();
        JSONObject noticeJson = new JSONObject(5);
        noticeJson.put("timeCode", timeCode);
        noticeJson.put("camp", camp);
        noticeJson.put("awardList", awardList);
        return noticeJson.toJSONString();
    }

    private GameecologyActivity.GameEcologyMsg buildAwardNoticeMsg(long actId, String noticeValue) {
        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(actId)
                .setNoticeType(PBCommonNoticeType.CAMP_AWARD_NOTICE)
                .setNoticeValue(noticeValue);

        return GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();
    }

    private void doSendCampAwardNotice(long actId, long uid, String noticeValue) {
        GameecologyActivity.GameEcologyMsg msg = buildAwardNoticeMsg(actId, noticeValue);

        svcSDKService.unicastUid(uid, msg);
        log.info("doSendCampAwardNotice with uid:{} noticeValue:{}", uid, msg.getCommonNoticeResponse().getNoticeValue());
    }

    private void doSendCampAwardDelayNotice(long actId, long uid, String noticeValue) {
        GameecologyActivity.GameEcologyMsg msg = buildAwardNoticeMsg(actId, noticeValue);
        delaySvcSDKServiceV2.unicastUid(uid, msg, 5);
        log.info("doSendCampAwardDelayNotice with uid:{} noticeValue:{}", uid, msg.getCommonNoticeResponse().getNoticeValue());
    }

    private void sendInfoflowMsg(CampCompeteComponentAttr attr, String timeCode, int camp, long sparePackageId, long awardCount, List<DoAwardResult> awardResults) {
        String business = switch ((int) attr.getBusiId()) {
            case 500 -> "交友";
            case 810 -> "聊天室";
            default -> "未知";
        };
        String title = String.format(INFOFLOW_TITLE_TEMPLATE, business, timeCode);

        Set<Long> uids = new HashSet<>(awardResults.size() * 2);
        for (DoAwardResult awardResult : awardResults) {
            uids.add(awardResult.getUserUid());
            uids.add(awardResult.getAnchorUid());
        }

        Map<Long, UserBaseInfo> userInfoMap = commonService.batchGetUserInfos(List.copyOf(uids), false);
        InfoflowData data = new InfoflowData();
        data.setAwardCount(awardCount);
        data.setCampName(camp == 1 ? "红色阵营" : "蓝色阵营");
        data.setAwardedCount(campCompeteService.queryConsumedAwardCount(attr));
        long spareCount = 0;
        List<AwardResultDetail> cpList = new ArrayList<>(awardResults.size());
        for (int i = 0; i < awardResults.size(); i++) {
            DoAwardResult awardResult = awardResults.get(i);
            AwardResultDetail item = new AwardResultDetail();
            item.setRank(i + 1);
            item.setAwardCount(awardResult.getAwardCount());
            item.setScore(awardResult.getScore());
            var userInfo = userInfoMap.get(awardResult.getUserUid());
            if (userInfo != null) {
                item.setUserNick(userInfo.getNick());
            }

            userInfo = userInfoMap.get(awardResult.getAnchorUid());
            if (userInfo != null) {
                item.setAnchorNick(userInfo.getNick());
            }
            cpList.add(item);

            spareCount += awardResult.getAwardList().stream().filter(it -> it.getPackageId() == sparePackageId).map(AwardResult::getNum).findFirst().orElse(0L) * 2;
        }
        data.setCpList(cpList);
        data.setSpareCount(spareCount);

        String message;
        try {
            message = FreeMarkerTemplateUtils.processTemplateIntoString(TEMPLATE, data);
        } catch (Exception e) {
            log.error("processTemplate exception:", e);
            return;
        }

        String msg = buildActRuliuMsg(attr.getActId(), false, title, message);
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_ACT_TURNOVER, msg, Collections.emptyList());
    }

    @GetMapping("campInfo")
    public Response<CampInfo> queryCampInfo(@RequestParam("actId") long actId, @RequestParam("cmptIndex") int cmptIndex, @RequestParam(name = "timeCode", required = false) String timeCode) {
        CampCompeteComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        if (StringUtils.isEmpty(timeCode)) {
            Date now = commonService.getNow(actId);
            timeCode = TimeKeyHelper.getTimeCode(attr.getTimeKey(), now);
        }
        CampInfo data = new CampInfo();
        data.setTimeCode(timeCode);
        Pair<Long, Long> campScores = queryCampScores(attr, timeCode);
        data.setCamp1Score(campScores.getLeft());
        data.setCamp2Score(campScores.getRight());

        final long remainAwardCount = campCompeteService.queryRemainAwardCount(attr);
        data.setRemainAwardCount(remainAwardCount);

        Cmpt5124AwardRecord awardRecord = campCompeteService.queryAwardRecord(actId, timeCode);
        if (awardRecord != null) {
            data.setAwardCount(awardRecord.getAwardCount());
            data.setWinCamp(awardRecord.getCamp());

            return Response.success(data);
        }

        int date;
        if (timeCode.length() >= 8) {
            date = Integer.parseInt(timeCode.substring(0, 8));
        } else {
            date = Integer.parseInt(DateFormatUtils.format(commonService.getNow(actId), DateUtil.PATTERN_TYPE2));
        }

        CampCompeteComponentAttr.CampAwardConfig awardConfig = attr.fetchCampAwardConfig(date);
        if (awardConfig == null) {
            data.setAwardCount(0);
            return Response.success(data);
        }

        final long campScore;
        if (awardConfig.getBaseMode() == 1) {
            campScore = campScores.getLeft() + campScores.getRight();
        } else {
            campScore = Math.max(campScores.getLeft(), campScores.getRight());
        }
        long awardCount = (campScore * awardConfig.getCent() / 10000);
        if (awardCount > 0) {
            awardCount = Math.min(awardConfig.getAwardCeiling(), awardCount);
        }

        if (awardCount > 0) {
            awardCount = Math.min(remainAwardCount, awardCount);
        }

        data.setAwardCount(awardCount);

        return Response.success(data);
    }

    @GetMapping("myCouplings")
    public Response<List<CouplingInfo>> queryMyCouplings(@RequestParam("actId") long actId,
                                                         @RequestParam("cmptIndex") int cmptIndex,
                                                         @RequestParam("timeCode") String timeCode) {
        CampCompeteComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "login is required");
        }

        List<Cmpt5124Coupling> couplings = campCompeteService.queryUserCouplings(actId, timeCode, uid);
        if (CollectionUtils.isEmpty(couplings)) {
            UserBaseInfo userInfo = commonService.getUserInfo(uid, false);
            CouplingInfo item = new CouplingInfo();
            item.setUserUid(uid);
            if (userInfo != null) {
                item.setUserNick(userInfo.getNick());
                item.setUserAvatar(userInfo.getHdLogo());
            }
            return Response.success(Collections.singletonList(item));
        }

        Set<Long> uids = new HashSet<>(couplings.size() * 2);
        List<String> camp1Members = new ArrayList<>(couplings.size()), camp2Members = new ArrayList<>(couplings.size());
        for (Cmpt5124Coupling coupling : couplings) {
            long userUid = coupling.getUserUid(), anchorUid = coupling.getAnchorUid();
            uids.add(userUid);
            uids.add(anchorUid);
            if (coupling.getCamp() == 1) {
                camp1Members.add(userUid + StringUtil.VERTICAL_BAR + anchorUid);
            } else {
                camp2Members.add(userUid + StringUtil.VERTICAL_BAR + anchorUid);
            }
        }

        Map<Long, UserBaseInfo> userInfoMap = commonService.batchGetUserInfos(List.copyOf(uids), false);
        Map<String, Rank> cpRanks = new HashMap<>(couplings.size());
        if (CollectionUtils.isNotEmpty(camp1Members) && attr.fetchCampConfigByCamp(1) != null) {
            Map<String, Rank> map = hdztRankingThriftClient.queryPointedMembersRanking(actId, attr.fetchCampConfigByCamp(1).getRankId(), attr.getPhaseId(), timeCode, camp1Members, Collections.emptyMap());
            cpRanks.putAll(map);
        }

        if (CollectionUtils.isNotEmpty(camp2Members) && attr.fetchCampConfigByCamp(2) != null) {
            Map<String, Rank> map = hdztRankingThriftClient.queryPointedMembersRanking(actId, attr.fetchCampConfigByCamp(2).getRankId(), attr.getPhaseId(), timeCode, camp2Members, Collections.emptyMap());
            cpRanks.putAll(map);
        }

        List<CouplingInfo> data = new ArrayList<>(couplings.size());
        for (Cmpt5124Coupling coupling : couplings) {
            CouplingInfo item = new CouplingInfo();
            long userUid = coupling.getUserUid(), anchorUid = coupling.getAnchorUid();
            item.setUserUid(userUid);
            item.setAnchorUid(anchorUid);
            item.setCamp(coupling.getCamp());
            UserBaseInfo userInfo = userInfoMap.get(userUid);
            if (userInfo != null) {
                item.setUserNick(userInfo.getNick());
                item.setUserAvatar(userInfo.getHdLogo());
            }

            userInfo = userInfoMap.get(anchorUid);
            if (userInfo != null) {
                item.setAnchorNick(userInfo.getNick());
                item.setAnchorAvatar(userInfo.getHdLogo());
            }

            String cpMember = userUid + StringUtil.VERTICAL_BAR + anchorUid;
            Rank rank = cpRanks.get(cpMember);
            if (rank != null && rank.getScore() > 0) {
                item.setScore(rank.getScore());
                item.setRank(rank.getRank());
            }


            data.add(item);
        }

        data.sort(Comparator.comparingLong(CouplingInfo::getScore).reversed());

        return Response.success(data);
    }

    @GetMapping("sendCurrencyStat")
    public Response<?> sendCurrencyStat(@RequestParam("actId") long actId, @RequestParam("cmptIndex") int cmptIndex) {
        CampCompeteComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        currencyStatService.sendInfoflowMsg(actId, attr.getBusiId(), 10);

        return Response.ok();
    }

    @Override
    public long getActId() {
        return ComponentId.CAMP_COMPETE;
    }

    @Override
    public Map<String, Object> buildItemMemberExtInfo(long actId, LayerMemberItem layerMemberItem, Map<String, Object> ext) {
        if (!LayerItemTypeKey.CHANNEL.equals(layerMemberItem.getItemType())) {
            return ext;
        }

        CampCompeteComponentAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return ext;
        }

        Date now = commonService.getNow(actId);
        String dateStr = TimeKeyHelper.getTimeCode(attr.getTimeKey(), now);

        if (ext == null) {
            ext = new HashMap<>(6);
        }

        Pair<Long, Long> campScores = queryCampScores(attr, dateStr);

        ext.put("camp1Score", campScores.getLeft());
        ext.put("camp2Score", campScores.getRight());
        boolean newSkin = attr.getSkinTime() != null && now.after(attr.getSkinTime());
        ext.put("newSkin", newSkin);

        return ext;
    }

    public Pair<Long, Long> queryCampScores(CampCompeteComponentAttr attr, String timeCode) {
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(attr.getActId(), attr.getCampRankId(), attr.getPhaseId(), timeCode, 2, Collections.emptyMap());
        long camp1Score = 0, camp2Score = 0;
        if (ranks != null) {
            camp1Score = ranks.stream().filter(rank -> "1".equals(rank.getMember())).map(Rank::getScore).findFirst().orElse(0L);
            camp2Score = ranks.stream().filter(rank -> "2".equals(rank.getMember())).map(Rank::getScore).findFirst().orElse(0L);
        }

        return Pair.of(camp1Score, camp2Score);
    }

    @Getter
    @Setter
    public static class CampInfo {

        protected String timeCode;

        protected long camp1Score;

        protected long camp2Score;

        protected long awardCount;

        protected long remainAwardCount;

        protected int winCamp;
    }

    @Getter
    @Setter
    public static class CouplingInfo {
        protected int camp;

        protected long userUid;

        protected String userNick;

        protected String userAvatar;

        protected long anchorUid;

        protected String anchorNick;

        protected String anchorAvatar;

        protected int rank = -1;

        protected long score = 0;
    }

    @Getter
    @Setter
    public static class WelfareViewExt {
        protected int camp;

        protected List<AwardResult> awardList;
    }

    @Getter
    @Setter
    public static class DoAwardResult {
        protected long userUid;

        protected long anchorUid;

        protected long score;

        protected long awardCount;

        protected List<AwardResult> awardList;

        public DoAwardResult() {
        }

        public DoAwardResult(long userUid, long anchorUid, long score, long awardCount, List<AwardResult> awardList) {
            this.userUid = userUid;
            this.anchorUid = anchorUid;
            this.score = score;
            this.awardCount = awardCount;
            this.awardList = awardList;
        }
    }

    @Getter
    @Setter
    public static class AwardResultDetail {
        protected int rank;

        protected String userNick = "神豪";

        protected String anchorNick = "主持";

        protected long score;

        protected long awardCount;
    }

    @Getter
    @Setter
    public static class InfoflowData {
        protected String campName;

        protected long awardCount;

        protected List<AwardResultDetail> cpList;

        protected long spareCount;

        protected long awardedCount;
    }
}
