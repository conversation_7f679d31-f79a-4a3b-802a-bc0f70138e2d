package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.RankInfo;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.CommonPBOperateRequest;
import com.yy.gameecology.hdzj.bean.CommonPBOperateResp;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.JyEnterChannelPopupComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.UserAchievementAttr;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.Rank;
import io.opentelemetry.api.trace.Span;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@RequestMapping("/5060")
@RestController
@Slf4j
@Component
public class JyEnterChannelPopupComponent extends BaseActComponent<JyEnterChannelPopupComponentAttr> {

    private static final String UNICAST_SID_KEY = "unicastSid";

    private static final String DEDUPLICATE_KEY = "deduplicate_%d";

    @Override
    public Long getComponentId() {
        return ComponentId.ENTER_CHANNEL_POPUP;
    }

    @Override
    public CommonPBOperateResp commonOperatePbRequest(CommonPBOperateRequest request) {
        return super.commonOperatePbRequest(request);
    }

    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = false)
    public void handlePhaseTimeEnd(PhaseTimeEnd phaseTimeEnd, JyEnterChannelPopupComponentAttr attr) {
        //将符合条件的sid保存到redis中
        if (attr.getRankId() != phaseTimeEnd.getRankId() || attr.getPhaseId() != phaseTimeEnd.getPhaseId()) {
            return;
        }

        List<Rank> ranks = hdztRankingThriftClient.queryRanking(attr.getActId(), attr.getRankId(), attr.getPhaseId(),
                StringUtils.EMPTY, attr.getTopN(), Collections.emptyMap());

        if (CollectionUtils.isEmpty(ranks)) {
            return;
        }

        String[] members = ranks.stream().map(Rank::getMember).toArray(String[]::new);
        String key = makeKey(attr, UNICAST_SID_KEY);
        String groupCode = getRedisGroupCode(attr.getActId());
        Long rs = actRedisDao.getRedisTemplate(groupCode).opsForSet().add(key, members);
        log.info("handlePhaseTimeEnd success with members:{}, rs:{}", members, rs);
    }

    private boolean isNeedExecute(JyEnterChannelPopupComponentAttr attr, long busiId) {
        if (attr.getBusiId() != busiId) {
            return false;
        }

        Date now = commonService.getNow(attr.getActId());
        Date startDate = DateUtil.getDate(attr.getStartDate(), DateUtil.DEFAULT_PATTERN);
        Date finallyDate = DateUtil.getDate(attr.getFinallyDate(), DateUtil.DEFAULT_PATTERN);
        return startDate.getTime() <= now.getTime() && finallyDate.getTime() > now.getTime();
    }

    public void handleUserEnterChannel(long uid, long sid, long ssid, final long busiId, long template) {
        getActivityIds().stream().map(this::getAllComponentAttrs).flatMap(Collection::stream)
                .filter(attr -> this.isNeedExecute(attr, busiId))
                .forEach(attr -> this.doHandleUserEnterChannel(attr, uid, sid));

    }

    private void doHandleUserEnterChannel(JyEnterChannelPopupComponentAttr attr, long uid, long sid) {
        //判断是否是需要播的频道
        String key = makeKey(attr, UNICAST_SID_KEY);
        String groupCode = getRedisGroupCode(attr.getActId());
        boolean exist = actRedisDao.getRedisTemplate(groupCode).opsForSet().isMember(key, String.valueOf(sid));
        if (!exist) {
            log.info("doHandleUserEnterChannel sid not exist:{}", sid);
            return;
        }

        //限制
        String limitKey = makeKey(attr, String.format(DEDUPLICATE_KEY, sid));
        long rs = actRedisDao.getRedisTemplate(groupCode).opsForHash().increment(limitKey, String.valueOf(uid), 1);
        if (rs > attr.getLimitCount()) {
            log.info("doHandleUserEnterChannel limit not match sid:{}, uid:{}, rs:{}", sid, uid, rs);
            return;
        }

        //执行单播
        GetRankReq getRankReq = new GetRankReq();
        getRankReq.setActId(attr.getActId());
        getRankReq.setRankId(attr.getRankId());
        getRankReq.setPhaseId(attr.getPhaseId());
        getRankReq.setRankCount((long) attr.getTopN());
        getRankReq.setShowType(1);
        getRankReq.setExtQueryList(Collections.emptyList());
        getRankReq.setShowZeroItem(true);
        getRankReq.setDateStr(StringUtils.EMPTY);
        RankInfo rankInfo = hdztRankService.getRankInfo(getRankReq);

        List list = rankInfo.getList();
        if (list.size() != attr.getTopN()) {
            log.info("doHandleUserEnterChannel getRankInfo rank list size not satisfy:{}", list.size());
            return;
        }

        GameecologyActivity.Act202103_InHouseTemplateEventRsp.Builder tips
                = GameecologyActivity.Act202103_InHouseTemplateEventRsp.newBuilder()
                .setActId(attr.getActId())
                .setCode(0)
                .setMessage("success")
                .setIsShow(1)
                .setTitle("guildHonor")
                .setExtjson(JSON.toJSONString(list));
        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.kAct202103_InHouseTemplateEventRsp_VALUE)
                .setAct202103InHouseTemplateEventRsp(tips).build();

        GameecologyActivity.BannerBroadcast.Builder bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(attr.getActId())
                .setBannerId(5060)
                .setAnchorLogo("")
                .setAnchorNick("")
                .setJsonData(JSON.toJSONString(list));
        GameecologyActivity.GameEcologyMsg msg_v2 = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();

        svcSDKService.unicastUid(uid, msg_v2);
        svcSDKService.unicastUid(uid, msg);
        log.info("doHandleUserEnterChannel unicast success with uid:{}, size:{}", uid, list.size());
    }

    @RequestMapping("/testBro")
    public Response testBroBestCp(long actId, long cmptInx, long uid) {
        if (SysEvHelper.isDeploy()) {
            return Response.success(null);
        }
        JyEnterChannelPopupComponentAttr attr = getUniqueComponentAttr(actId);
        GetRankReq getRankReq = new GetRankReq();
        getRankReq.setActId(attr.getActId());
        getRankReq.setRankId(attr.getRankId());
        getRankReq.setPhaseId(attr.getPhaseId());
        getRankReq.setRankCount((long) attr.getTopN());
        getRankReq.setShowType(1);
        getRankReq.setExtQueryList(Collections.emptyList());
        getRankReq.setShowZeroItem(true);
        getRankReq.setDateStr(StringUtils.EMPTY);
        RankInfo rankInfo = hdztRankService.getRankInfo(getRankReq);
        List list = rankInfo.getList();

        GameecologyActivity.BannerBroadcast.Builder bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(attr.getActId())
                .setBannerId(5060)
                .setAnchorLogo("")
                .setAnchorNick("")
                .setJsonData(JSON.toJSONString(list));
        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();

        svcSDKService.unicastUid(uid, msg);
        String traceId = Span.current().getSpanContext().getTraceId();
        return Response.success(traceId);
    }
}
