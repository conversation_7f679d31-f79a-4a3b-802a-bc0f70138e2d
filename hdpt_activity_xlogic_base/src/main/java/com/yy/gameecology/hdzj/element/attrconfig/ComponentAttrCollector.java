package com.yy.gameecology.hdzj.element.attrconfig;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.db.model.gameecology.HdzjComponentAttrDefine;
import com.yy.gameecology.common.db.model.gameecology.HdzjComponentAttrDic;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.element.ActComponent;
import com.yy.gameecology.hdzj.element.component.HonorHallRankComponent;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.time.Duration;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 组件属性收集器
 *
 * <AUTHOR>
 * @date 2022/2/25 15:44
 **/
@Component
public class ComponentAttrCollector {
    private static Logger logger = LoggerFactory.getLogger(ComponentAttrCollector.class);
    @Autowired
    private ActComponent[] components;
    @Autowired
    private GameecologyDao gameecologyDao;

    private static final Set<Class<?>> BASIC_TYPE_SET = Sets.newHashSet(Byte.class, Short.class, Integer.class, Long.class
            , Float.class, Double.class, Character.class, Boolean.class, String.class, Date.class, LocalTime.class, Duration.class);

    private static final Map<String, String> BASIC_TYPE_MAP = Maps.newHashMap();

    private static final Map<String, String> PROP_TYPE_MAP = Maps.newHashMap();

    private static final Map<Long, ActComponent> ACT_COMPONENT_MAP = Maps.newHashMap();

    @Autowired
    private DropDownSource[] dropDownSourceList;

    @PostConstruct
    @NeedRecycle(author = "wangdonghong", notRecycle = true)
    public void init() {
        initBasicTypeMap();
        initPropTypeMap();
        initActComponentMap();
    }

    private void initBasicTypeMap() {
        BASIC_TYPE_MAP.put(int.class.getName(), Integer.class.getName());
        BASIC_TYPE_MAP.put(byte.class.getName(), Byte.class.getName());
        BASIC_TYPE_MAP.put(short.class.getName(), Short.class.getName());
        BASIC_TYPE_MAP.put(long.class.getName(), Long.class.getName());
        BASIC_TYPE_MAP.put(float.class.getName(), Float.class.getName());
        BASIC_TYPE_MAP.put(double.class.getName(), Double.class.getName());
        BASIC_TYPE_MAP.put(char.class.getName(), Character.class.getName());
        BASIC_TYPE_MAP.put(boolean.class.getName(), Boolean.class.getName());
    }

    private void initPropTypeMap() {
        PROP_TYPE_MAP.put(Number.class.getName(), PropType.NUMBER);
        PROP_TYPE_MAP.put(Boolean.class.getName(), PropType.SWITCH);
        PROP_TYPE_MAP.put(Date.class.getName(), PropType.DATE_TIME);
        PROP_TYPE_MAP.put(LocalTime.class.getName(), PropType.TIME_PICKER);
        PROP_TYPE_MAP.put(String.class.getName(), PropType.TEXT);
        PROP_TYPE_MAP.put(Byte.class.getName(), PropType.NUMBER);
        PROP_TYPE_MAP.put(Short.class.getName(), PropType.NUMBER);
        PROP_TYPE_MAP.put(Integer.class.getName(), PropType.NUMBER);
        PROP_TYPE_MAP.put(Long.class.getName(), PropType.NUMBER);
        PROP_TYPE_MAP.put(Float.class.getName(), PropType.NUMBER);
        PROP_TYPE_MAP.put(Double.class.getName(), PropType.NUMBER);
        PROP_TYPE_MAP.put(Character.class.getName(), PropType.TEXT);
        PROP_TYPE_MAP.put(Map.class.getName(), PropType.TABLE);
        PROP_TYPE_MAP.put(Duration.class.getName(), PropType.DURATION);
    }

    private void initActComponentMap() {
        if (components == null) {
            return;
        }
        for (ActComponent component : components) {
            ACT_COMPONENT_MAP.put(component.getComponentId(), component);
        }
    }

    public void synAll() {
        for (Map.Entry<Long, ActComponent> entry : ACT_COMPONENT_MAP.entrySet()) {
            if (entry.getKey() == null) {
                continue;
            }
            synComponentAttrDefine(entry.getKey());
        }
    }

    public void synDropDownSource() {
        for (DropDownSource dropDownSource : dropDownSourceList) {
            HdzjComponentAttrDic dic = new HdzjComponentAttrDic();
            dic.setDropDownSource(dropDownSource.getClass().getName());
            dic.setDropDownList(JSON.toJSONString(dropDownSource.listDropDown()));
            gameecologyDao.insertOrUpdate(HdzjComponentAttrDic.class, dic, HdzjComponentAttrDic.TABLE_NAME);
        }
    }

    /**
     * 同步组件的属性定义到数据库
     **/
    public void synComponentAttrDefine(Long componentId) {
        if (componentId == null || ACT_COMPONENT_MAP.get(componentId) == null) {
            throw new SuperException("not found ActComponent for Id =" + componentId, SuperException.E_PARAM_ILLEGAL);
        }
        HdzjComponentAttrDefine query = new HdzjComponentAttrDefine();
        query.setCmptId(componentId.intValue());

        ActComponent actComponent = ACT_COMPONENT_MAP.get(componentId);

        List<CommonConfigProp> props = collect(actComponent);
        if (CollectionUtils.isEmpty(props)) {
            // logger.info("{} not found prop", componentId);
            // gameecologyDao.delete(HdzjComponentAttrDefine.class, query);
            return;
        }

        List<HdzjComponentAttrDefine> attrDefines = gameecologyDao.select(HdzjComponentAttrDefine.class, query, "");
        Map<String, HdzjComponentAttrDefine> oldDefineMap = attrDefines.stream()
                .collect(Collectors.toMap(define -> define.getCmptId() + define.getPropName() + define.getPid(), define -> define));

        List<HdzjComponentAttrDefine> newDefines = new ArrayList<>();
        int order = 0;
        for (CommonConfigProp prop : props) {
            HdzjComponentAttrDefine newDefine = toDefine(prop, order);
            newDefines.add(newDefine);
            order++;
        }
        for (HdzjComponentAttrDefine newDefine : newDefines) {
            String key = newDefine.getCmptId() + newDefine.getPropName() + newDefine.getPid();

            HdzjComponentAttrDefine oldDefine = oldDefineMap.get(key);
            compare(newDefine, oldDefine);
        }
        int deleteSize = gameecologyDao.delete(HdzjComponentAttrDefine.class, query);
        logger.info("synComponentAttrDefine componentId={},deleteSize={}", componentId, deleteSize);
        int[] insertResult = gameecologyDao.batchInsert(HdzjComponentAttrDefine.class, newDefines);
        logger.info("synComponentAttrDefine componentId={},insertResult={}", componentId, JSON.toJSONString(insertResult));
    }

    private static final List<String> IGNORE_FIELDS = Arrays.asList("createTime", "updateTime", "level", "showOrder");

    private void compare(HdzjComponentAttrDefine newDefine, HdzjComponentAttrDefine oldDefine) {
        if (oldDefine == null) {
            // logger.info("oldDefine is not found,newDefine={}", JSON.toJSONString(newDefine));
            return;
        }

        List<Field> fields = listAllField(HdzjComponentAttrDefine.class);
        for (Field field : fields) {
            if (!HdzjComponentAttrDefine.TABLE_FIELDS.containsKey(field.getName())) {
                continue;
            }
            if (IGNORE_FIELDS.contains(field.getName())) {
                continue;
            }

            try {
                field.setAccessible(true);
                Object oldValue = field.get(oldDefine);
                Object newValue = field.get(newDefine);

                if (oldValue == null && Objects.equals(newValue, "")) {
                    continue;
                }

                if (Objects.equals(oldValue, "null") && Objects.equals(newValue, "")) {
                    continue;
                }

                if ("placeholder".equals(field.getName())) {
                    continue;
                }

                if ("remark".equals(field.getName())) {
                    continue;
                }

                // oldValue="java.lang.Integer",newValue="Number"
                if ("java.lang.Integer".equals(oldValue) && "Number".equals(newValue)) {
                    continue;
                }

                if ("java.lang.Long".equals(oldValue) && "Number".equals(newValue)) {
                    continue;
                }

                if ("propType".equals(field.getName()) && "java.lang.String".equals(oldValue) && "Text".equals(newValue)) {
                    continue;
                }
                if ("propType".equals(field.getName()) && "java.lang.Number".equals(oldValue) && "Number".equals(newValue)) {
                    continue;
                }
                if ("propType".equals(field.getName()) && "java.util.Map".equals(oldValue) && "Table".equals(newValue)) {
                    continue;
                }

                // field:extJson,propName=busiId
                if ("extJson".equals(field.getName()) && "busiId".equals(newDefine.getPropName())) {
                    continue;
                }

                if (!Objects.equals(oldValue, newValue)) {
                    logger.info("componentId={},field:{},propName={} is not equal,oldValue={},newValue={}"
                            , newDefine.getCmptId(), field.getName(), newDefine.getPropName(), JSON.toJSONString(oldValue), JSON.toJSONString(newValue));
                }
            } catch (Exception ex) {

            }
        }
    }

    private HdzjComponentAttrDefine toDefine(CommonConfigProp prop, int order) {
        HdzjComponentAttrDefine newDefine = new HdzjComponentAttrDefine();
        newDefine.setLevel(prop.getLevel());
        newDefine.setShowOrder(order);
        newDefine.setDefaultValue(prop.getDefaultValue() == null ? "" : prop.getDefaultValue());
        newDefine.setCmptId(prop.getComponentId().intValue());
        newDefine.setUpdateTime(new Date());
        newDefine.setPropType(prop.getPropType());
        newDefine.setCreateTime(new Date());
        newDefine.setPid(prop.getPid());
        newDefine.setExtJson(prop.getExtJson() == null ? "" : prop.getExtJson());
        if (StringUtils.equals(prop.getLabelText(), prop.getRemark())) {
            newDefine.setRemark("");
        } else {
            newDefine.setRemark(prop.getRemark());
        }
        newDefine.setPlaceholder(prop.getPlaceholder() == null ? "" : prop.getPlaceholder());
        newDefine.setLabelText(prop.getLabelText());
        newDefine.setValueType(prop.getValueType() == null ? String.class.getName() : prop.getValueType());
        newDefine.setPropName(prop.getPropName() == null ? PropType.TEXT : prop.getPropName());

        return newDefine;
    }

    public List<CommonConfigProp> collect(ActComponent component) {
        Long componentId = component.getComponentId();

        return toProps(componentId, component.getMyAttrClass());
    }

    private List<CommonConfigProp> toProps(Long componentId, Class<?> attrClass) {
        List<CommonConfigProp> props = new ArrayList<>();

        // 获取组件Attribute所有字段
        List<Field> fields = listAllField(attrClass);

        fields.forEach(field -> {
            // 没有@ComponentAttrField注解,不收集
            ComponentAttrField attrField = field.getAnnotation(ComponentAttrField.class);
            if (attrField == null) {
                // logger.info("componentId={},fieldName={} has no ComponentAttrField", componentId, field.getName());
                return;
            }

            try {
                CommonConfigProp prop = new CommonConfigProp();
                prop.setComponentId(componentId);
                prop.setCreateTime(new Date());
                prop.setPropName(field.getName());
                prop.setUpdateTime(new Date());
                prop.setPid("#");

                Class<?> fieldType = field.getType();
                if (isBasicType(fieldType)) {
                    buildBasicTypeAttrField(field, attrClass, fieldType, prop, attrField.propType());
                } else if (isCollectionType(fieldType)) {
                    List<CommonConfigProp> subProps = buildCollectionTypeAttrField(attrField, prop);
                    if (CollectionUtils.isNotEmpty(subProps)) {
                        props.addAll(subProps);
                    }

                    if (Set.class.isAssignableFrom(fieldType)) {
                        prop.setValueType(Set.class.getName());
                    }
                    // 设置默认值
                    field.setAccessible(true);
                    Object defaultValue = field.get(attrClass.newInstance());
                    if (defaultValue != null) {
                        List<String> list = JSON.parseArray(JSON.toJSONString(defaultValue), String.class);
                        prop.setDefaultValue(list.stream().collect(Collectors.joining(",")));
                    }

                    // 默认值
                    if (StringUtils.isNotEmpty(attrField.defaultValue())) {
                        prop.setDefaultValue(attrField.defaultValue());
                    }
                } else if (Map.class.isAssignableFrom(fieldType)) {
                    List<CommonConfigProp> subProps = buildMapTypeAttrField(attrField, prop);
                    if (CollectionUtils.isNotEmpty(subProps)) {
                        props.addAll(subProps);
                    }

                    // 设置默认值
                    field.setAccessible(true);
                    Object defaultValue = field.get(attrClass.newInstance());
                    if (defaultValue != null && !((Map) defaultValue).isEmpty()) {
                        JSONArray array = new JSONArray();
                        for (Object object : ((Map) defaultValue).entrySet()) {
                            Map.Entry entry = (Map.Entry) object;
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("key1", entry.getKey());
                            jsonObject.put("value", entry.getValue());
                            array.add(jsonObject);
                        }

                        prop.setDefaultValue(JSON.toJSONString(array));
                    }
                }

                // 设置注解展示的信息
                setShowInfo(prop, attrField, fieldType);

                props.add(prop);
            } catch (Exception ex) {
                if (SysEvHelper.isDeploy()) {
                    return;
                }
                logger.error("toProp error,class={},field={}", attrClass.getName(), field.getName(), ex);
            }
        });

        return props;
    }

    private void setShowInfo(CommonConfigProp prop, ComponentAttrField attrField, Class<?> fieldType) {
        prop.setRemark(attrField.remark());
        prop.setLabelText(attrField.labelText());
        if (StringUtils.isNotEmpty(attrField.placeholder())) {
            prop.setPlaceholder(attrField.placeholder());
        } else {
            prop.setPlaceholder(attrField.labelText());
        }

        if (Date.class.equals(fieldType)) {
            JSONObject extJson = new JSONObject();
            extJson.put("format", attrField.dateShowFormat());
            extJson.put("value-format", attrField.dateValueFormat());
            prop.setExtJson(extJson.toJSONString());
        }

        if (attrField.dropDownSourceBeanClass() != null && attrField.dropDownSourceBeanClass().length > 0) {
            try {
                prop.setPropType(PropType.DROP_DOWN);
                Class<?>[] sourceClass = attrField.dropDownSourceBeanClass();
                DropDownSource dropDownSource = (DropDownSource) sourceClass[0].newInstance();
                JSONObject extJson = new JSONObject();
                extJson.put("dropDownSource", dropDownSource.getClass().getName());
                prop.setExtJson(extJson.toJSONString());
            } catch (Exception ex) {

            }
        }
        prop.setUseDialog(attrField.useDialog());
    }

    /**
     * map类型的属性
     **/
    private List<CommonConfigProp> buildMapTypeAttrField(ComponentAttrField attrField, CommonConfigProp pProp) {
        SubField[] subFields = attrField.subFields();
        if (subFields == null || subFields.length <= 0) {
            throw new IllegalArgumentException("not config mapField,componentId=" + pProp.getComponentId() + ",fieldName=" + pProp.getPropName());
        }

        JSONObject extJson = new JSONObject();
        List<CommonConfigProp> subProps = new ArrayList<>();
        for (SubField subField : subFields) {
            extJson.put(subField.fieldName(), subField.type().getName());
            if (subField.skip()) {
                continue;
            }

            // 基础类型
            if (isBasicType(subField.type())) {
                String fieldName = subField.fieldName();
                if (Constant.MAP_LIST_VALUE.equals(fieldName)) {
                    fieldName = Constant.VALUE;
                }
                subProps.add(buildCommonConfigProp(subField.type().getName(), subField.propType(), pProp.getComponentId(), buildPid(pProp)
                        , fieldName, subField.labelText(), subField.remark()));
            } else if (isCollectionType(subField.type())) {

            } else {
                extJson.put(subField.fieldName(), Object.class);
                subProps.addAll(buildInnerClass(pProp, subField.type()));
            }

            if (Constant.MAP_LIST_VALUE.equals(subField.fieldName())) {
                extJson.put(Constant.VALUE, List.class.getName());
            }
        }
        pProp.setExtJson(extJson.toJSONString());
        pProp.setPropType(PropType.TABLE);
        pProp.setValueType(Map.class.getName());

        return subProps;
    }

    /**
     * 基本类型的属性字段
     **/
    private void buildBasicTypeAttrField(Field field, Class<?> attrClass, Class<?> fieldType, CommonConfigProp prop, String propType) {
        try {
            field.setAccessible(true);
            Object defaultValue = field.get(attrClass.getDeclaredConstructor().newInstance());
            if (defaultValue != null) {
                if (Date.class.isAssignableFrom(fieldType)) {
                    prop.setDefaultValue(DateUtil.format((Date) defaultValue));
                } else if (LocalTime.class.isAssignableFrom(fieldType)) {
                    prop.setDefaultValue(((LocalTime) defaultValue).format(DateTimeFormatter.ofPattern("HH:mm:ss")));
                } else {
                    prop.setDefaultValue(defaultValue.toString());
                }
            }
        } catch (Exception ex) {
            logger.error("buildBasicTypeAttrField error,class={},field={}", attrClass.getName(), field.getName(), ex);
        }
        prop.setValueType(BASIC_TYPE_MAP.getOrDefault(fieldType.getName(), fieldType.getName()));
        if (StringUtils.isNotEmpty(propType)) {
            prop.setPropType(propType);
        } else {
            prop.setPropType(PROP_TYPE_MAP.get(prop.getValueType()));
        }
    }

    /**
     * 列表类型的组件属性
     **/
    private List<CommonConfigProp> buildCollectionTypeAttrField(ComponentAttrField attrField, CommonConfigProp pProp) {
        SubField[] subFields = attrField.subFields();
        if (subFields == null || subFields.length <= 0) {
            throw new IllegalArgumentException("not config mapField,componentId=" + pProp.getComponentId() + ",fieldName=" + pProp.getPropName());
        }
        pProp.setValueType(List.class.getName());

        JSONObject extJson = new JSONObject();
        List<CommonConfigProp> subProps = new ArrayList<>();
        for (SubField subField : subFields) {
            extJson.put(subField.fieldName(), subField.type().getName());
            if (subField.skip()) {
                continue;
            }

            // 基础类型
            if (isBasicType(subField.type())) {
                pProp.setPropType(PropType.TEXT);

            } else if (isCollectionType(subField.type())) {
                throw new IllegalStateException("collection sub filed cannot be collection");
            } else {
                extJson.put(subField.fieldName(), Object.class);
                subProps.addAll(buildInnerClass(pProp, subField.type()));
                pProp.setPropType(PropType.TABLE);
            }
        }
        pProp.setExtJson(extJson.toJSONString());

        return subProps;
    }

    private List<CommonConfigProp> buildInnerClass(CommonConfigProp pProp, Class<?> innerClass) {
        List<CommonConfigProp> innerProps = new ArrayList<>();
        Field[] innerFields = innerClass.getDeclaredFields();
        String pid = buildPid(pProp);
        for (Field innerField : innerFields) {
            ComponentAttrField attrField = innerField.getAnnotation(ComponentAttrField.class);
            if (attrField == null) {
                continue;
            }

            CommonConfigProp prop = new CommonConfigProp();
            prop.setComponentId(pProp.getComponentId());
            prop.setPid(pid);
            prop.setCreateTime(new Date());
            prop.setUpdateTime(new Date());
            prop.setPropName(innerField.getName());

            prop.setValueType(BASIC_TYPE_MAP.getOrDefault(innerField.getType().getName(), innerField.getType().getName()));
            if (StringUtils.isNotEmpty(attrField.propType())) {
                prop.setPropType(attrField.propType());
            } else {
                prop.setPropType(PROP_TYPE_MAP.get(prop.getValueType()));
            }

            //
            if (attrField.useTextarea() == 1) {
                prop.setPropType(PropType.TEXT);
            }

            setShowInfo(prop, attrField, innerField.getType());
            innerProps.add(prop);
        }

        return innerProps;
    }

    private String buildPid(CommonConfigProp pProp) {
        return pProp.getComponentId() + "#" + pProp.getPropName();
    }

    private CommonConfigProp buildCommonConfigProp(String typeName, String propType, Long componentId, String pid, String propName, String labelText, String remark) {
        CommonConfigProp prop = new CommonConfigProp();
        if (StringUtils.isEmpty(propType)) {
            propType = PROP_TYPE_MAP.get(typeName);
        }
        prop.setPropType(propType);
        prop.setValueType(typeName);
        prop.setPropName(propName);
        prop.setPid(pid);
        prop.setComponentId(componentId);
        prop.setCreateTime(new Date());
        prop.setUpdateTime(new Date());
        if (StringUtils.isEmpty(labelText)) {
            labelText = propName;
        }
        prop.setLabelText(labelText);
        if (StringUtils.isEmpty(remark)) {
            remark = labelText;
        }
        prop.setRemark(remark);

        return prop;
    }

    /**
     * 获取所有属性
     **/
    private List<Field> listAllField(Class<?> attrClass) {
        return new ArrayList<>(Arrays.asList(attrClass.getDeclaredFields()));
    }

    private boolean isBasicType(Class<?> clz) {
        return clz.isPrimitive() || BASIC_TYPE_SET.contains(clz);
    }

    /**
     * 使用父类判断 ???
     **/
    private boolean isCollectionType(Class<?> clz) {
        return List.class.isAssignableFrom(clz) || Set.class.isAssignableFrom(clz) || clz.isArray();
    }

    @Data
    private static class CommonConfigProp {
        private Long componentId;
        private String propName;
        private String pid;
        private String labelText;
        private String propType;
        private String remark;
        private Date createTime;
        private Date updateTime;
        private String placeholder;
        private String defaultValue;
        private Integer level = 1;
        private String valueType;
        private String extJson;
        private Integer useDialog;
    }

    /**
     * 控件类型
     */
    public static class PropType {
        /**
         * <el-input-number></el-input-number>
         */
        public static final String NUMBER = "Number";
        /**
         * <el-input></el-input>
         **/
        public static final String TEXT = "Text";
        /**
         * <el-date-picker></el-date-picker>
         **/
        public static final String DATE_TIME = "DateTime";

        /**
         * <el-time-select></el-time-select>
         */
        public static final String TIME_PICKER = "TimePicker";

        /**
         * <el-select></el-select>
         * <p>
         * 对应的<el-option></el-option> 配置到哪去
         **/
        public static final String DROP_DOWN = "DropDown";
        /**
         * <el-table></el-table>
         * <p>
         * 需要配套列
         * <el-table-column></el-table-column>
         **/
        public static final String TABLE = "Table";
        /**
         * <el-switch></el-switch>
         **/
        public static final String SWITCH = "Switch";

        /**
         * <el-input type="textarea"></el-input>
         **/
        public static final String TEXTAREA = "Textarea";

        /**
         * 图片类型
         */
        public static final String IMAGE = "Image";

        public static final String DURATION = "Duration";
    }

    public static void main(String[] args) {
        ComponentAttrCollector collector = new ComponentAttrCollector();
        collector.init();
//        List<CommonConfigProp> props = collector.collect(new RankingRankAwardComponent());
//        List<CommonConfigProp> props = collector.collect(new RankCardComponent());
//        List<CommonConfigProp> props = collector.collect(new SunshineTaskComponent());

//        List<CommonConfigProp> props = collector.collect(new Rank2RankComponent());

        ActComponent component = new HonorHallRankComponent();
        List<CommonConfigProp> props = collector.collect(component);

        buildSql(props);
    }

    private static void buildSql(List<CommonConfigProp> props) {
        int order = 0;
        for (CommonConfigProp prop : props) {
            String sql = "INSERT INTO `gameecology`.`hdzj_component_attr_define`(`cmpt_id`,`prop_name`,`pid`,`label_text`,`prop_type`,`value_type`,`placeholder`,`remark`,`create_time`,`update_time`,`default_value`,`level`,`extJson`,`show_order`) VALUES (%s,'%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s');";
            sql = String.format(sql, prop.getComponentId(), prop.getPropName()
                    , prop.getPid()
                    , prop.getLabelText()
                    , prop.getPropType()
                    , prop.getValueType()
                    , prop.getPlaceholder()
                    , prop.getRemark()
                    , DateUtil.format(new Date())
                    , DateUtil.format(new Date())
                    , prop.getDefaultValue() == null ? "" : prop.getDefaultValue()
                    , prop.getLevel()
                    , prop.getExtJson() == null ? "" : prop.getExtJson()
                    , order++);

            System.out.println(sql);
        }
    }
}
