package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.hdzt.PhaseRankTag;
import com.yy.gameecology.activity.bean.hdzt.RankPhasePair;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-07-13 16:29
 **/
@Data
public class RankBuilderComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "加载贡献榜阶段配置", remark = "[主榜有阶段ID，优先级较高，适用于每个主榜阶段对应的贡献榜阶段都不一样的情况]配置了这个这个会把主榜每个成员的贡献榜加载到主榜扩展字段，有一定性能损耗。贡献榜phaseId配置：0代表用主榜phaseId;-1代表用总榜;>0用配置的数值",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "主榜Id"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "主榜阶段Id"),
                    @SubField(fieldName = Constant.VALUE, type = RankPhasePair.class, labelText = "贡献榜"),
            })
    private Map<Long, Map<Long, RankPhasePair>> loadContributeRankPhaseMap = Maps.newLinkedHashMap();


    @ComponentAttrField(labelText = "加载贡献榜配置", remark = "配置了这个这个会把主榜每个成员的贡献榜加载到主榜扩展字段，有一定性能损耗。贡献榜phaseId配置：0代表用主榜phaseId;-1代表用总榜;>0用配置的数值",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "主榜Id"),
                    @SubField(fieldName = Constant.VALUE, type = RankPhasePair.class, labelText = "贡献榜"),
            })
    private Map<Long, RankPhasePair> loadContributeRankMap= Maps.newLinkedHashMap();

    @ComponentAttrField(labelText = "贡献榜成员数量")
    private long loadContributeSize;

    @ComponentAttrField(labelText = "成员昵称设置白名单组件索引", remark = "大于0时有效，设置hdzj_component_whitelist组件名单，影响范围：主榜昵称、挂件昵称")
    private long memberNameCmptIndex = -1;

    @ComponentAttrField(labelText = "晋级赛榜tag处理", remark = "pointedMember处理，tag标签，晋级后的成员只在一个赛道" ,
            subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = PhaseRankTag.class, labelText = "榜单tag配置")
    })
    private List<PhaseRankTag>  loadPhaseRankTagConfig;

    @ComponentAttrField(labelText = "添加家族昵称", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = RankPhasePair.class), remark = "房间榜单添加家族昵称")
    private List<RankPhasePair> familyNameRanks;
}
