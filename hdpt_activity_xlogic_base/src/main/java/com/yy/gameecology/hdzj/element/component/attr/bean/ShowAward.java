package com.yy.gameecology.hdzj.element.component.attr.bean;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;

public class ShowAward {

    @ComponentAttrField(labelText = "奖励百分比")
    private double awardPercent;

    @ComponentAttrField(labelText = "奖励个数(有种情况就是，产品给的不是百分比会有无限循环小数情况，为了方便处理)")
    private long awardNum;

    public double getAwardPercent() {
        return awardPercent;
    }

    public void setAwardPercent(double awardPercent) {
        this.awardPercent = awardPercent;
    }

    public long getAwardNum() {
        return awardNum;
    }

    public void setAwardNum(long awardNum) {
        this.awardNum = awardNum;
    }
}
