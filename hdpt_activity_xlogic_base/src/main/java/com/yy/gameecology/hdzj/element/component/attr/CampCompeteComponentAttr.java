package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Getter
@Setter
public class CampCompeteComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    protected long busiId;

    @ComponentAttrField(labelText = "过滤弹幕游戏频道")
    protected boolean excludeDanmaku = true;

    @ComponentAttrField(labelText = "主持榜单ID")
    protected long rankId;

    @ComponentAttrField(labelText = "阶段ID")
    protected long phaseId;

    @ComponentAttrField(labelText = "时间分榜", dropDownSourceBeanClass = TimeKeySource.class)
    private long timeKey;

    @ComponentAttrField(labelText = "主持角色", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class), remark = "多个使用英文逗号隔开")
    protected List<Long> anchorRoleIds;

    @ComponentAttrField(labelText = "神豪角色")
    protected long userRoleId;

    @ComponentAttrField(labelText = "阵营配置", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = CampConfig.class), remark = "camp=1：福气阵营，camp=2：运气阵营")
    protected List<CampConfig> campConfigs;

    @ComponentAttrField(labelText = "阵营角色", remark = "阵营在榜单中的角色ID")
    protected long campRoleId;

    @ComponentAttrField(labelText = "阵营榜单ID", remark = "阵营荣耀值的榜单ID")
    protected long campRankId;

    @ComponentAttrField(labelText = "奖励总上限", remark = "整个活动期间奖池的总上限（礼物个数）")
    protected long awardCeiling;

    @ComponentAttrField(labelText = "奖励配置", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = CampAwardConfig.class), remark = "根据日期配置奖励")
    protected List<CampAwardConfig> awardConfigs;

    @ComponentAttrField(labelText = "奖池ID", remark = "瓜分奖励发奖的奖池ID")
    protected long tAwardTskId;

    @ComponentAttrField(labelText = "单播限定hostId", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Integer.class), remark = "进频道奖励单播限定的hostId，为空则不限定，多个使用逗号分隔开")
    protected Set<Integer> noticeHostIds;

    @ComponentAttrField(labelText = "app特效配置", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = CampAppEffectConfig.class), remark = "移动端MP4特效配置, 对应的key配置统一放在“app特效key配置”")
    protected List<CampAppEffectConfig> appEffectConfigs;

    @ComponentAttrField(labelText = "app特效key配置", remark = "mp4中的key配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "key"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "value", remark = "可以包含占位符的图片或富文本")
            })
    protected Map<String, String> mp4LayerExtKeyValues;

    @ComponentAttrField(labelText = "换肤开始时间", remark = "需要开始更换皮肤的时间")
    protected Date skinTime;

    public CampConfig fetchCampConfigByCamp(int camp) {
        return campConfigs.stream().filter(campConfig -> campConfig.getCamp() == camp).findFirst().orElseThrow();
    }

    public CampAwardConfig fetchCampAwardConfig(int date) {
        return awardConfigs.stream().filter(awardConfig -> awardConfig.getBeginDate() <= date && awardConfig.getEndDate() >= date).findFirst().orElse(null);
    }

    public CampAppEffectConfig fetchCampEffectConfig(int date, int camp) {
        if (CollectionUtils.isEmpty(appEffectConfigs)) {
            return null;
        }
        return appEffectConfigs.stream().filter(effectConfig -> effectConfig.getCamp() == camp && effectConfig.getBeginDate() <= date && effectConfig.getEndDate() >= date).findFirst().orElse(null);
    }

    @Getter
    @Setter
    public static class CampConfig {

        @ComponentAttrField(labelText = "阵营", remark = "1-福气阵营，2-运气阵营")
        protected int camp;

        @ComponentAttrField(labelText = "阵营榜单ID", remark = "对应阵营的cp榜单ID")
        protected long rankId;

        @ComponentAttrField(labelText = "阵营累榜ITEM", remark = "对应阵营累榜的ITEM_ID")
        protected String rankItem;
    }

    @Getter
    @Setter
    public static class CampAwardConfig {
        @ComponentAttrField(labelText = "开始日期", remark = "配置生效的开始日期（包含）yyyyMMdd")
        protected int beginDate;

        @ComponentAttrField(labelText = "结束日期", remark = "配置生效的结束日期（包含）yyyyMMdd")
        protected int endDate;

        @ComponentAttrField(labelText = "比例模式", remark = "奖励比例对应的阵营分模式；0-单胜利方阵营分，1-双方阵营总分")
        protected int baseMode = 0;

        @ComponentAttrField(labelText = "奖励比例", remark = "对应日期的奖励比例的分子，分母是：100")
        protected int cent;

        @ComponentAttrField(labelText = "单场上限", remark = "对应日期的单场奖励上限")
        protected long awardCeiling;

        @ComponentAttrField(labelText = "瓜分人数", remark = "阵营内的TOP N 可以瓜分奖励，-1表示全部人都能瓜分")
        protected int mainAwardMembers = -1;

        @ComponentAttrField(labelText = "主奖包ID", remark = "主奖包ID")
        protected long tAwardPkgMainId;

        @ComponentAttrField(labelText = "通用奖包ID", remark = "阵营内所有人都会发的普通奖池ID")
        protected long tAwardPkgCommonId;

        @ComponentAttrField(labelText = "备用奖包ID", remark = "奖励不足时发的备用奖包ID")
        protected long tAwardPkgSpareId;
    }

    @Getter
    @Setter
    public static class CampAppEffectConfig {
        @ComponentAttrField(labelText = "阵营", remark = "1-福气阵营，2-运气阵营")
        protected int camp;

        @ComponentAttrField(labelText = "开始日期", remark = "配置生效的开始日期（包含）yyyyMMdd")
        protected int beginDate;

        @ComponentAttrField(labelText = "结束日期", remark = "配置生效的结束日期（包含）yyyyMMdd")
        protected int endDate;

        @ComponentAttrField(labelText = "mp4特效url")
        protected String mp4Url;

        @ComponentAttrField(labelText = "优先级", remark = "特效排队显示优先级，值越小，优先级越高；目前默认全屏礼物特效的优先级为999，如果优先级低于全屏礼物特效优先级，需要该值大于999")
        protected int mp4Level;
    }
}
