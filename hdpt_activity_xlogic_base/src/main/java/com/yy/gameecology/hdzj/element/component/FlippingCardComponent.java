package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.SendGiftEvent;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.BroadCastHelpService;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.service.WhiteListService;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.consts.BroadcastType;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeActAttrConst;
import com.yy.gameecology.common.consts.PBCommonNoticeType;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.NanoIdUtils;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.FlippingCardComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztaward.AwardModelInfo;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/5076")
public class FlippingCardComponent extends BaseActComponent<FlippingCardComponentAttr> {

    private static final int FLIP_STATE = 0;

    private static final int SHOW_STATE = 1;

    private static final int BEFORE_STATE = -1;

    private static final int AFTER_STATE = -2;

    private static final String BROAD_CAR_POOL = "broadcast_current_pool:%s";

    private static final String CURRENT_POOL = "current_pool:%s";

    private static final String LOCK_NAME = "handle_lock:%s";

    private static final String LOTTERY_STATE = "lottery_state:%s";

    private static final String CHANCE_BALANCE = "chance_balance:%d";

    private static final String OCCUPY_CARD = "occupy_card:%s";

    private static final String CARD_POOL = "card_pool:%s";

    private static final String CHANCE_UID_SET = "chance_uids";

    private static final String CHANCE_COUNT = "chance_count";

//    private static final String CARD_POOL_COUNT = "card_pool_count:%s";

    private static final String FLIP_UID_SET = "flip_uids:%s";

    private static final String FLIP_COUNT = "flip_count:%s";

    private static final String OCCUPY_SCRIPT_TEXT = """
            local lockKey = KEYS[1]
            local cardKey = KEYS[2]
            local balanceKey = KEYS[3]
            local occupyKey = KEYS[4]
            			
            local cardVal = ARGV[1]
            local cost = tonumber(ARGV[2])
            local cardId = ARGV[3]
            local member = ARGV[4]
            local expiredSec = tonumber(ARGV[5] or 0)
            			
            local exists = tonumber(redis.call('EXISTS', lockKey) or 0)
            if exists == 1 then
            	return 432
            end
            			
            local balance = tonumber(redis.call('GET', balanceKey) or 0)
            if balance < cost then
            	return 433
            end
            			
            local set = tonumber(redis.call('HSETNX', occupyKey, cardId, member) or 0)
            if set == 0 then
            	return 431
            end
            			
            redis.call('DECRBY', balanceKey, cost)
            redis.call('HSET', cardKey, cardId, cardVal)
                        
            if expiredSec > 0 then
            	redis.call('EXPIRE', occupyKey, expiredSec)
            end
            			
            return 0
            """;

    private static final RedisScript<Long> OCCUPY_SCRIPT = new DefaultRedisScript<>(OCCUPY_SCRIPT_TEXT, Long.class);

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private WhiteListService whiteListService;

    @Override
    public Long getComponentId() {
        return ComponentId.FLIPPING_CARD;
    }


    @HdzjEventHandler(value = SendGiftEvent.class, canRetry = true)
    public void onSendGiftEvent(SendGiftEvent event, FlippingCardComponentAttr attr) {
        Map<String, FlippingCardComponentAttr.PresentConfig> configMap = attr.getPresentConfigMap();
        if (!configMap.containsKey(event.getGiftId())) {
            return;
        }

        FlippingCardComponentAttr.PresentConfig config = configMap.get(event.getGiftId());
        if (event.getGiftNum() < config.getTriggerCount()) {
            return;
        }

        final long actId = attr.getActId();
        final String groupCode = getRedisGroupCode(actId);
        final String seqKey = makeKey(attr, event.getSeq());
        Boolean exist = actRedisDao.getRedisTemplate(groupCode).hasKey(seqKey);
        if (exist != null && exist) {
            return;
        }

        int chanceCount = (int) (event.getGiftNum() / config.getTriggerCount() * config.getChanceCount());

        final long member = event.getSendUid();
        String balanceKey = makeKey(attr, String.format(CHANCE_BALANCE, member));
        List<Long> result = actRedisDao.incrValueWithSeq(groupCode, seqKey, balanceKey, chanceCount, 24 * 60 * 60);
        if (result == null || result.size() < Const.TOW || result.get(Const.ONE) < 0) {
            log.error("onSendGiftEvent incr member chance count exception member: {} chance: {} result: {}", member, chanceCount, result);
            throw new RuntimeException();
        }

        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> addChanceUid(attr, member, chanceCount, groupCode));

        // send unicast
        JSONObject noticeJson = new JSONObject();
        noticeJson.put("chanceCount", chanceCount);
        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(actId)
                .setNoticeType(PBCommonNoticeType.FLIP_CHANCE_OBTAIN)
                .setNoticeValue(noticeJson.toJSONString());

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();

        RetryTool.withRetryCheck(actId, makeKey(attr, String.format("grant_card_uni_%d", member)), 30 * 24 * 60 * 60, () -> svcSDKService.unicastUid(member, msg));
    }

    private void addChanceUid(FlippingCardComponentAttr attr, long uid, int count, String groupCode) {
        String dateStr = DateFormatUtils.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
        String totalKey = makeKey(attr, CHANCE_UID_SET);
        String dailyKey = totalKey + StringUtil.UNDERSCORE + dateStr;

        String totalCountKey = makeKey(attr, CHANCE_COUNT);
        String dailyCountKey = totalCountKey + StringUtil.UNDERSCORE + dateStr;

        actRedisDao.sadd(groupCode, totalKey, String.valueOf(uid));
        actRedisDao.sadd(groupCode, dailyKey, String.valueOf(uid));

        actRedisDao.incrValue(groupCode, totalCountKey, count);
        actRedisDao.incrValue(groupCode, dailyCountKey, count);
    }

    public CardPool createCardPool(FlippingCardComponentAttr attr, PoolType type, String groupCode, long expiredSec) {
        final long taskId, honorTaskId;
        final int honorCount;
        final List<FlippingCardComponentAttr.Card> cards;
        final String poolId = NanoIdUtils.id(25);
        CardPool pool = new CardPool();
        pool.setPoolId(poolId);
        pool.setState(FLIP_STATE);
        pool.setExpired(System.currentTimeMillis() + attr.getFlipCountdown());

        if (type == PoolType.primary) {
            taskId = attr.getPrimaryTaskId();
            honorTaskId = attr.getPrimaryHonorTaskId();
            honorCount = attr.getPrimaryHonorCount();
            cards = attr.getPrimaryCards();
        } else {
            taskId = attr.getSeniorTaskId();
            honorTaskId = attr.getSeniorHonorTaskId();
            honorCount = attr.getSeniorHonorCount();
            cards = attr.getSeniorCards();
        }

        List<Integer> honorCardIds = getHonorCardIds(cards, honorCount);
        Map<String, String> map = new HashMap<>(cards.size());
        for (FlippingCardComponentAttr.Card card : cards) {
            WatchwordCard watchwordCard = new WatchwordCard();
            watchwordCard.setCardId(card.getCardId());
            watchwordCard.setType(type);
            watchwordCard.setText(card.getText());
            watchwordCard.setState(CardState.flippable);
            watchwordCard.setTaskId(honorCardIds.contains(card.getCardId()) ? honorTaskId : taskId);
            map.put(String.valueOf(card.getCardId()), JSON.toJSONString(watchwordCard));
        }

        String poolKey = makeKey(attr, String.format(CARD_POOL, poolId));
        actRedisDao.hmset(groupCode, poolKey, map);
        actRedisDao.expire(groupCode, poolKey, expiredSec + 1);
        log.info("createCardPool set pool with id: {}", poolId);

        return pool;
    }

    public void createAndSetCurPool(FlippingCardComponentAttr attr, PoolType type, String groupCode) {
        long expiredSec = (attr.getFlipCountdown() + attr.getShowCountdown()) / 1000 + 10 * 60;
        CardPool cardPool = createCardPool(attr, type, groupCode, expiredSec);
        String currPoolKey = makeKey(attr, String.format(CURRENT_POOL, type.name()));
        actRedisDao.set(groupCode, currPoolKey, JSON.toJSONString(cardPool));
        log.info("createAndSetCurPool create new card pool type:{}, poolId:{}", type, cardPool.poolId);

//        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> incrCardPoolCount(attr, type, groupCode));
    }

//    private void incrCardPoolCount(FlippingCardComponentAttr attr, PoolType type, String groupCode) {
//        String dateStr = DateFormatUtils.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
//        String totalKey = makeKey(attr, String.format(CARD_POOL_COUNT, type.name()));
//        String dailyKey = totalKey + StringUtil.UNDERSCORE + dateStr;
//
//        actRedisDao.incrValue(groupCode, totalKey, 1);
//        actRedisDao.incrValue(groupCode, dailyKey, 1);
//    }

    @NeedRecycle(author = "liqingyang", notRecycle = true)
    @Scheduled(initialDelay = 500, fixedDelay = 1000)
    public void handlePrimaryCountdown() {
        getActivityIds().stream().map(this::getAllComponentAttrs)
                .flatMap(Collection::stream)
                .forEach(attr -> {
                    // 判断活动时间
                    if (actInfoService.inActShowTime(attr.getActId())) {
                        doHandleCountdown(attr, PoolType.primary);
                    }
                });
    }

    @NeedRecycle(author = "liqingyang", notRecycle = true)
    @Scheduled(initialDelay = 1000, fixedDelay = 1000)
    public void handleSeniorCountdown() {
        getActivityIds().stream().map(this::getAllComponentAttrs)
                .flatMap(Collection::stream)
                .forEach(attr -> {
                    // 判断活动时间
                    if (actInfoService.inActShowTime(attr.getActId())) {
                        doHandleCountdown(attr, PoolType.senior);
                    }
                });
    }

    @NeedRecycle(author = "liqingyang", notRecycle = true)
    @Scheduled(initialDelay = 500, fixedDelay = 3000)
    public void trySendPrimaryCardPoolBroadcast() {
        getActivityIds().stream().map(this::getAllComponentAttrs)
                .flatMap(Collection::stream)
                .forEach(attr -> {
                    // 判断活动时间
                    if (!actInfoService.inActShowTime(attr.getActId())) {
                        return;
                    }

                    String key = makeKey(attr, String.format(BROAD_CAR_POOL, PoolType.primary.name()));
                    boolean deleted = Boolean.TRUE.equals(actRedisDao.getRedisTemplate(getRedisGroupCode(attr.getActId())).delete(key));
                    if (!deleted) {
                        return;
                    }

                    doBroadcastCardPool(PoolType.primary, attr);
                });
    }

    @NeedRecycle(author = "liqingyang", notRecycle = true)
    @Scheduled(initialDelay = 1500, fixedDelay = 3000)
    public void trySendSeniorCardPoolBroadcast() {
        getActivityIds().stream().map(this::getAllComponentAttrs)
                .flatMap(Collection::stream)
                .forEach(attr -> {
                    // 判断活动时间
                    if (!actInfoService.inActShowTime(attr.getActId())) {
                        return;
                    }

                    String key = makeKey(attr, String.format(BROAD_CAR_POOL, PoolType.senior.name()));
                    boolean deleted = Boolean.TRUE.equals(actRedisDao.getRedisTemplate(getRedisGroupCode(attr.getActId())).delete(key));
                    if (!deleted) {
                        return;
                    }

                    doBroadcastCardPool(PoolType.senior, attr);
                });
    }

    public void doHandleCountdown(FlippingCardComponentAttr attr, PoolType type) {
        final long actId = attr.getActId();
        String lockName = makeKey(attr, String.format(LOCK_NAME, type.name()));
        Secret secret = locker.lock(lockName);
        if (secret == null) {
            return;
        }

        final String groupCode = getRedisGroupCode(actId);
        String currPoolKey = makeKey(attr, String.format(CURRENT_POOL, type.name()));
        try {
            String value = actRedisDao.get(groupCode, currPoolKey);
            CardPool cardPool = null;
            if (StringUtils.startsWith(value, StringUtil.OPEN_BRACE)) {
                cardPool = JSON.parseObject(value, CardPool.class);
            }

            boolean invalid = cardPool == null || (cardPool.state != FLIP_STATE && cardPool.state != SHOW_STATE);
            if (invalid) {
                if (!actInfoService.inActTime(actId)) {
                    actRedisDao.del(groupCode, currPoolKey);
                    return;
                }

                createAndSetCurPool(attr, type, groupCode);
                return;
            }

            // 如果卡片已被翻完，直接开奖
            if (cardPool.state == FLIP_STATE) {
                final String occupyKey = makeKey(attr, String.format(OCCUPY_CARD, cardPool.poolId));
                Long size = actRedisDao.getRedisTemplate(groupCode).opsForHash().size(occupyKey);
                int totalCardSize = type == PoolType.primary ? attr.getPrimaryCards().size() : attr.getSeniorCards().size();
                // 卡片已被翻完
                if (size >= totalCardSize) {
                    boolean finish = doLottery(cardPool.poolId, type, groupCode, attr);
                    if (finish) {
                        cardPool.setState(SHOW_STATE);
                        cardPool.setExpired(System.currentTimeMillis() + attr.getShowCountdown());
                        actRedisDao.set(groupCode, currPoolKey, JSON.toJSONString(cardPool));
                        log.info("doHandleCountdown occupy full lottery finish poolId:{}", cardPool.poolId);
                    }

                    addBroadcastCarPool(type, attr);
                    return;
                }
            }

            long currMills = System.currentTimeMillis();
            // 倒计时未到
            if (currMills < cardPool.expired) {
                return;
            }

            // 展示倒计时结束
            if (cardPool.state == SHOW_STATE) {
                if (!actInfoService.inActTime(actId)) {
                    actRedisDao.del(groupCode, currPoolKey);
                    addBroadcastCarPool(type, attr);
                    return;
                }

                // create next card pool
                createAndSetCurPool(attr, type, groupCode);
                addBroadcastCarPool(type, attr);
                return;
            }

            // 翻转倒计时结束
            boolean finish = doLottery(cardPool.poolId, type, groupCode, attr);
            if (finish) {
                cardPool.setState(SHOW_STATE);
                cardPool.setExpired(System.currentTimeMillis() + attr.getShowCountdown());
                actRedisDao.set(groupCode, currPoolKey, JSON.toJSONString(cardPool));
                log.info("doHandleCountdown countdown finish lottery finish poolId:{}", cardPool.poolId);
            }
            addBroadcastCarPool(type, attr);
        } finally {
            locker.unlock(lockName, secret);
        }
    }

    public boolean doLottery(String poolId, PoolType poolType, String groupCode, FlippingCardComponentAttr attr) {
        String lotteryKey = makeKey(attr, String.format(LOTTERY_STATE, poolId));
        boolean set = actRedisDao.setNX(groupCode, lotteryKey, "1", attr.getShowCountdown() / 1000);
        if (!set) {
            return false;
        }

        String cardPoolKey = makeKey(attr, String.format(CARD_POOL, poolId));
        try {
            Map<String, String> entries = actRedisDao.getRedisTemplate(groupCode).<String, String>opsForHash().entries(cardPoolKey);
            List<Future<Long>> futures = new ArrayList<>(entries.size());
            List<WatchwordCard> cards = new ArrayList<>(entries.size());
            Set<Long> taskIds = new HashSet<>(2);
            for (Map.Entry<String, String> entry : entries.entrySet()) {
                WatchwordCard card = JSON.parseObject(entry.getValue(), WatchwordCard.class);
                taskIds.add(card.taskId);
                cards.add(card);
                if (card.state == CardState.flippable) {
                    futures.add(CompletableFuture.completedFuture(0L));
                    continue;
                }

                // 抽奖放到线程池处理
                if (card.state == CardState.flipped && card.uid > 0) {
                    Future<Long> future = threadPoolManager.get(Const.IMPORTANT_POOL).submit(() -> {
                        String seq = poolId + StringUtil.VERTICAL_BAR + card.cardId;
                        BatchLotteryResult result = hdztAwardServiceClient.doBatchLottery(seq, attr.getBusiId(), card.uid, card.taskId, 1, 3);
                        if (result == null || result.code != 0) {
                            // TODO: alert
                            log.error("doCardLottery fail result:{}, poolId:{}, cardId:{}", result, poolId, card.cardId);
                            throw new RuntimeException("lottery fail");
                        }

                        return result.recordPackages.values().stream().findFirst().orElseThrow();
                    });

                    futures.add(future);
                    continue;
                }

                futures.add(CompletableFuture.failedFuture(new IllegalStateException("card state unexpected")));
            }

            Map<Long, AwardModelInfo> modelInfoMap = getPackageInfo(taskIds);
            Map<String, String> result = new HashMap<>(entries.size());
            Map<Long, Map<Long, Integer>> userCardAwardCounts = new HashMap<>(entries.size());
            for (int i = 0; i < futures.size(); i++) {
                Future<Long> future = futures.get(i);
                WatchwordCard card = cards.get(i);
                try {
                    Long packageId = future.get(5, TimeUnit.SECONDS);
                    if (packageId == null || packageId == 0) {
                        card.setState(CardState.empty);
                    } else {
                        card.setPackageId(packageId);
                        card.setState(CardState.awarded);
                        AwardModelInfo modelInfo = modelInfoMap.get(packageId);
                        if (modelInfo != null) {
                            card.setPackageName(modelInfo.packageName);
                            card.setPackageImage(modelInfo.packageImage);
                        }

                        Map<Long, Integer> awardCounts = userCardAwardCounts.computeIfAbsent(card.uid, (k) -> new HashMap<>(modelInfoMap.size()));
                        awardCounts.compute(packageId, (k, v) -> v == null ? 1 : v + 1);
                    }
                } catch (ExecutionException | TimeoutException e) {
                    card.setState(CardState.failed);
                    log.error("doLottery with poolId:{}, cardId:{} fail", poolId, card.cardId, e);
                }

                result.put(String.valueOf(card.cardId), JSON.toJSONString(card));
            }

            // save cards
            actRedisDao.hmset(groupCode, cardPoolKey, result);

            // unicast
            for (Map.Entry<Long, Map<Long, Integer>> entry : userCardAwardCounts.entrySet()) {
                threadPoolManager.get(Const.IMPORTANT_POOL).execute(() -> {
                    List<CardAward> cardAwards = new ArrayList<>(entry.getValue().size());
                    for (long packageId : entry.getValue().keySet()) {
                        CardAward cardAward = new CardAward();
                        cardAward.setPackageId(packageId);
                        int count = entry.getValue().get(packageId);
                        cardAward.setCount(count);
                        AwardModelInfo modelInfo = modelInfoMap.get(packageId);
                        if (modelInfo != null) {
                            cardAward.setPackageName(modelInfo.packageName);
                            cardAward.setPackageImage(modelInfo.packageImage);
                        }

                        cardAwards.add(cardAward);
                    }

                    JSONObject json = new JSONObject(5);
                    json.put("type", poolType.name());
                    json.put("poolId", poolId);
                    json.put("cardAwards", cardAwards);

                    GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                            .setActId(attr.getActId())
                            .setNoticeType(PBCommonNoticeType.FLIP_AWARD)
                            .setNoticeValue(json.toJSONString());

                    GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                            .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                            .setCommonNoticeResponse(panel).build();

                    String seq = String.format("award_uni_%s|%d", poolId, entry.getKey());
                    RetryTool.withRetryCheck(attr.getActId(), seq, () -> svcSDKService.unicastUid(entry.getKey(), msg));
                });

            }
        } catch (Exception e) {
            log.error("doLottery with poolId:{}, poolType:{} exception:", poolId, poolType, e);
            actRedisDao.del(groupCode, lotteryKey);
            return false;
        }

        return true;
    }

    private Map<Long, AwardModelInfo> getPackageInfo(Set<Long> packageIds) {
        try {
            return hdztAwardServiceClient.queryAwardTasks(new ArrayList<>(packageIds), false);
        } catch (Exception e) {
            log.error("getPackageInfo fail:", e);
        }

        return Collections.emptyMap();
    }

    @GetMapping("chance/detail")
    public Response<JSONObject> getMyChanceDetail(@RequestParam("actId") long actId,
                                                  @RequestParam("cmptIndex") long cmptIndex) {
        FlippingCardComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist!");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(403, "login is need!");
        }

        final String groupCode = getRedisGroupCode(actId);
        UserBaseInfo userBaseInfo = commonService.getUserInfo(uid, false);
        JSONObject data = new JSONObject(5);
        data.put("uid", uid);
        if (userBaseInfo != null) {
            data.put("nick", userBaseInfo.getNick());
            data.put("avatar", userBaseInfo.getHdLogo());
        }
        String balanceKey = makeKey(attr, String.format(CHANCE_BALANCE, uid));
        String value = actRedisDao.get(groupCode, balanceKey);
        int balance = StringUtils.isNumeric(value) ? Integer.parseInt(value) : 0;
        data.put("balance", balance);

        return Response.success(data);
    }

    @GetMapping("card/pool")
    public Response<CardPoolDetail> getCardPoolDetail(@RequestParam("actId") long actId,
                                                      @RequestParam("cmptIndex") long cmptIndex,
                                                      @RequestParam("type") PoolType type) {
        FlippingCardComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist!");
        }

        CardPoolDetail poolDetail = doGetCardPoolDetail(attr, type);
        if (poolDetail == null) {
            return Response.fail(432, "pool not available");
        }

        return Response.success(poolDetail);
    }

    @RequestMapping("occupy/card")
    public Response<CardDetail> occupyCard(@RequestParam("actId") long actId,
                                  @RequestParam("cmptIndex") long cmptIndex,
                                  @RequestParam("poolId") String poolId,
                                  @RequestParam("cardId") int cardId) {
        FlippingCardComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist!");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(403, "login is need!");
        }

        final String groupCode = getRedisGroupCode(actId), cardIdStr = String.valueOf(cardId);
        // 判断pool的状态
        String cardPoolKey = makeKey(attr, String.format(CARD_POOL, poolId));
        String value = actRedisDao.hget(groupCode, cardPoolKey, cardIdStr);
        if (!StringUtils.startsWith(value, StringUtil.OPEN_BRACE)) {
            return Response.fail(431, "card not exist");
        }

        WatchwordCard card = JSON.parseObject(value, WatchwordCard.class);
        if (card.getState() != CardState.flippable) {
            addBroadcastCarPool(card.type, attr);
            return Response.fail(431, "card not flippable");
        }

        String currPoolKey = makeKey(attr, String.format(CURRENT_POOL, card.type.name()));
        value = actRedisDao.get(groupCode, currPoolKey);
        if (!StringUtils.startsWith(value, StringUtil.OPEN_BRACE)) {
            addBroadcastCarPool(card.type, attr);
            return Response.fail(432, "pool not available");
        }

        CardPool cardPool = JSON.parseObject(value, CardPool.class);
        if (cardPool.state != 0) {
            addBroadcastCarPool(card.type, attr);
            return Response.fail(432, "pool not flippable");
        }

        UserBaseInfo userInfo =  commonService.getUserInfo(uid, false);
        card.setState(CardState.flipped);
        card.setUid(uid);
        card.setNick(userInfo.getNick());
        card.setAvatar(userInfo.getLogo());
        String cardVal = JSON.toJSONString(card);
        final int cost = card.type == PoolType.primary ? attr.getPrimaryPoolCost() : attr.getSeniorPoolCost();

        String lotteryKey = makeKey(attr, String.format(LOTTERY_STATE, poolId));
        String balanceKey = makeKey(attr, String.format(CHANCE_BALANCE, uid));
        final String occupyKey = makeKey(attr, String.format(OCCUPY_CARD, poolId));
        long expiredSec = (attr.getFlipCountdown() + attr.getShowCountdown()) / 1000 + 10 * 60;
        Long result = actRedisDao.getRedisTemplate(groupCode).execute(OCCUPY_SCRIPT,
                ImmutableList.of(lotteryKey, cardPoolKey, balanceKey, occupyKey),
                cardVal, String.valueOf(cost), cardIdStr, String.valueOf(uid), String.valueOf(expiredSec));

        if (result == null || result != 0) {
            if (result != null && result.intValue() != 433) {
                addBroadcastCarPool(card.type, attr);
            }
            return Response.fail(result == null ? 500 : result.intValue(), "do occupy card fail");
        }

        CardDetail data = new CardDetail(card);
        addBroadcastCarPool(card.type, attr);

        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> addFlipUid(attr, card.type, uid, groupCode));

        return Response.success(data);
    }

    private void addFlipUid(FlippingCardComponentAttr attr, PoolType type, long uid, String groupCode) {
        String dateStr = DateFormatUtils.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
        String totalKey = makeKey(attr, String.format(FLIP_UID_SET, type.name()));
        String dailyKey = totalKey + StringUtil.UNDERSCORE + dateStr;

        actRedisDao.sadd(groupCode, totalKey, String.valueOf(uid));
        actRedisDao.sadd(groupCode, dailyKey, String.valueOf(uid));

        String totalCountKey = makeKey(attr, String.format(FLIP_COUNT, type.name()));
        actRedisDao.hIncrByKey(groupCode, totalCountKey, "total", 1);
        actRedisDao.hIncrByKey(groupCode, totalCountKey, dateStr, 1);
    }

    @GetMapping("stat")
    public Response<?> queryStat(@RequestParam("actId") long actId,
                                 @RequestParam("cmptIndex") long cmptIndex,
                                 @RequestParam("dateStr") String dateStr) {
        FlippingCardComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist!");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(403, "login is need!");
        }

        String uids = cacheService.getActAttrValue(actId, GeActAttrConst.DATA_STAT_UID, "");
        if (!uids.contains(Convert.toString(uid))) {
            return Response.fail(403, "no authority");
        }

        //获得抽奖机会次数
        String totalChanceKey = makeKey(attr, CHANCE_UID_SET);
        String dailyChanceKey = totalChanceKey + StringUtil.UNDERSCORE + dateStr;

        String totalChanceCountKey = makeKey(attr, CHANCE_COUNT);
        String dailyChanceCountKey = totalChanceCountKey + StringUtil.UNDERSCORE + dateStr;

//        String totalPool1CountKey = makeKey(attr, String.format(CARD_POOL_COUNT, PoolType.primary));
//        String dailyPool1CountKey = totalPool1CountKey + StringUtil.UNDERSCORE + dateStr;
//
//        String totalPool2CountKey = makeKey(attr, String.format(CARD_POOL_COUNT, PoolType.senior));
//        String dailyPool2CountKey = totalPool2CountKey + StringUtil.UNDERSCORE + dateStr;

        //翻卡人数
        String totalFlip1Key = makeKey(attr, String.format(FLIP_UID_SET, PoolType.primary));
        String dailyFlip1Key = totalFlip1Key + StringUtil.UNDERSCORE + dateStr;

        String totalFlip2Key = makeKey(attr, String.format(FLIP_UID_SET, PoolType.senior));
        String dailyFlip2Key = totalFlip2Key + StringUtil.UNDERSCORE + dateStr;

        //翻卡次数
        String primaryCountKey = makeKey(attr, String.format(FLIP_COUNT, PoolType.primary));
        String seniorCountKey = makeKey(attr, String.format(FLIP_COUNT, PoolType.senior));


        List<Object> counts = actRedisDao.getRedisTemplate(getRedisGroupCode(actId))
                .executePipelined((RedisCallback<Object>) connection -> {
                    connection.sCard(totalChanceKey.getBytes(StandardCharsets.UTF_8));
                    connection.sCard(dailyChanceKey.getBytes(StandardCharsets.UTF_8));

                    connection.get(totalChanceCountKey.getBytes(StandardCharsets.UTF_8));
                    connection.get(dailyChanceCountKey.getBytes(StandardCharsets.UTF_8));

//            connection.get(totalPool1CountKey.getBytes(StandardCharsets.UTF_8));
//            connection.get(dailyPool1CountKey.getBytes(StandardCharsets.UTF_8));
//
//            connection.get(totalPool2CountKey.getBytes(StandardCharsets.UTF_8));
//            connection.get(dailyPool2CountKey.getBytes(StandardCharsets.UTF_8));

                    connection.sCard(totalFlip1Key.getBytes(StandardCharsets.UTF_8));
                    connection.sCard(dailyFlip1Key.getBytes(StandardCharsets.UTF_8));

                    connection.sCard(totalFlip2Key.getBytes(StandardCharsets.UTF_8));
                    connection.sCard(dailyFlip2Key.getBytes(StandardCharsets.UTF_8));

                    connection.hGet(primaryCountKey.getBytes(StandardCharsets.UTF_8), "total".getBytes(StandardCharsets.UTF_8));
                    connection.hGet(primaryCountKey.getBytes(StandardCharsets.UTF_8), dateStr.getBytes(StandardCharsets.UTF_8));

                    connection.hGet(seniorCountKey.getBytes(StandardCharsets.UTF_8), "total".getBytes(StandardCharsets.UTF_8));
                    connection.hGet(seniorCountKey.getBytes(StandardCharsets.UTF_8), dateStr.getBytes(StandardCharsets.UTF_8));
                    return null;
                });

        Map<String,Object> result = Maps.newLinkedHashMap();
        result.put("总积分发放人数", Convert.toInt(counts.get(0), 0));
        result.put("日积分发放人数", Convert.toInt(counts.get(1), 0));

        result.put("总积分发放次数", Convert.toInt(counts.get(2), 0));
        result.put("日积分发放次数", Convert.toInt(counts.get(3), 0));

//        result.put("primaryTotalPool", Convert.toInt(counts.get(2), 0));
//        result.put("primaryDailyPool", Convert.toInt(counts.get(3), 0));
//
//        result.put("seniorTotalPool", Convert.toInt(counts.get(4), 0));
//        result.put("seniorDailyPool", Convert.toInt(counts.get(5), 0));

        result.put("初级总翻卡人数", Convert.toInt(counts.get(4), 0));
        result.put("初级日翻卡人数", Convert.toInt(counts.get(5), 0));

        result.put("高级总翻卡人数", Convert.toInt(counts.get(6), 0));
        result.put("高级日翻卡人数", Convert.toInt(counts.get(7), 0));

        result.put("初级总翻卡次数", Convert.toInt(counts.get(8), 0));
        result.put("初级日翻卡次数", Convert.toInt(counts.get(9), 0));

        result.put("高级总翻卡次数", Convert.toInt(counts.get(10), 0));
        result.put("高级日翻卡次数", Convert.toInt(counts.get(11), 0));

        return Response.success(result);
    }

    public CardPoolDetail doGetCardPoolDetail(FlippingCardComponentAttr attr, PoolType type) {
        final String groupCode = getRedisGroupCode(attr.getActId());
        String currPoolKey = makeKey(attr, String.format(CURRENT_POOL, type.name()));
        String value = actRedisDao.get(groupCode, currPoolKey);
        if (!StringUtils.startsWith(value, StringUtil.OPEN_BRACE)) {
            int timeStatus = actInfoService.actTimeStatus(attr.getActId());
            if (timeStatus == 0) {
                log.warn("doGetCardPoolDetail in activity time but not pool was found");
                return null;
            }

            CardPoolDetail detail = new CardPoolDetail();
            detail.setPoolId(StringUtils.EMPTY);
            detail.setState(timeStatus < 0 ? BEFORE_STATE : AFTER_STATE);
            detail.setType(type);
            detail.setCost(type == PoolType.primary ? attr.getPrimaryPoolCost() : attr.getSeniorPoolCost());
            List<FlippingCardComponentAttr.Card> cardConfigs = type == PoolType.primary ? attr.getPrimaryCards() : attr.getSeniorCards();
            List<CardDetail> cards = cardConfigs.stream().map(card -> {
                CardDetail cardDetail = new CardDetail();
                cardDetail.setCardId(card.getCardId());
                cardDetail.setText(card.getText());
                cardDetail.setState(CardState.empty);
                return cardDetail;
            }).sorted(Comparator.comparingInt(CardDetail::getCardId)).toList();
            detail.setCards(cards);
            return detail;
        }

        CardPool cardPool = JSON.parseObject(value, CardPool.class);
        final String poolId = cardPool.poolId;
        String cardPoolKey = makeKey(attr, String.format(CARD_POOL, poolId));
        Map<String, String> entries = actRedisDao.getRedisTemplate(groupCode).<String, String>opsForHash().entries(cardPoolKey);
        final long honorTaskId = type == PoolType.primary ? attr.getPrimaryHonorTaskId() : attr.getSeniorHonorTaskId();
        List<CardDetail> cards = entries.values().stream().map(val -> JSON.parseObject(val, WatchwordCard.class))
                .map(card -> {
                    CardDetail cardDetail = new CardDetail(card);
                    if (card.state != CardState.flippable && card.state != CardState.flipped) {
                        cardDetail.setHonor(card.taskId == honorTaskId);
                    }
                    return cardDetail;
                })
                .sorted(Comparator.comparingInt(CardDetail::getCardId))
                .toList();
        CardDetail honorCard = cards.stream().filter(cardDetail -> cardDetail.uid > 0 && cardDetail.honor).findFirst().orElse(null);
        if (honorCard != null) {
            CardDetail copy = new CardDetail();
            copy.setHonor(true);
            copy.setCardId(honorCard.cardId);
            copy.setText(honorCard.text);
            copy.setState(honorCard.state);
            copy.setUid(honorCard.uid);
            copy.setAvatar(honorCard.avatar);
            copy.setNick(honorCard.nick);
            copy.setPackageName(honorCard.packageName);
            copy.setPackageImage(honorCard.packageImage);
            honorCard = copy;
        }
        long flippableCount = cards.stream().filter(cardDetail -> cardDetail.state == CardState.flippable).count();

        CardPoolDetail detail = new CardPoolDetail();
        detail.setPoolId(cardPool.poolId);
        detail.setType(type);
        detail.setCost(type == PoolType.primary ? attr.getPrimaryPoolCost() : attr.getSeniorPoolCost());
        detail.setState(cardPool.state);
        detail.setCards(cards);
        detail.setHonorCard(honorCard);
        detail.setFlippableCount(flippableCount);
        detail.setCountdown(Math.max(cardPool.expired - System.currentTimeMillis(), 0) + 100);

        return detail;
    }

    public void addBroadcastCarPool(PoolType type, FlippingCardComponentAttr attr) {
        String key = makeKey(attr, String.format(BROAD_CAR_POOL, type.name()));
        actRedisDao.incrValue(getRedisGroupCode(attr.getActId()), key, 1);
        actRedisDao.getRedisTemplate(getRedisGroupCode(attr.getActId())).opsForValue().set(key, String.valueOf(System.currentTimeMillis()), getCountdownSecs(attr), TimeUnit.SECONDS);
    }

    public long getCountdownSecs(FlippingCardComponentAttr attr) {
        return (attr.getShowCountdown() + attr.getFlipCountdown()) / 1000 +20;
    }

    public void doBroadcastCardPool(PoolType type, FlippingCardComponentAttr attr) {
        CardPoolDetail detail = doGetCardPoolDetail(attr, type);
        final long bannerId = 5076001L;
        long bannerType = type == PoolType.primary ? 0 : 1;
        if (detail != null) {
            List<ChannelInfo> customerChannel = whiteListService.getAllCustomerChannel(attr.getActId(), Convert.toString(this.getComponentId()));
            if (CollectionUtils.isEmpty(customerChannel)) {
                commonBroadCastService.commonBannerBroadcast(0, 0, 0,
                        BroadCastHelpService.changeBusiId2BroTemplate(attr.getBusiId()), BroadcastType.ALL_TEMPLATE,
                        attr.getActId(), 0, 0, bannerId, bannerType, detail);
            }
            //非在线频道也要广播
            else {
                log.info("doBroadcastCardPool customer channel,actId:{},size:{}", attr.getActId(), customerChannel.size());
                commonBroadCastService.commonBannerBroadcast(attr.getActId(), 0, 0, StringUtil.EMPTY, bannerId, bannerType, detail, customerChannel);
            }
        }
    }

    public List<Integer> getHonorCardIds(List<FlippingCardComponentAttr.Card> cards, int count) {
        Assert.isTrue(count < cards.size(), "count can not bigger than list size");
        List<Integer> cardIds = cards.stream().map(FlippingCardComponentAttr.Card::getCardId).collect(Collectors.toList());
        Collections.shuffle(cardIds);
        return cardIds.subList(0, count);
    }

    public static enum PoolType {
        primary, senior
    }

    public static enum CardState {
        /**
         * 等待被翻开，展示文案
         **/
        flippable,
        /**
         * 已被翻开，等待抽奖
         **/
        flipped,
        /**
         * 抽奖完成，展示抽奖结果
         **/
        awarded,

        /**
         * 失败
         */
        failed,

        /**
         * 未被选择
         */
        empty
    }

    @Data
    public static class CardPool {
        protected String poolId;

        protected int state;

        protected long expired;
    }

    @Data
    public static class WatchwordCard {

        protected int cardId;

        protected PoolType type;

        protected String text;

        protected CardState state;

        protected long taskId;

        protected long uid;

        protected String nick;

        protected String avatar;

        protected long packageId;

        protected String packageName;

        protected String packageImage;
    }

    @Data
    public static class CardDetail {

        public CardDetail() {
        }

        public CardDetail(WatchwordCard card) {
            this.cardId = card.cardId;
            this.text = card.text;
            this.state = card.state;
            this.uid = card.uid;
            this.nick = card.nick;
            this.avatar = card.avatar;
            this.packageName = card.packageName;
            this.packageImage = card.packageImage;
        }

        protected int cardId;

        protected String text;

        protected CardState state;

        protected long uid;

        protected String nick;

        protected String avatar;

        protected boolean honor;

        protected String packageName;

        protected String packageImage;
    }

    @Data
    public static class CardPoolDetail {
        protected PoolType type;

        protected int cost;

        protected String poolId;

        protected int state;

        protected long countdown;

        protected List<CardDetail> cards;

        protected CardDetail honorCard;

        protected long flippableCount;
    }

    @Data
    public static class CardAward {
        protected long packageId;

        protected String packageName;

        protected String packageImage;

        protected int count;
    }
}
