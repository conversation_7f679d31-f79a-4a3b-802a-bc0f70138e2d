package com.yy.gameecology.hdzj;

import com.alibaba.fastjson.JSON;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.aomi.sdk.core.Trace;
import com.yy.aomi.sdk.core.TracerHolder;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.fostress.api.FirstExecRetryContext;
import com.yy.fostress.api.RetryContext;
import com.yy.fostress.api.RetryStrategy;
import com.yy.fostress.exception.TransSetUpException;
import com.yy.fostress.sitter.api.StatelessRetryCallback;
import com.yy.fostress.sitter.service.MessageAckService;
import com.yy.fostress.sitter.thrift.FostressCenterServiceClient;
import com.yy.fostress.yrpc.Fostress;
import com.yy.gameecology.activity.Aomi;
import com.yy.gameecology.activity.bean.SendGiftComboEvent;
import com.yy.gameecology.activity.bean.SendGiftEvent;
import com.yy.gameecology.activity.bean.StartShowEvent;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.BaseEvent;
import com.yy.gameecology.activity.bean.hdzt.HdztAwardLotteryMsg;
import com.yy.gameecology.activity.bean.mq.ChannelFightEndEvent;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.service.ActInfoService;
import com.yy.gameecology.activity.service.CacheService;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeActAttrConst;
import com.yy.gameecology.common.db.model.gameecology.HdzjActivity;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.MDCUtils;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.bean.*;
import com.yy.gameecology.hdzj.element.ActComponent;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.thrift.hdztranking.RoleType;
import io.opentelemetry.api.GlobalOpenTelemetry;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Scope;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;

/**
 * 活动组件事件分发处理类
 *
 * @author: 郭立平[<EMAIL>]
 * @date: 2021/2/4 10:50
 **/
@Component
public class HdzjEventDispatcher extends StatelessRetryCallback<EventNotifyParam, Throwable> {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 组件里用了很多sleep，所以把超时时间调长，防止因超时错误地触发重试
     */
    private static final RetryStrategy STRATEGY = new RetryStrategy() {
        @Override
        public long transTimeout() {
            return 50 * DateUtils.MILLIS_PER_SECOND;
        }
    };

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private ActInfoService actInfoService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private HdzjComponentCollaborator hdzjComponentCollaborator;

    @Autowired
    private MessageAckService messageAckService;

    @Autowired
    private FostressCenterServiceClient fostressCenterServiceClient;

    private final Tracer TRACER = GlobalOpenTelemetry.getTracer(HdzjEventDispatcher.class.getName());

    /**
     * 中台发奖事件通知
     */
    public void notify(HdztAwardLotteryMsg event) {
        notify(event.getActId(), event, event.getSeq());
    }

    /**
     * 中台榜单事件通知
     */
    public void notify(BaseEvent event) {
        notify(event.getActId(), event, event.getSeq());
    }

    /**
     * 各业务送礼事件通知
     */
    public void notify(SendGiftEvent event) {
        String seq = event.getSeq();
        long sendUid = event.getSendUid();
        long recvUid = event.getRecvUid();
        Map<Long, HdzjActivity> map = cacheService.getHdzjActivityMap();

        for (Long actId : map.keySet()) {
            // 保持老的活动有效性检查逻辑，严格在活动时间内才有效，不给活动结束后的时间冗余
            ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityInfo(actId);
            if (activityInfoVo == null || !actId.equals(activityInfoVo.getActId())) {
                log.warn("notify act info not found@actId:{}, seq:{}, sendUId:{}, recvUid:{}", actId, seq, sendUid, recvUid);
                continue;
            }
            long currentTime = activityInfoVo.getCurrentTime();
            long endTime = Convert.toLong(cacheService.getActAttrValue(actId, String.format(GeActAttrConst.COMPONENT_ACT_END_TIME, event.getClass().getName()), activityInfoVo.getEndTime()+""));
            if (currentTime < activityInfoVo.getBeginTime() || currentTime > endTime) {
                log.warn("notify not in act time@actId:{}, seq:{}, sendUId:{}, recvUid:{}", actId, seq, sendUid, recvUid);
                continue;
            }

            //生产环境灰度白名单控制
            if (!commonService.checkWhiteList(actId, RoleType.USER, String.valueOf(sendUid))) {
                log.warn("notify skip@actId:{}, seq:{}, sendUid:{} not int whitelist! recvUid:{}", actId, seq, sendUid, recvUid);
                continue;
            }

            notify(actId, event, event.getSeq());
        }
    }

    public void notify(SendGiftComboEvent event) {
        String seq = event.getSeq();
        long sendUid = event.getSendUid();
        long recvUid = event.getRecvUid();
        Map<Long, HdzjActivity> map = cacheService.getHdzjActivityMap();

        for (Long actId : map.keySet()) {
            // 保持老的活动有效性检查逻辑，严格在活动时间内才有效，不给活动结束后的时间冗余
            ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityInfo(actId);
            if (activityInfoVo == null || !actId.equals(activityInfoVo.getActId())) {
                log.warn("notify act info not found@actId:{}, seq:{}, sendUId:{}, recvUid:{}", actId, seq, sendUid, recvUid);
                continue;
            }
            long currentTime = activityInfoVo.getCurrentTime();
            long endTime = Convert.toLong(cacheService.getActAttrValue(actId, String.format(GeActAttrConst.COMPONENT_ACT_END_TIME, event.getClass().getName()), activityInfoVo.getEndTime()+""));
            if (currentTime < activityInfoVo.getBeginTime() || currentTime > endTime) {
                log.warn("notify not in act time@actId:{}, seq:{}, sendUId:{}, recvUid:{}", actId, seq, sendUid, recvUid);
                continue;
            }

            //生产环境灰度白名单控制
            if (!commonService.checkWhiteList(actId, RoleType.USER, String.valueOf(sendUid))) {
                log.warn("notify skip@actId:{}, seq:{}, sendUid:{} not int whitelist! recvUid:{}", actId, seq, sendUid, recvUid);
                continue;
            }

            notify(actId, event, event.getSeq());
        }
    }


    /**
     * 交友乱斗事件通知
     */
    public void notify(ChannelFightEndEvent fightEndEvent) {
        String seq = fightEndEvent.getGameId();
        long anchorUid = fightEndEvent.getCompereUid();
        long otherAnchorUid = fightEndEvent.getExtraUid();
        Map<Long, HdzjActivity> map = cacheService.getHdzjActivityMap();

        for (Long actId : map.keySet()) {
            // 保持老的活动有效性检查逻辑，严格在活动时间内才有效，不给活动结束后的时间冗余
            ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityInfo(actId);
            if (activityInfoVo == null || !actId.equals(activityInfoVo.getActId())) {
                log.warn("notify act info not found@actId:{}, seq:{}, anchorUid:{}, otherAnchorUid:{}", actId, seq, anchorUid, otherAnchorUid);
                continue;
            }
            long currentTime = activityInfoVo.getCurrentTime();
            long endTime = Convert.toLong(cacheService.getActAttrValue(actId, String.format(GeActAttrConst.COMPONENT_ACT_END_TIME, fightEndEvent.getClass().getName()), activityInfoVo.getEndTime()+""));
            if (currentTime < activityInfoVo.getBeginTime() || currentTime > endTime) {
                log.warn("notify not in act time@actId:{}, seq:{}, anchorUid:{}, otherAnchorUid:{}", actId, seq, anchorUid, otherAnchorUid);
                continue;
            }

            //生产环境灰度白名单控制
            if (!commonService.checkWhiteList(actId, RoleType.ANCHOR, String.valueOf(anchorUid)) ||
                    !commonService.checkWhiteList(actId, RoleType.ANCHOR, String.valueOf(otherAnchorUid))) {
                log.warn("notify skip@actId:{}, seq:{}, anchorUid:{}  or otherAnchorUid:{} not int whitelist! ", actId, seq, anchorUid, otherAnchorUid);
                continue;
            }

            notify(actId, fightEndEvent, fightEndEvent.getGameId());
        }
    }

    public void notify(StartShowEvent startShowEvent) {
        String seq = startShowEvent.getSeq();
        Map<Long, HdzjActivity> map = cacheService.getHdzjActivityMap();
        long uid = startShowEvent.getUid();
        for (Long actId : map.keySet()) {
            ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityInfo(actId);
            if (activityInfoVo == null || !actId.equals(activityInfoVo.getActId())) {
                log.warn("notify act info not found@actId:{}, seq:{}, anchorUid:{}", actId, seq, uid);
                continue;
            }

            long currentTime = activityInfoVo.getCurrentTime();
            long endTime = Convert.toLong(cacheService.getActAttrValue(actId, String.format(GeActAttrConst.COMPONENT_ACT_END_TIME, startShowEvent.getClass().getName()), activityInfoVo.getEndTime()+""));
            if (currentTime < activityInfoVo.getBeginTime() || currentTime > endTime) {
                log.warn("notify not in act time@actId:{}, seq:{}, anchorUid:{}", actId, seq, uid);
                continue;
            }

            //生产环境灰度白名单控制
            if (!commonService.checkWhiteList(actId, RoleType.ANCHOR, String.valueOf(uid))) {
                log.warn("notify skip@actId:{}, seq:{}, anchorUid:{} not int whitelist! ", actId, seq, uid);
                continue;
            }

            notify(actId, startShowEvent, seq);
        }

    }

    /**
     * 自定义与活动无关的事件响应
     */
    public void notify(long uid, RoleType roleType, Object event, String seq) {

        Map<Long, HdzjActivity> map = cacheService.getHdzjActivityMap();
        int index = 0;
        for (Long actId : map.keySet()) {
            notify(actId, uid, roleType, event, seq + StringUtil.UNDERSCORE + index);
            index++;
        }
    }

    /**
     * 自定义与活动相关的事件响应
     */
    public void notify(Long actId, long uid, RoleType roleType, Object event, final String seq) {

        // 保持老的活动有效性检查逻辑，严格在活动时间内才有效，不给活动结束后的时间冗余
        ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityInfo(actId);
        if (activityInfoVo == null || !actId.equals(activityInfoVo.getActId())) {
            log.warn("{} notify act info not found@actId:{}, uid:{}, roleType:{}", event.getClass().getSimpleName(), actId, uid, roleType);
            return;
        }

        long currentTime = activityInfoVo.getCurrentTime();
        long endTime = Convert.toLong(cacheService.getActAttrValue(actId, String.format(GeActAttrConst.COMPONENT_ACT_END_TIME, event.getClass().getName()), activityInfoVo.getEndTime()+""));
        if (currentTime < activityInfoVo.getBeginTime() || currentTime > endTime) {
            log.warn("{} notify not in act time@actId:{}, uid:{}, roleType:{}", event.getClass().getSimpleName(), actId, uid, roleType);
            return;
        }

        //生产环境灰度白名单控制
        if (!commonService.checkWhiteList(actId, roleType, String.valueOf(uid))) {
            log.warn("{} notify skip@actId:{}, uid:{}  roleType:{} not int whitelist!", event.getClass().getSimpleName(), actId, uid, roleType);
            return;
        }
        notify(actId, event, seq);
    }

    /**
     * 自定义与活动无关的事件响应-多用户事件
     */
    public void notify(List<Pair<RoleType, String>> roleList, Object event, String seq) {

        Map<Long, HdzjActivity> map = cacheService.getHdzjActivityMap();
        int index = 0;
        for (Long actId : map.keySet()) {
            notify(actId, roleList, event, seq + StringUtil.UNDERSCORE + index);
            index++;
        }
    }
    /**
     * 自定义与活动相关的事件响应-多用户事件
     */
    public void notify(Long actId, List<Pair<RoleType,String>> roleList, Object event, String seq) {

        // 保持老的活动有效性检查逻辑，严格在活动时间内才有效，不给活动结束后的时间冗余
        ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityInfo(actId);
        if (activityInfoVo == null || !actId.equals(activityInfoVo.getActId())) {
            log.warn("{} notify act info not found@actId:{}, uid:{}, seq:{}", event.getClass().getSimpleName(), actId, seq);
            return;
        }
        long currentTime = activityInfoVo.getCurrentTime();
        long endTime = Convert.toLong(cacheService.getActAttrValue(actId, String.format(GeActAttrConst.COMPONENT_ACT_END_TIME, event.getClass().getName()), activityInfoVo.getEndTime()+""));
        if (currentTime < activityInfoVo.getBeginTime() ||  currentTime > endTime) {
            log.warn("{} notify not in act time@actId:{}, seq:{}", event.getClass().getSimpleName(), actId, seq);
            return;
        }

        //生产环境灰度白名单控制
        if (!commonService.checkWhiteList(actId, roleList)) {
            log.warn("{} notify skip@actId:{}, roleList:{} not int whitelist!", event.getClass().getSimpleName(), actId, roleList);
            return;
        }
        notify(actId, event, seq);
    }
    /**
     * 通用请求
     */
    public CommonPBOperateResp commonOperatePbRequest(CommonPBOperateRequest request) {
        try {
            return (CommonPBOperateResp) hdzjComponentCollaborator.callComponent(request.getActId(), request.getCmptId(), "commonOperatePbRequest", request);
        } catch (Exception e) {
            log.error("commonOperateRequest error,request:{},e:{}", JSON.toJSONString(request), e.getMessage(), e);
            return null;
        }
    }

    private static final int EXECUTED_CODE = 400;

    public void notify(final long actId, final Object event, final String seq) {
        notify(actId, event, seq, false);
    }

    /**
     *
     * @param notUserAutoRetry 某些事件量比较大，且不需要自动重试的，可以设置强制不入自动重试系统
     */
    public void notify(final long actId, final Object event, final String seq, final boolean notUserAutoRetry) {
        // 活动状态检查无效时直接返回
        if (!checkActivityStatus(actId, event)) {
            return;
        }

        if(!canNotify(actId, event)) {
            log.warn("act cant notify @actId:{}, class:{}, seq:{}", actId, event.getClass().getName(), seq);
            return;
        }

        // 若无事件的处理器则直接返回
        List<HandlerBean> handlerBeans = ElementManager.getHandlerBeans(actId);
        if (CollectionUtils.isEmpty(handlerBeans)) {
            log.warn("notify skip@no handler for activity -> {}", JSON.toJSONString(event));
            return;
        }

        // 往fostress登记一条数据
        long start = System.currentTimeMillis();

        // 使用消息的seq
        EventNotifyParam param = new EventNotifyParam(handlerBeans, actId, event, event.getClass());

        final String messageId = actId + StringUtil.UNDERSCORE + seq;
        String traceId = "";
        boolean retry = false;
        //默认全量打开
        boolean openAutoRetry = Const.ONESTR.equals(cacheService.getActAttrValue(actId, GeActAttrConst.OPEN_CMPT_MSG_AUTO_RETRY, Const.ONESTR));

        if (notUserAutoRetry) {
            openAutoRetry = false;
        }
        if (SysEvHelper.isDev() || openAutoRetry) {
            try {
                Fostress.TransResponse transResponse = fostressCenterServiceClient.doSetUpTrans(messageId, this, STRATEGY, param);
                log.info("doSetUpTrans resp={}", transResponse == null ? null : JsonFormat.printToString(transResponse));
                //曾经下过单，并且是完结状态，不往下执行
                if (transResponse != null && transResponse.getResult() == EXECUTED_CODE) {
                    // {"result": 400,"message": "message had executed"}
                    log.warn("message had executed ,messageId={} ", messageId);
                    return;
                }

                if (transResponse == null || !StringUtils.hasText(transResponse.getTraceId())) {
                    throw new TransSetUpException();
                }

                traceId = transResponse.getTraceId();
                //曾经下过单，是非完结状态，可重复往下执行
                retry = transResponse.getResult() == 100;
            } catch (Exception ex) {
                log.error("doSetUpTrans error", ex);
            }
        }

        // 丢到线程池中异步执行
        String tTraceId = traceId;
        boolean tRetry = retry;
        threadPoolManager.get(Const.KAFKA_MSG_HANDLER_POOL).execute(() -> {
            List<Future<Boolean>> futures = handleComponent(param, tRetry);

            // 判断futures的结果,告知fostress
            Fostress.ExecState execState = Fostress.ExecState.successful;
            Throwable throwable = null;
            for (Future<Boolean> future : futures) {
                try {
                    if (!future.get()) {
                        execState = Fostress.ExecState.exception_expected;
                        break;
                    }
                } catch (Exception ex) {
                    execState = Fostress.ExecState.exception_expected;
                    log.error("get component handler result error", ex);
                    throwable = ex;
                    break;
                }
            }
            if (StringUtil.isNotBlank(tTraceId)) {
                messageAckService.acknowledge(FirstExecRetryContext.with(messageId, tTraceId), execState
                        , start, System.currentTimeMillis(), execState == Fostress.ExecState.successful ? 1 : 0, throwable);
            }
        });
    }

    private List<Future<Boolean>> handleComponent(EventNotifyParam param, final boolean fromRetry) {
        // 组件之间异步执行
        final Trace asyncTrace = TracerHolder.getTracer().start("HdzjEventDispatcher.notify", false,
                Aomi.HANDLE_EXECUTOR_IMPORTANT_TRACE_CONTEXT);

        List<Future<Boolean>> futures = new ArrayList<>();
        long actId = param.getActId();
        Object event = param.getEvent();
        for (final HandlerBean handlerBean : param.getHandlerBeans()) {
            Callable<Boolean> callable = () -> {
                Clock clock = new Clock();
                ActComponent actComponent = handlerBean.getActComponent();
                long cmptId = actComponent.getComponentId();
                long cmptUseInx = handlerBean.getCmptUseInx();
                try {
                    // 对唯一所有的检查
                    if (actComponent.isUniq1UseIndex() && cmptUseInx != 1) {
                        log.error("notify fail1@not uniq 1 use index, actId:{}, cmptId:{}, cmptUseInx:{}, event:{}",
                                actId, cmptId, cmptUseInx, JSON.toJSONString(event));
                        return true;
                    }

                    // 对参数对象检查
                    ComponentAttr componentAttr = actComponent.getComponentAttr(actId, cmptUseInx);
                    if (componentAttr == null) {
                        log.error("notify fail2@componentAttr is null, actId:{}, cmptId:{}, cmptUseInx:{}, event:{}",
                                actId, cmptId, cmptUseInx, JSON.toJSONString(event));
                        return true;
                    }

                    return processActComponent(actId, event, param.getEventClass(), actComponent, componentAttr, fromRetry);
                } catch (Throwable e) {
                    log.error("notify exception@actId:{}, cmptId:{}, event:{}, err:{} {}", actId, cmptId, JSON.toJSONString(event), e.getMessage(), clock.tag(), e);
                    return false;
                } finally {
                    TracerHolder.getTracer().end(asyncTrace);
                }
            };

            Future<Boolean> future = threadPoolManager.get(Const.IMPORTANT_POOL).submit(callable);
            futures.add(future);
        }

        return futures;
    }

    /**
     * 检查活动状态，下面的检查可能会阻碍一些消息的通知，使用者需要注意这个情况
     * 注意：本实现对活动结束后15分钟内的消息仍然往下分发，超过15分钟的才拒绝返回
     *
     * @author: 郭立平[<EMAIL>]
     * @date: 2021/3/15 16:16
     **/
    private boolean checkActivityStatus(long actId, Object event) {
        // 只处理活动模板内的活动（包括部署分组匹配检查），若不是直接返回 false
        if (!actInfoService.isMyHdzjActivityDuty(actId)) {
            return false;
        }

        // 若活动中台对应的活动状态无效，直接返回 false
        ActivityInfoVo actInfo = hdztRankingThriftClient.queryActivityInfo(actId);
        if (!Const.isOk1(actInfo.getStatus())) {
            return false;
        }

        // 活动没有开始，直接返回 false
        long now = commonService.getNow(actId).getTime();
        if (now < actInfo.getBeginTime()) {
            return false;
        }
        long endTime = Convert.toLong(cacheService.getActAttrValue(actId, String.format(GeActAttrConst.COMPONENT_ACT_END_TIME, event.getClass().getName()), actInfo.getEndTime()+""));

        // 活动结束超过指定时长，打错误告警日志并返回 false
        long seconds = 900;
        long diff = (now - endTime) / 1000;
        if (diff > seconds) {
            log.error("notify warning！！！ activity:{} has finished {} > {} seconds,reject event:{}", actId, diff, seconds, event.getClass().getName());
            return false;
        }

        // 【活动开始后 ~ 活动结束+900秒】 的消息才允许 notify
        return true;
    }

    private boolean processActComponent(long actId, Object event, Class<?> eventClass, ActComponent actComponent, ComponentAttr componentAttr, boolean fromRetry) {
        Set<HdzjMethodWrapper> methods = ElementManager.getHandlerMethods(actComponent, eventClass);

        if (!CollectionUtils.isEmpty(methods)) {
            int successCount = 0;
            for (HdzjMethodWrapper method : methods) {
                // 非重试 或者 可重试,才执行组件方法
                if (!fromRetry || method.isCanRetry()) {
                    boolean result = call("component", method.getMethod(), actComponent, actId, event, componentAttr);
                    successCount += (result ? 1 : 0);
                } else {
                    // 记录告警日志
                    log.error("重试调用非幂等方法，跳过不执行,actId={},event={},component={},method={}",
                            actId, JSON.toJSONString(event), actComponent.getComponentId(), method.getMethod().getName());
                    successCount++;
                }
            }
            return successCount == methods.size();
        }

        return true;
    }

    private boolean call(String tag, Method method, Object invoker, long actId, Object... args) {
        if (method != null) {
            try {
                // 不影响其它handler
                method.invoke(invoker, args);
            } catch (Throwable e) {
                String name = invoker.getClass().getName() + "." + method.getName();
                log.error("call exception@tag:{}, actId:{}, method:{}, args:{}, err:{} ", tag, actId, name, args, e.getMessage(), e);

                return false;
            }
        }

        return true;
    }

    @Override
    public Object execute(RetryContext context, EventNotifyParam param) throws Throwable {


        //TODO 解决fostress client 自加入trace id失效问题后，这里去掉!!!
        MDCUtils.putContext("fostress");

        Span span = TRACER.spanBuilder("fostress").startSpan();
        try (Scope ignored = span.makeCurrent()) {

            // 转成对应类型
            param.setEvent(JSON.parseObject(JSON.toJSONString(param.getEvent()), param.getEventClass()));
            List<HandlerBean> handlerBeans = ElementManager.getHandlerBeans(param.getActId());
            param.setHandlerBeans(handlerBeans);

//            for (HandlerBean handlerBean : param.getHandlerBeans()) {
//                handlerBean.setActComponent((ActComponent) SpringBeanAwareFactory.getBean(handlerBean.getBeanName()));
//            }

            List<Future<Boolean>> futures = handleComponent(param, true);
            for (Future<Boolean> future : futures) {
                if (!future.get()) {
                    throw new RuntimeException("handleComponent fail");
                }
            }
        } finally {
            span.end();
        }


        //TODO 解决fostress client 自加入trace id失效问题后，这里去掉!!!
        MDCUtils.clearContext();



        return 1;
    }

    public boolean canNotify(long actId, Object event) {
        //1. 判断 消息是否在通知名单列表 2. 判断活动是否允许其通知
        String notifyEventBlackList = cacheService.
                getActAttrValue(actId, GeActAttrConst.COMPONENT_ACT_NOTIFY_BLACK_LIST, "");
        notifyEventBlackList = notifyEventBlackList + CacheService.LIMIT_NOTIFY_EVENT_OBJECTS;
        if(notifyEventBlackList.contains(event.getClass().getName())) {
            return Const.ONESTR.equals(cacheService.getActAttrValue(actId, String.format(GeActAttrConst.COMPONENT_NOTIFY_OP, event.getClass().getName()), "0"));
        }
        return true;
    }

    @Override
    public Class<EventNotifyParam> getMsgClass() {
        return EventNotifyParam.class;
    }
}
