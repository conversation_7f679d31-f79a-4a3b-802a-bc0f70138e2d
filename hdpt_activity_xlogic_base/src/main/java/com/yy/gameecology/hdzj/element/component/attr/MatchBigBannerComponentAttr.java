package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvagConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvgaTextConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.MatchBigBannerItem;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-02-26 20:25
 **/
@Data
public class MatchBigBannerComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "监听榜单结束事件的榜单Id")
    private long eventSettleRankId;

    @ComponentAttrField(labelText = "监听榜单结束事件的阶段Id")
    private long eventSettlePhaseId;

    @ComponentAttrField(labelText = "延迟广播毫秒")
    private int delayMill;

    @ComponentAttrField(labelText = "需要广播的榜单配置"
            , subFields = {
            @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "榜单Id"),
            @SubField(fieldName = Constant.VALUE, type = MatchBigBannerItem.class)
    })
    private Map<Long, MatchBigBannerItem> settleRankConfig = Maps.newLinkedHashMap();


    @ComponentAttrField(labelText = "横幅类型，自定义")
    private String bannerType;

    @ComponentAttrField(labelText = "横幅id，自定义")
    private Long bannerId;

    @ComponentAttrField(labelText = "成员信息业务类型", remark = "控制成员信息读取和广播范围")
    private long busiId;

    @ComponentAttrField(labelText = "成员类型")
    private int roleType;

    @ComponentAttrField(labelText = "app横幅广播svga/mp4", remark = "横幅显示的成员元素个数不一样，素材也不一样"
            , subFields = {
            @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "成员数量"),
            @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "url")
    })
    private Map<Integer, String> appBannerSvgaUrl;

    @ComponentAttrField(labelText = "app横幅推送业务", remark = "需要推送的业务类型 1 -语音房 2 -交友房 4 -其他 8 -宝贝 位域表示 支持组合：交友and宝贝：2+8")
    private int appBannerBusiId;

    @ComponentAttrField(labelText = "mp4广播类型", remark = "1-子频道广播 2-顶级频道下所有子厅广播 3 -全服广播 4 -全平台广播 5 -全家族广播 6 -单播")
    private int bcType;

    @ComponentAttrField(labelText = "svgakey文字映射"
            , subFields = {
            @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "数据索引下标"),
            @SubField(fieldName = Constant.MAP_LIST_VALUE, type = BannerSvgaTextConfig.class, labelText = "text配置")
    })
    private Map<Integer, List<BannerSvgaTextConfig>> appBannerTextTemplate;


    @ComponentAttrField(labelText = "svga图片key",
            subFields = {@SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "数据索引下标"),
                    @SubField(fieldName = Constant.KEY2, type = String.class, labelText = "imgkey"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "图片地址")})
    private Map<Integer, Map<String, String>> appBannerImgLayers;


}
