package com.yy.gameecology.hdzj;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.service.CacheService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.db.model.gameecology.HdzjComponent;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.HandlerBean;
import com.yy.gameecology.hdzj.bean.HdzjMethodWrapper;
import com.yy.gameecology.hdzj.element.ActComponent;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 元素（组件、任务、福利）管理类
 *
 * <AUTHOR>
 * @date 2021/2/4 14:33
 */
@SuppressWarnings("ALL")
@Component
@DependsOn("springBeanAwareFactory")
public class ElementManager {

    private Logger log = LoggerFactory.getLogger(this.getClass());

    private long count = 0;

    @Autowired
    private CacheService cacheService;

    /**
     * 存放各种处理器： key：actId， val：list是 cmptId + cmptUseInx 限定的组件对象
     */
    private static Map<Long, List<HandlerBean>> handlerBeanMap = Maps.newConcurrentMap();

    /**
     * 存放处理器下 TplEventHandler 标注的方法对象
     */
    private static Map<Object, Map<Class<?>, Set<HdzjMethodWrapper>>> methodMap = Maps.newConcurrentMap();

    private Map<String, ActComponent> actComponentMap = Maps.newHashMap();

    @Autowired
    public void setActComponentMap(Map<String, ActComponent> actComponentMap) {
        this.actComponentMap = actComponentMap;
    }

    @PostConstruct
    @NeedRecycle(author = "guoliping", notRecycle = true)
    void init() {
        // 1.解析提取出所有的组件注解处理方法
        for (ActComponent actComponent : actComponentMap.values()) {
            savehandlerMethods(actComponent);
        }

        // 3.刷新活动组件、部件配置
        refresh();
    }

    @Scheduled(cron = "0/20 * * * * ?")
    @NeedRecycle(author = "guoliping", notRecycle = true)
    synchronized public void refresh() {
        count++;
        Clock clock = new Clock();
        try {
            // 让测试环境加快更新， 生产环境为20秒 x 3 = 60秒 = 1分钟
            final int three = 3;
            if (SysEvHelper.isDeploy() && (count % three != 1)) {
                return;
            }

            // 初始化组件, TODO:: 前期所有活动id都扫描，后面数据大了，可以滤除活动已结束3个月以上的活动
            Map<Long, List<HandlerBean>> handlerBeans = Maps.newConcurrentMap();
            for (Object elm : actComponentMap.values()) {
                if (elm instanceof ActComponent) {
                    this.register((ActComponent) elm, handlerBeans);
                }
            }

            // 对象引用赋值，保证原子无并发问题
            handlerBeanMap = handlerBeans;
            log.info("{}) refresh ok@handlerBeanMap:{}, methodMap:{} {}", count, handlerBeanMap.size(), methodMap.size(), clock.tag());
        } catch (Throwable t) {
            log.error("{}) refresh exception@err:{} {}", count, t.getMessage(), clock.tag(), t);
        }
    }

    /**
     * 注册组件处理器，禁止覆盖
     */
    private void register(ActComponent actComponent, Map<Long, List<HandlerBean>> handlerBeans) {
        Long componentId = actComponent.getComponentId();
        if (componentId == null) {
            log.warn("{}) register component fail@{} componentId is null", count, actComponent.getClass().getName());
            return;
        }

        Set<Long> actIds = actComponent.getActivityIds();
        for (Long actId : actIds) {
            Clock clock = new Clock();
            saveActComponentHandler(actId, actComponent, handlerBeans);
            log.info("{}) register component done@actId:{}, componentId:{} {}", count, actId, componentId, clock.tag());
        }
    }

    /**
     * 获取活动相关的所有处理器
     */
    public static List<HandlerBean> getHandlerBeans(Long actId) {
        return handlerBeanMap.get(actId);
    }

    /**
     * 获取处理器对应的方法集合
     */
    public static Set<HdzjMethodWrapper> getHandlerMethods(Object invoker, Class<?> clazz) {
        Map<Class<?>, Set<HdzjMethodWrapper>> map = methodMap.get(invoker);
        return CollectionUtils.isEmpty(map) ? null : map.get(clazz);
    }

    /**
     * 查找活动的 cmptUseInx 组件对象
     */
    public ActComponent getActComponent(Long actId, long cmptId, long cmptUseInx) {
        List<HandlerBean> handlerBeanList = ElementManager.handlerBeanMap.get(actId);
        if (!CollectionUtils.isEmpty(handlerBeanList)) {
            for (HandlerBean handlerBean : handlerBeanList) {
                if (handlerBean.getActComponent().getComponentId() == cmptId && handlerBean.getCmptUseInx() == cmptUseInx) {
                    return handlerBean.getActComponent();
                }
            }
        }
        return null;
    }

    /**
     * 查找活动的组件对象，不限定 cmptUseInx
     */
    public ActComponent getActComponent(Long actId, long cmptId) {
        List<HandlerBean> handlerBeanList = ElementManager.handlerBeanMap.get(actId);
        if (!CollectionUtils.isEmpty(handlerBeanList)) {
            for (HandlerBean handlerBean : handlerBeanList) {
                if (handlerBean.getActComponent().getComponentId() == cmptId) {
                    return handlerBean.getActComponent();
                }
            }
        }
        return null;
    }

    private void saveActComponentHandler(Long actId, ActComponent actComponent, Map<Long, List<HandlerBean>> handlerBeans) {
        Long cmptId = actComponent.getComponentId();
        if (cmptId == null) {
            return;
        }

        List<HandlerBean> list = handlerBeans.computeIfAbsent(actId, k -> Lists.newArrayList());
        if (actComponent.isUniq1UseIndex()) {
            HdzjComponent hdzjComponent = cacheService.getHdzjComponent(actId, cmptId, 1);
            if (hdzjComponent != null && Const.isOk1(hdzjComponent.getStatus())) {
                list.add(new HandlerBean(actId, actComponent, hdzjComponent.getCmptUseInx()));
            }
        } else {
            Map<String, HdzjComponent> hdzjComponents = cacheService.getHdzjComponentMap(actId);
            for (String key : hdzjComponents.keySet()) {
                HdzjComponent hdzjComponent = hdzjComponents.get(key);
                if (cmptId.equals(hdzjComponent.getCmptId()) && Const.isOk1(hdzjComponent.getStatus())) {
                    list.add(new HandlerBean(actId, actComponent, hdzjComponent.getCmptUseInx()));
                }
            }
        }
    }

    /**
     * 分析注解方法，保存到map中，方便快速检索调用
     *
     * @author: 郭立平[<EMAIL>]
     * @date: 2021/2/23 16:09
     **/
    static private void savehandlerMethods(Object object) {
        Class<? extends Object> clazz = getRealClass(object);
        Method[] methods = clazz.getMethods();
        for (Method method : methods) {
            Class<?>[] parameterTypes = method.getParameterTypes();
            if (!isValidHanderMethod(object, method, parameterTypes)) {
                continue;
            }
            // 组装，登记

            HdzjEventHandler methodAnnotation = method.getAnnotation(HdzjEventHandler.class);
            HdzjMethodWrapper methodWrapper = new HdzjMethodWrapper(method, methodAnnotation.canRetry());
            Map<Class<?>, Set<HdzjMethodWrapper>> maps = methodMap.computeIfAbsent(object, k -> Maps.newHashMap());
            Set<HdzjMethodWrapper> set = maps.computeIfAbsent(parameterTypes[0], k -> Sets.newHashSet());
            set.add(methodWrapper);
        }
    }

    /**
     * 检查是否有效的处理方法
     */
    static private boolean isValidHanderMethod(Object object, Method method, Class<?>[] parameterTypes) {
        // 方法基本要求检查
        final int two = 2;
        if (parameterTypes.length != two) {
            return false;
        }
        final String voidStr = "void";
        if (!Modifier.isPublic(method.getModifiers()) || !voidStr.equals(method.getReturnType().getName())) {
            return false;
        }
        HdzjEventHandler methodAnnotation = method.getAnnotation(HdzjEventHandler.class);
        if (methodAnnotation == null) {
            return false;
        }

        // object 必须是 ActComponent 的实例
        if (!(object instanceof ActComponent)) {
            return false;
        }

        // 参数 2 必须是  ComponentAttr 或 其子类
        if (!ComponentAttr.class.isAssignableFrom(parameterTypes[1])) {
            return false;
        }

        // 检查注解类中是否含有 参数1的类型
        Class<?>[] annotationClasses = methodAnnotation.value();
        if (annotationClasses != null) {
            for (Class<?> annotationClass : annotationClasses) {
                if (annotationClass.equals(parameterTypes[0])) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 获取对象的真实类（不是代理类）
     *
     * @author: 郭立平[<EMAIL>]
     * @date: 2021/2/23 16:09
     */
    public static Class<? extends Object> getRealClass(Object object) {
        Class<? extends Object> clazz = object.getClass();
        // 假定代理类名上总含 $ 符号，这个看情况而定！
        while (clazz.getName().contains(StringUtil.DOLLAR)) {
            clazz = clazz.getSuperclass();
        }
        return clazz;
    }
}
