package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.service.ActInfoService;
import com.yy.gameecology.activity.service.DelayQueueService;
import com.yy.gameecology.activity.service.DelaySvcSDKServiceV2;
import com.yy.gameecology.common.bean.ChannelBaseInfo;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.PBCommonNoticeType;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.JsonUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.*;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.InterstellarTreasureComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.PersonalTaskComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardProp;
import com.yy.gameecology.hdzj.utils.BusinessUtils;
import com.yy.gameecology.hdzj.utils.JSONUtils;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztranking.MemberRankingInfo;
import com.yy.thrift.hdztranking.MemberRankingInfoRequest;
import com.yy.thrift.hdztranking.QueryUserTaskResponse;
import com.yy.thrift.hdztranking.UserTaskItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/inter_treasure")
public class InterstellarTreasureComponent extends BaseActComponent<InterstellarTreasureComponentAttr> {

    private static final String DELAY_HANDLE_TASK_COMPLETED = "delay_handle_task_completed";

    private static final String DELAY_LOTTERY = "delay_do_lottery";

    private static final String LOTTERY_SIGN_UP_LIST = "lottery_sign_up_list|%d|%d";

    private static final String LOTTERY_SIGN_UP_END_TAG = "lottery_sign_up_end";

    private static final String LOTTERY_AWARD_RECORD = "lottery_award_record|%d|%d";

    private static final String USER_AWARD_SEQ = "seq:user_award:%d:%d:%d:%d:%d";

    private static final String OP_TYPE_SIGN_UP_LOTTERY = "signUpLottery";

    private static final String OP_TYPE_QUERY_USER_AWARD = "queryUserAward";

    private static final String LAST_JOB_TIME = "last_job_time:%s";

    private static final String ADD_JOB_LOCK = "add_job_lock:%s";

    private static final String LUA_SCRIPT =
            "if redis.call('HEXISTS', KEYS[3], KEYS[4]) == 1 then\n" +
                    "    return -1\n" +
                    "end\n" +
                    "return redis.call('ZADD', KEYS[1], ARGV[1], KEYS[2])";

    private DefaultRedisScript<Long> script = new DefaultRedisScript<>(LUA_SCRIPT, Long.class);

    @Autowired
    private DelayQueueService delayQueueService;

    @Autowired
    private DelaySvcSDKServiceV2 delaySvcSDKServiceV2;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private ActInfoService actInfoService;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private PersonalTaskComponent personalTaskComponent;


    private Set<Long> initActIds = Sets.newHashSet();


    @Override
    public Long getComponentId() {
        return ComponentId.INTERSTELLAR_TREASURE;
    }

    @Scheduled(cron = "0 0/1 * * * ?")
    @PostConstruct
    @NeedRecycle(notRecycle = true, author = "liqingyang")
    public void init() {
        List<ActivityInfoVo> activityInfoVos = hdztRankingThriftClient.queryEffectActInfos();
        if (CollectionUtils.isNotEmpty(activityInfoVos)) {
            for (ActivityInfoVo actInfo : activityInfoVos) {
                long actId = actInfo.getActId();
                if (!actInfoService.inActTime(commonService.getNow(actId), actInfo)) {
                    continue;
                }

                //发布的时候可能活动还没开始，所以这里要用定时器来初始化，为避免重复初始化，这里加个去重！！！
                if (initActIds.contains(actId)) {
                    log.info("startDelaySettlement skip,actId:{}", actId);
                    continue;
                }
                initActIds.add(actId);


                //完成任务，初始化数据，抽奖弹窗广播
                final String delayKey1 = Const.addActivityPrefix(actId, DELAY_HANDLE_TASK_COMPLETED);
                delayQueueService.startDelaySettlement(delayKey1, 1, InterstellarTreasureTaskEvent.class, this::handleTaskCompleted);
                //给参与抽奖的用户开奖
                final String delayKey2 = Const.addActivityPrefix(actId, DELAY_LOTTERY);
                delayQueueService.startDelaySettlement(delayKey2, 1, InterstellarTreasureTaskEvent.class, this::doLottery);

                log.info("startDelaySettlement done,actId:{}", actId);
            }
        }

    }


    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = false)
    public void onTaskProgressChanged(TaskProgressChanged event, InterstellarTreasureComponentAttr attr) {
        if (event.getRankId() != attr.getTaskRankId()) {
            log.info("onTaskProgressChanged rankId:{} not match", event.getRankId());
            return;
        }
        log.info("onTaskProgressChanged, task:{}", JSONUtils.toJsonString(event));
        long startIndex = event.getStartTaskIndex(), currIndex = event.getCurrTaskIndex();
        if (startIndex == currIndex) {
            return;
        }

        final long actId = event.getActId();
        final String delayKey = Const.addActivityPrefix(actId, DELAY_HANDLE_TASK_COMPLETED);
        long sid = Long.parseLong(event.getMember());
        if(attr.getPersonalComptid() > 0) {
            PersonalTaskComponentAttr personalTaskComponentAttr = personalTaskComponent.getComponentAttr(attr.getActId(), attr.getPersonalComptid());
            long target = personalTaskComponent.getPersonalTaskTarget(personalTaskComponentAttr, sid+"");
            int targetLevel = personalTaskComponentAttr.getTargetLevel(target);
            if(targetLevel > currIndex) {
                log.info("sid:{}, targetLevel:{}, currIndex:{} cant pass", sid, targetLevel, currIndex);
                return;
            }

            //不要补发低级宝箱
            if(startIndex < targetLevel) {
                startIndex = targetLevel - 1;
            }
        }

        Secret lock = null;
        String lockName = makeKey(attr, String.format(ADD_JOB_LOCK, sid));
        lock = locker.lock(lockName, 5, event.getSeq(), 10);
        try {
            if (lock != null) {
                String lastJobKey = makeKey(attr, String.format(LAST_JOB_TIME, sid));
                String groupCode = redisConfigManager.getGroupCode(actId);
                long lastJobTime = Convert.toLong(actRedisDao.getRedisTemplate(groupCode).opsForValue().get(lastJobKey));
                long now = System.currentTimeMillis();
                log.info("lastJobTime:{}, now:{}", lastJobTime, now);
                long delaySecs = lastJobTime == 0L || lastJobTime - now <= 0 ?
                        0L : (lastJobTime - now) / 1000;
                for (long taskId = startIndex + 1; taskId <= currIndex; taskId++) {
                    InterstellarTreasureTaskEvent delayEvent = new InterstellarTreasureTaskEvent(actId, attr.getCmptUseInx(), sid, taskId);
                    if (delaySecs == 0) {
                        handleTaskCompleted(delayEvent);
                    } else {
                        delayQueueService.setDelayEvent(delayKey, delaySecs, delayEvent);
                        log.info("onTaskProgressChanged setDelayEvent with delaySecs:{}, delayEvent:{}", delaySecs, delayEvent);
                    }
                    delaySecs += (17 + attr.getLotteryAwaitSec());
                    actRedisDao.getRedisTemplate(groupCode).opsForValue().set(lastJobKey, System.currentTimeMillis() + (delaySecs * 1000) +"");
                }
            } else {
                log.error("get lock error event:{}", JsonUtil.toJson(event));
            }
        } catch (Exception e) {
            log.error("onTaskProgressChanged error {}", e.getMessage(), e);
        } finally {
            locker.unlock(lockName, lock, 5);
        }
    }

    public void handleTaskCompleted(InterstellarTreasureTaskEvent event) {
        log.info("handleTaskCompleted with event:{}", event);
        final long actId = event.getActId(), cmptUseInx = event.getCmptUseInx(), sid = event.getSid(), taskId = event.getTaskId();
        InterstellarTreasureComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        if (attr == null) {
            return;
        }

        ChannelBaseInfo channelInfo = commonService.getChannelInfo(sid, false);
        if (channelInfo == null) {
            log.error("handleTaskCompleted could not find channel info sid:{}", sid);
            return;
        }

        //设置延迟抽奖
        final String delayKey = Const.addActivityPrefix(event.getActId(), DELAY_LOTTERY);
        delayQueueService.setDelayEvent(delayKey, attr.getLotteryAwaitSec(), event);

        //发送顶级频道弹窗
        JSONObject json = new JSONObject(5);
        json.put("cmptUseInx", cmptUseInx);
        json.put("taskId", taskId);
        json.put("taskName", attr.getTaskNameMap().get(taskId));
        json.put("lotteryAwaitSec", attr.getLotteryAwaitSec());
        json.put("giftName", attr.getGiftName());
        json.put("giftIcon", attr.getGiftIcon());

        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(actId)
                .setNoticeType(PBCommonNoticeType.INTERSTELLAR_TREASURE_POPUP)
                .setNoticeValue(json.toJSONString());

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();

        svcSDKService.broadcastTop(sid, msg);
        log.info("handleTaskCompleted send top sid broadcast with json:{}", json);

        //发送全模板底部横幅
        Template template = getTemplateByBusiId(attr.getBusiId());
        if (template != null) {
            JSONObject jsonData = new JSONObject(5);
            final long taskMileage = attr.getTaskPassValueMap().get(taskId);
            jsonData.put("taskId", taskId);
            jsonData.put("taskMileage", taskMileage);
            jsonData.put("taskName", attr.getTaskNameMap().get(taskId));
            String content = String.format(attr.getBroadcastTpl(), channelInfo.getAsid(), (taskMileage / 10000) + "万", attr.getTaskNameMap().get(taskId));
            jsonData.put("content", content);
            GameecologyActivity.BannerBroadcast broadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                    .setActId(actId)
                    .setBannerId(15)
                    .setBannerType(0)
                    .setSid(sid)
                    .setAsid(channelInfo.getAsid())
                    .setChannelLogo(channelInfo.getLogo())
                    .setChannelName(channelInfo.getName())
                    .setJsonData(jsonData.toJSONString())
                    .build();

            GameecologyActivity.GameEcologyMsg bannerMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                    .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                    .setBannerBroadcast(broadcast)
                    .build();
            svcSDKService.broadcastTemplate(template, bannerMsg);

            log.info("handleTaskCompleted send template cast banner with jsonData:{}", jsonData);
        }
    }

    public static Template getTemplateByBusiId(int busiId) {
        return BusinessUtils.getTemplateByBusiId(busiId);
    }

    public Response<Integer> signUpLottery(long actId, long cmptUseInx, long sid, long taskId, long uid) {
        InterstellarTreasureComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        if (attr == null) {
            return Response.fail(400, "活动参数错误！");
        }
        log.info("signUpLottery begin,actId:{},cmptUseInx:{},sid:{},taskId:{},uid:{}", actId, cmptUseInx, sid, taskId, uid);

        long contributeRankId = attr.getContributeRankId();

        String groupCode = redisConfigManager.getGroupCode(actId);
        String endKey = makeKey(attr, LOTTERY_SIGN_UP_END_TAG);
        String hashKey = sid + "|" + taskId;
        boolean hasKey = actRedisDao.getRedisTemplate(groupCode).opsForHash().hasKey(endKey, hashKey);
        if (hasKey) {
            log.info("signUpLottery time out,actId:{},cmptUseInx:{},sid:{},taskId:{},uid:{}", actId, cmptUseInx, sid, taskId, uid);
            return Response.fail(400, "手慢了，参与倒计时已结束！");
        }

        MemberRankingInfoRequest request = new MemberRankingInfoRequest();
        request.setActId(actId);
        request.setRankingId(contributeRankId);
        request.setPhaseId(attr.getPhaseId());
        request.setMemberIds(Collections.singletonList(String.valueOf(uid)));
        request.setSubKeyMember(String.valueOf(sid));
        MemberRankingInfo rankingInfo = hdztRankingThriftClient.queryMemberRankingInfo(request).get(String.valueOf(uid));
        if (rankingInfo == null || rankingInfo.getScore() <= 0) {
            log.info("signUpLottery could not get member rank score taskId:{}, sid:{}, uid:{}", taskId, sid, uid);
            return Response.success(0, "暂不符合抽奖条件");
        }

        //增加
        final String key = makeKey(attr, String.format(LOTTERY_SIGN_UP_LIST, sid, taskId));
        List<String> keys = ImmutableList.of(key, String.valueOf(uid), endKey, hashKey);
        Long rs = actRedisDao.getRedisTemplate(groupCode).execute(script, keys, String.valueOf(rankingInfo.score));
        if (rs == null || rs < 0) {
            log.info("signUpLottery time out,actId:{},cmptUseInx:{},sid:{},taskId:{},uid:{}", actId, cmptUseInx, sid, taskId, uid);
            return Response.fail(400, "手慢了，参与倒计时已结束！");
        }
        log.info("signUpLottery done,actId:{},cmptUseInx:{},sid:{},taskId:{},uid:{}", actId, cmptUseInx, sid, taskId, uid);
        return Response.success(1, "已成功参与抽取宝箱");
    }

    public Response<JSONObject> queryUserAward(long actId, long cmptUseInx, long sid, long taskId, long uid) {
        InterstellarTreasureComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        if (attr == null) {
            return Response.fail(400, "活动参数错误！");
        }

        final String groupCode = redisConfigManager.getGroupCode(actId);
//        final String key = makeKey(attr, String.format(LOTTERY_SIGN_UP_LIST, sid, taskId));
//        Double score = actRedisDao.getRedisTemplate(groupCode).opsForZSet().score(key, String.valueOf(uid));
//        if (score == null || score <= 0) {
//            return Response.fail(400, "您还没有参与此次抽奖！");
//        }

        final String awardRecordKey = makeKey(attr, String.format(LOTTERY_AWARD_RECORD, sid, taskId));
        Object value = actRedisDao.getRedisTemplate(groupCode).opsForHash().get(awardRecordKey, String.valueOf(uid));
        long awardPackageId = Convert.toLong(value, 0);
        AwardProp awardProp = attr.getAwardPropMap().get(awardPackageId);
        JSONObject json = new JSONObject(6);
        json.put("taskId", taskId);
        if (awardProp == null) {
            json.put("lotteryHit", 0);
        } else {
            json.put("lotteryHit", 1);
            json.put("awardName", awardProp.getAwardName());
            json.put("awardIcon", awardProp.getAwardIcon());
            json.put("awardCount", 1);
        }

        return Response.success(json);
    }

    public Response<List<JSONObject>> queryTaskAwardMember(long actId, long cmptUseInx, long sid, long taskId) {
        InterstellarTreasureComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        if (attr == null) {
            return Response.fail(400, "活动参数错误！");
        }

        final String groupCode = redisConfigManager.getGroupCode(actId);
        final String awardRecordKey = makeKey(attr, String.format(LOTTERY_AWARD_RECORD, sid, taskId));
        Map<Object, Object> awards = actRedisDao.getRedisTemplate(groupCode).opsForHash().entries(awardRecordKey);

        if (MapUtils.isEmpty(awards)) {
            return Response.success(Collections.emptyList());
        }

        List<JSONObject> data = new ArrayList<>(awards.size());
        for (Map.Entry<Object, Object> entry : awards.entrySet()) {
            long uid = Convert.toLong(entry.getKey());
            long packageId = Convert.toLong(entry.getValue());

            UserBaseInfo userBaseInfo = commonService.getUserInfo(uid, false);
            AwardProp prop = attr.getAwardPropMap().get(packageId);
            JSONObject item = new JSONObject(3);
            item.put("uid", uid);
            item.put("award", prop);
            item.put("nick", userBaseInfo != null ? userBaseInfo.getNick() : uid);
            item.put("yyno", userBaseInfo != null ? userBaseInfo.getYyno() : uid);

            data.add(item);
        }

        return Response.success(data);
    }

    @Override
    public CommonPBOperateResp commonOperatePbRequest(CommonPBOperateRequest request) {
        CommonPBOperateResp resp = new CommonPBOperateResp();
        long cmptIndex = request.getCmptIndex();
        InterstellarTreasureComponentAttr attr = getComponentAttr(request.getActId(), cmptIndex);
        if (attr == null) {
            resp.setCode(1);
            resp.setMsg("活动参数错误");
            return resp;
        }

        if (OP_TYPE_SIGN_UP_LOTTERY.equals(request.getOpType())) {
            long taskId = Convert.toLong(request.getOpId());
            Response<Integer> response = this.signUpLottery(request.getActId(), cmptIndex, request.getOpSid(), taskId, request.getOpUid());
            resp.setContent(String.valueOf(response.getData()));
            resp.setCode(response.getResult());
            resp.setMsg(response.getReason());
        } else if (OP_TYPE_QUERY_USER_AWARD.equals(request.getOpType())) {
            long taskId = Convert.toLong(request.getOpId());
            Response<JSONObject> response = this.queryUserAward(request.getActId(), cmptIndex, request.getOpSid(), taskId, request.getOpUid());
            resp.setCode(response.getResult());
            resp.setMsg(response.getReason());
            if (response.getData() != null) {
                resp.setContent(response.getData().toJSONString());
            } else {
                resp.setContent("{}");
            }
        } else {
            resp.setCode(1);
            resp.setMsg("非法请求参数");
        }

        return resp;
    }

    public Response<InterstellarTreasureTaskInfo> doQueryTaskInfo(long actId, long cmptUseInx, long sid) {
        InterstellarTreasureComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        if (attr == null) {
            return Response.fail(400, "活动参数错误！");
        }

        QueryUserTaskResponse response = hdztRankingThriftClient.queryUserTaskInfo(actId, attr.getTaskRankId(), attr.getPhaseId(), StringUtils.EMPTY, String.valueOf(sid));
        if (response == null || response.code != 0 || MapUtils.isEmpty(response.items)) {
            return Response.fail(500, "活动配置错误！");
        }

        InterstellarTreasureTaskInfo taskInfo = new InterstellarTreasureTaskInfo();
        UserTaskItem taskItem = response.items.values().stream().findFirst().get();
        taskInfo.setCurrTaskId(taskItem.curTaskId);
        taskInfo.setCurrMileage(taskItem.curTaskScore);
        taskInfo.setCurrTaskName(taskItem.taskName);
        taskInfo.setSid(sid);
        taskInfo.setAsid(commonService.getAsid(sid));
        Map<Long, Long> passValueMap = attr.getTaskPassValueMap();
        Map<Long, String> taskNameMap = attr.getTaskNameMap();
        Map<Long, Long> channelAwardAmountMap = attr.getChannelAwardAmountMap();

        List<InterstellarTreasureTaskItem> items = passValueMap.keySet().stream().sorted().map(taskId -> {
            InterstellarTreasureTaskItem item = new InterstellarTreasureTaskItem();
            item.setTaskId(taskId);
            item.setTaskName(taskNameMap.get(taskId));
            item.setPassMileage(passValueMap.get(taskId));
            item.setChannelAwardAmount(channelAwardAmountMap.get(taskId));
            item.setChestAwards(getChestAwards(attr, taskId));

            return item;
        }).collect(Collectors.toList());
        taskInfo.setTaskItems(items);

        return Response.success(taskInfo);
    }

    private List<AwardProp> getChestAwards(InterstellarTreasureComponentAttr attr, long taskId) {
        List<AwardProp> result = new ArrayList<>(attr.getBigAwardPackageIds().size() + attr.getMiddleAwardPackageIds().size() + attr.getSmallAwardPackageIds().size());
        Map<Long, Integer> awardCount = attr.getAwardCountMap().getOrDefault(taskId, attr.getDefaultAwardCount());
        List<AwardProp> prop1 = attr.getBigAwardPackageIds().stream()
                .filter(packageId -> awardCount.get(packageId) > 0)
                .map(packageId -> attr.getAwardPropMap().get(packageId))
                .filter(Objects::nonNull).collect(Collectors.toList());

        result.addAll(prop1);

        List<AwardProp> prop2 = attr.getMiddleAwardPackageIds().stream()
                .filter(packageId -> awardCount.get(packageId) > 0)
                .map(packageId -> attr.getAwardPropMap().get(packageId))
                .filter(Objects::nonNull).collect(Collectors.toList());

        result.addAll(prop2);

        List<AwardProp> prop3 = attr.getSmallAwardPackageIds().stream()
                .filter(packageId -> awardCount.get(packageId) > 0)
                .map(packageId -> attr.getAwardPropMap().get(packageId))
                .filter(Objects::nonNull).collect(Collectors.toList());

        result.addAll(prop3);

        return result;
    }

    private void doLottery(InterstellarTreasureTaskEvent event) {
        final long actId = event.getActId(), cmptUseInx = event.getCmptUseInx(), sid = event.getSid(), taskId = event.getTaskId();
        InterstellarTreasureComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        if (attr == null) {
            return;
        }

        String groupCode = redisConfigManager.getGroupCode(actId);

        //开奖，设置一个key 防止再有用户点击参与
        String endKey = makeKey(attr, LOTTERY_SIGN_UP_END_TAG);
        final String key = makeKey(attr, String.format(LOTTERY_SIGN_UP_LIST, sid, taskId));
        String hashKey = sid + "|" + taskId;
        boolean put = actRedisDao.getRedisTemplate(groupCode).opsForHash().putIfAbsent(endKey, hashKey, StringUtil.ONE);
        if (!put) {
            return;
        }

        Map<Long, Integer> awardCount = attr.getAwardCountMap().getOrDefault(taskId, attr.getDefaultAwardCount());

        //抽大奖
        Set<Long> bigAwardPackageIds = attr.getBigAwardPackageIds();
        if (CollectionUtils.isNotEmpty(bigAwardPackageIds)) {
            Set<String> tuples = actRedisDao.getRedisTemplate(groupCode).opsForZSet().reverseRange(key, 0, attr.getBAwardCandidateCount() - 1);
            Map<String, Long> awardMembers = lotteryFromCandidates(tuples, bigAwardPackageIds, awardCount);
            log.info("doLottery big award welfare members:{}", awardMembers);

            if (MapUtils.isNotEmpty(awardMembers)) {
                //发奖
                doWelfareAward(awardMembers, sid, taskId, attr);
            }
        }

        //抽中奖
        Set<Long> middleAwardPackageIds = attr.getMiddleAwardPackageIds();
        if (CollectionUtils.isNotEmpty(middleAwardPackageIds)) {
            Set<String> tuples = actRedisDao.getRedisTemplate(groupCode).opsForZSet().reverseRangeByScore(key, attr.getMAwardThreshold(), Long.MAX_VALUE);
            Map<String, Long> awardMembers = lotteryFromCandidates(tuples, middleAwardPackageIds, awardCount);
            log.info("doLottery middle award welfare members:{}", awardMembers);

            if (MapUtils.isNotEmpty(awardMembers)) {
                //发奖
                doWelfareAward(awardMembers, sid, taskId, attr);
            }
        }

        //抽小奖
        Set<Long> smallAwardPackageIds = attr.getSmallAwardPackageIds();
        if (CollectionUtils.isNotEmpty(smallAwardPackageIds)) {
            int total = smallAwardPackageIds.stream().map(awardCount::get).reduce(0, Integer::sum);
            Set<String> tuples = actRedisDao.getRedisTemplate(groupCode).opsForZSet().reverseRange(key, 0, total - 1);
            Map<String, Long> awardMembers = lotteryFromCandidates(tuples, smallAwardPackageIds, awardCount);
            log.info("doLottery small award welfare members:{}", awardMembers);

            if (MapUtils.isNotEmpty(awardMembers)) {
                //发奖
                doWelfareAward(awardMembers, sid, taskId, attr);
            }
        }

        //剩余的人发谢谢参与
        Set<String> tuples = actRedisDao.getRedisTemplate(groupCode).opsForZSet().range(key, 0, -1);
        if (CollectionUtils.isNotEmpty(tuples)) {
            JSONObject json = new JSONObject(5);
            json.put("cmptUseInx", cmptUseInx);
            json.put("taskId", taskId);
            json.put("lotteryHit", 0);
            final String noticeValue = json.toJSONString();

            for (String member : tuples) {
                long uid = Long.parseLong(member);
                GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                        .setActId(actId)
                        .setNoticeType(PBCommonNoticeType.INTERSTELLAR_TREASURE_LOTTERY)
                        .setNoticeValue(noticeValue);

                GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                        .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                        .setCommonNoticeResponse(panel).build();

                svcSDKService.unicastUid(uid, msg);
            }
            log.info("doLottery send lottery not hit member size:{}", tuples.size());
        }

    }

    private Map<String, Long> lotteryFromCandidates(Set<String> candidates, Set<Long> packageIds, Map<Long, Integer> awardCount) {
        int total = packageIds.stream().map(awardCount::get).reduce(0, Integer::sum);
        if (total <= 0 || CollectionUtils.isEmpty(candidates)) {
            return Collections.emptyMap();
        }

        Map<String, Long> result = new HashMap<>(total);

        List<String> members = new LinkedList<>(candidates);
        Collections.shuffle(members);
        for (long packageId : packageIds) {
            if (members.size() <= 0) {
                break;
            }

            int count = awardCount.getOrDefault(packageId, 0);
            if (count <= 0) {
                continue;
            }

            for (int i = 0; i < count; i++) {
                if (members.size() <= 0) {
                    break;
                }

                String member = members.remove(0);
                result.put(member, packageId);
            }
        }

        return result;
    }

    private void doWelfareAward(Map<String, Long> awardMembers, long sid, long taskId, InterstellarTreasureComponentAttr attr) {
        //从redis 移除
        final String key = makeKey(attr, String.format(LOTTERY_SIGN_UP_LIST, sid, taskId));
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String[] values = awardMembers.keySet().toArray(new String[awardMembers.size()]);
        actRedisDao.getRedisTemplate(groupCode).opsForZSet().remove(key, values);

        //保存中奖纪录
        final String awardRecordKey = makeKey(attr, String.format(LOTTERY_AWARD_RECORD, sid, taskId));
        Map<String, String> m = awardMembers.keySet().stream().collect(Collectors.toMap(Function.identity(), member -> String.valueOf(awardMembers.get(member))));
        actRedisDao.getRedisTemplate(groupCode).opsForHash().putAll(awardRecordKey, m);

        //使用线程池发奖&单播
        final long actId = attr.getActId(), cmptUseInx = attr.getCmptUseInx();
        for (Map.Entry<String, Long> entry : awardMembers.entrySet()) {
            long uid = Long.parseLong(entry.getKey());
            threadPoolManager.get(Const.GENERAL_POOL).submit(() -> {
                String seq = String.format(USER_AWARD_SEQ, actId, cmptUseInx, taskId, sid, uid);

                //发奖
                hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), attr.getBusiId(), uid, attr.getAwardTaskId(), 1, entry.getValue(), seq, Collections.emptyMap());

                AwardProp awardProp = attr.getAwardPropMap().get(entry.getValue());
                if (awardProp != null) {
                    //发单播
                    JSONObject json = new JSONObject(5);
                    json.put("cmptUseInx", cmptUseInx);
                    json.put("taskId", taskId);
                    json.put("lotteryHit", 1);
                    json.put("awardName", awardProp.getAwardName());
                    json.put("awardIcon", awardProp.getAwardIcon());
                    json.put("awardCount", 1);

                    GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                            .setActId(actId)
                            .setNoticeType(PBCommonNoticeType.INTERSTELLAR_TREASURE_LOTTERY)
                            .setNoticeValue(json.toJSONString());

                    GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                            .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                            .setCommonNoticeResponse(panel).build();

                    svcSDKService.unicastUid(uid, msg);
                    log.info("doWelfareAward success with sid:{}, taskId:{} uid:{}, packageId:{}", sid, taskId, uid, entry.getValue());

                    //判断是最大奖，发广播
                    if (attr.getBigAwardPackageIds().contains(entry.getValue())) {
                        broLotteryBigAward(attr, uid, sid, taskId, entry.getValue());
                    }
                }
            });
        }
    }

    private void broLotteryBigAward(InterstellarTreasureComponentAttr attr, long uid, long sid, long taskId, long packageId) {
        ChannelBaseInfo channelInfo = commonService.getChannelInfo(sid, false);
        if (channelInfo == null) {
            return;
        }
        Template template = getTemplateByBusiId(attr.getBusiId());
        JSONObject jsonData = new JSONObject(5);
        String taskName = attr.getTaskNameMap().get(taskId);
        String nick = commonService.getNickName(uid, false);
        nick = nick.replace("<", "&lt;").replace(">", "&gt;");
        AwardProp awardProp = attr.getAwardPropMap().get(packageId);
        jsonData.put("taskId", taskId);
        jsonData.put("awardPackageId", packageId);
        jsonData.put("taskName", attr.getTaskNameMap().get(taskId));
        jsonData.put("awardName", awardProp.getAwardName());
        String content = String.format(attr.getBigAwardBroTpl(), nick, channelInfo.getAsid(), taskName, awardProp.getAwardName());
        jsonData.put("content", content);
        GameecologyActivity.BannerBroadcast broadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(attr.getActId())
                .setBannerId(16)
                .setBannerType(0)
                .setSid(sid)
                .setAsid(channelInfo.getAsid())
                .setChannelLogo(channelInfo.getLogo())
                .setChannelName(channelInfo.getName())
                .setJsonData(jsonData.toJSONString())
                .build();

        GameecologyActivity.GameEcologyMsg bannerMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(broadcast)
                .build();
        delaySvcSDKServiceV2.broadcastTemplate(template, bannerMsg, 3, 0);

        log.info("broLotteryBigAward send template cast banner with jsonData:{}", jsonData);
    }

    @RequestMapping("/signUpLottery")
    public Response<Integer> signUpLottery(HttpServletRequest request, HttpServletResponse response,
                                           @RequestParam(name = "actId") long actId,
                                           @RequestParam(name = "cmptUseInx", defaultValue = "400") long cmptUseInx,
                                           @RequestParam(name = "sid") long sid,
                                           @RequestParam(name = "taskId") long taskId) {
        long loginUid = getLoginYYUid(request, response);
        if (loginUid <= 0) {
            return Response.fail(401, "未登录！");
        }

        return this.signUpLottery(actId, cmptUseInx, sid, taskId, loginUid);
    }

    @GetMapping("/queryUserAward")
    public Response<JSONObject> queryUserAward(HttpServletRequest request, HttpServletResponse response,
                                               @RequestParam(name = "actId") long actId,
                                               @RequestParam(name = "cmptUseInx", defaultValue = "400") long cmptUseInx,
                                               @RequestParam(name = "sid") long sid,
                                               @RequestParam(name = "taskId") long taskId) {
        long loginUid = getLoginYYUid(request, response);
        if (loginUid <= 0) {
            return Response.fail(401, "未登录！");
        }

        return this.queryUserAward(actId, cmptUseInx, sid, taskId, loginUid);
    }

    @GetMapping("/queryTaskInfo")
    public Response<InterstellarTreasureTaskInfo> queryTaskInfo(@RequestParam(name = "actId") long actId,
                                                                @RequestParam(name = "cmptUseInx", defaultValue = "400") long cmptUseInx,
                                                                @RequestParam(name = "sid") long sid) {
        return this.doQueryTaskInfo(actId, cmptUseInx, sid);
    }

    @GetMapping("/queryTaskAwardMember")
    public Response<List<JSONObject>> queryTaskAwardMember(HttpServletRequest request, HttpServletResponse response,
                                                           @RequestParam(name = "actId") long actId,
                                                           @RequestParam(name = "cmptUseInx", defaultValue = "400") long cmptUseInx,
                                                           @RequestParam(name = "sid") long sid,
                                                           @RequestParam(name = "taskId") long taskId) {
        long loginUid = getLoginYYUid(request, response);
        if (loginUid <= 0) {
            return Response.fail(401, "未登录！");
        }

        final Set<Long> allowUids = ImmutableSet.of(50018033L, 50043502L);
        if (!allowUids.contains(loginUid)) {
            return Response.fail(401, "未授权！");
        }

        return this.queryTaskAwardMember(actId, cmptUseInx, sid, taskId);
    }
}
