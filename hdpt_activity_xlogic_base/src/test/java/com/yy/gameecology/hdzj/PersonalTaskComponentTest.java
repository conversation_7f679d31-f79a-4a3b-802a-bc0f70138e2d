package com.yy.gameecology.hdzj;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.hdzt.BaseEvent;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.element.component.PersonalTaskComponent;
import com.yy.gameecology.hdzj.element.component.attr.PersonalTaskComponentAttr;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.concurrent.atomic.AtomicLong;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-4.properties"})
public class PersonalTaskComponentTest {
    static {
        System.setProperty("group", "4");
    }
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    AtomicLong counter = new AtomicLong(0);

    @Autowired
    private PersonalTaskComponent personalTaskComponent;

    @Test
    public void onPhaseTimeEnd() throws Exception {
        PersonalTaskComponentAttr attr = personalTaskComponent.getComponentAttr(2022033001, 1);
        PhaseTimeEnd event = new PhaseTimeEnd();
        event.setActId(2022033001);
        event.setRankId(40);
        event.setUri(BaseEvent.PHASE_TIME_END);
        event.setTimestamp(DateUtil.today());
        event.setEkey("PhaseTimeEnd_2022033001_40_30");
        event.setIndex(0);
        event.setPhaseId(30);
        event.setTimeKey(0);
        event.setEndTime("2022-05-20 23:59:59");
        personalTaskComponent.onPhaseTimeEnd(event, attr);
        System.out.println("done!");
    }

    @Test
    public void onPhaseTimeEnd2() throws Exception {
        PersonalTaskComponentAttr attr = personalTaskComponent.getComponentAttr(2022064001, 1);
        PhaseTimeEnd event = new PhaseTimeEnd();
        event.setActId(2022064001);
        event.setRankId(40);
        event.setUri(BaseEvent.PHASE_TIME_END);
        event.setTimestamp(DateUtil.today());
        event.setEkey("PhaseTimeEnd_2022064001_40_30");
        event.setIndex(0);
        event.setPhaseId(30);
        event.setTimeKey(0);
        event.setEndTime("2022-08-15 23:59:59");
        personalTaskComponent.onPhaseTimeEnd(event, attr);
        System.out.println("done!");
    }

    @Test
    public void onRankingScoreChanged() throws Exception {
        PersonalTaskComponentAttr attr = personalTaskComponent.getComponentAttr(2022064001, 1);
//        RankingScoreChanged event = new RankingScoreChanged();
//        event.setActId(2022033001);
//        event.setRankId(40);
//        event.setPhaseId(30);
//        event.setUri(RankingScoreChanged.URI);
//        event.setTimestamp(DateUtil.today());
//        event.setMember("248697624");
//        event.setPhaseScore(4000000);
//        event.setEkey("RankingScoreChanged_2022033001_40_30");
//        event.setIndex(0);
//        event.setPhaseId(30);
//        event.setTimeKey(0);

        String data = "{\"actId\":2022064001,\"actors\":{40014:\"2574016105\"},\"busiId\":400,\"ekey\":\"\",\"index\":0,\"itemCount\":500,\"itemId\":\"WNXD_001\",\"itemScore\":5000,\"member\":\"2574016105\",\"occurTime\":\"2022-08-14 00:00:13\",\"phaseId\":30,\"phaseScore\":2100000,\"rankId\":40,\"rankScore\":2100000,\"seq\":\"8ff7ddde-fa8a-48cf-9e55-9f5af30db28c\",\"timeKey\":0,\"timestamp\":\"2022-08-14 00:00:13\",\"uri\":2002}";
        RankingScoreChanged event = JSON.parseObject(data, RankingScoreChanged.class);

        personalTaskComponent.onRankingScoreChanged(event, attr);
        System.out.println("done!");
    }

    @Test
    public void getMyStatus() throws Exception {
        personalTaskComponent.getMyStatus(2022033001, 1, 248697624, 0);
        personalTaskComponent.getMyStatus(2022033001, 1, 248699782, 0);
        System.out.println("done!");
    }

    @Test
    public void giveAwards() throws Exception {
        personalTaskComponent.giveAwards();
        System.out.println("done!");
    }
}
