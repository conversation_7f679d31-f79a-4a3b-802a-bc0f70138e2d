package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.hdzt.ActShopExchangeResult;
import com.yy.gameecology.activity.client.thrift.UserinfoThriftClient;
import com.yy.gameecology.activity.client.yrpc.ZhuiyaShopServerClient;
import com.yy.gameecology.activity.dao.redis.ActRedisDao;
import com.yy.gameecology.activity.service.ActInfoService;
import com.yy.gameecology.common.bean.ClientInfo;
import com.yy.gameecology.common.utils.WebUtil;
import com.yy.gameecology.hdzj.bean.ExchangeResult;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.protocol.pb.shop.ZhuiyaShopServer;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AovInviteActShopComponent exchange方法单元测试
 *
 * <AUTHOR> Assistant
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-2.properties"})
public class AovInviteActShopComponentTest {
    
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    
    @InjectMocks
    private AovInviteActShopComponent aovInviteActShopComponent;
    
    @Mock
    private UserinfoThriftClient userinfoThriftClient;
    
    @Mock
    private ZhuiyaShopServerClient zhuiyaShopServerClient;
    
    @Mock
    private ActInfoService actInfoService;
    
    @Mock
    private ActRedisDao actRedisDao;
    
    @Mock
    private HttpServletRequest request;
    
    // 测试数据
    private static final Long TEST_ACT_ID = 2025064001L;
    private static final Long TEST_UID = 123456789L;
    private static final String TEST_ITEM_ID = "test_item_001";
    private static final Long TEST_CMPT_INDEX = 1L;
    
    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // Mock基础方法
        doReturn(TEST_UID).when(aovInviteActShopComponent).getLoginYYUid();
        doReturn("test_group").when(aovInviteActShopComponent).getRedisGroupCode(anyLong());
        doReturn("test_key").when(aovInviteActShopComponent).makeKey(any(), anyString());
        
        // Mock ClientInfo
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setApp("test_app");
        clientInfo.setHdid("test_hdid");
        clientInfo.setIp("127.0.0.1");
        clientInfo.setPlatform("android");
        
        try (MockedStatic<WebUtil> webUtilMock = mockStatic(WebUtil.class)) {
            webUtilMock.when(() -> WebUtil.getClientInfo(any())).thenReturn(clientInfo);
        }
    }
    
    /**
     * 测试成功兑换场景
     */
    @Test
    public void testExchangeSuccess() {
        // 准备测试数据
        AovInviteActShopComponentAttr attr = createTestAttr();
        doReturn(attr).when(aovInviteActShopComponent).getComponentAttr(TEST_ACT_ID, TEST_CMPT_INDEX);
        
        // Mock活动状态检查
        when(actInfoService.actTimeStatus(TEST_ACT_ID)).thenReturn(0);
        
        // Mock实名认证
        when(userinfoThriftClient.getIdHash(TEST_UID)).thenReturn("test_id_hash");
        
        // Mock Redis锁
        when(actRedisDao.setNX(anyString(), anyString(), anyString(), anyInt())).thenReturn(true);
        
        // Mock商品查询响应
        ZhuiyaShopServer.ListShopItemResp listResp = createMockListShopItemResp();
        when(zhuiyaShopServerClient.listShopItem(any())).thenReturn(listResp);
        
        // Mock购买响应
        ZhuiyaShopServer.BuyShopItemResp buyResp = createMockBuyShopItemResp(true);
        when(zhuiyaShopServerClient.buyShopItem(any())).thenReturn(buyResp);
        
        // Mock ClientInfo
        try (MockedStatic<WebUtil> webUtilMock = mockStatic(WebUtil.class)) {
            ClientInfo clientInfo = new ClientInfo();
            clientInfo.setApp("test_app");
            clientInfo.setHdid("test_hdid");
            clientInfo.setIp("127.0.0.1");
            clientInfo.setPlatform("android");
            webUtilMock.when(() -> WebUtil.getClientInfo(any())).thenReturn(clientInfo);
            
            // 执行测试
            Response<ExchangeResult> response = aovInviteActShopComponent.exchange(
                TEST_ACT_ID, TEST_CMPT_INDEX, TEST_ITEM_ID, request);
            
            // 验证结果
            assertNotNull(response);
            assertEquals(0, response.getResult());
            assertNotNull(response.getData());
            assertEquals("测试兑换成功", response.getData().getResult());
            assertEquals("测试提示", response.getData().getTip());
        }
    }
    
    /**
     * 测试用户未登录场景
     */
    @Test
    public void testExchangeUserNotLogin() {
        // Mock未登录状态
        doReturn(0L).when(aovInviteActShopComponent).getLoginYYUid();
        
        // 执行测试
        Response<ExchangeResult> response = aovInviteActShopComponent.exchange(
            TEST_ACT_ID, TEST_CMPT_INDEX, TEST_ITEM_ID, request);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(400, response.getResult());
        assertEquals("未登录", response.getReason());
    }
    
    /**
     * 测试活动已结束场景
     */
    @Test
    public void testExchangeActivityEnded() {
        // Mock活动已结束
        when(actInfoService.actTimeStatus(TEST_ACT_ID)).thenReturn(1);
        
        // 执行测试
        Response<ExchangeResult> response = aovInviteActShopComponent.exchange(
            TEST_ACT_ID, TEST_CMPT_INDEX, TEST_ITEM_ID, request);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(3, response.getResult());
        assertEquals("活动已结束!", response.getReason());
    }
    
    /**
     * 测试活动未开始场景
     */
    @Test
    public void testExchangeActivityNotStarted() {
        // Mock活动未开始
        when(actInfoService.actTimeStatus(TEST_ACT_ID)).thenReturn(-1);
        
        // 执行测试
        Response<ExchangeResult> response = aovInviteActShopComponent.exchange(
            TEST_ACT_ID, TEST_CMPT_INDEX, TEST_ITEM_ID, request);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(3, response.getResult());
        assertEquals("活动未开始!", response.getReason());
    }
    
    /**
     * 测试用户未实名认证场景
     */
    @Test
    public void testExchangeUserNotVerified() {
        // Mock活动状态正常
        when(actInfoService.actTimeStatus(TEST_ACT_ID)).thenReturn(0);
        
        // Mock未实名认证
        when(userinfoThriftClient.getIdHash(TEST_UID)).thenReturn("");
        
        // 执行测试
        Response<ExchangeResult> response = aovInviteActShopComponent.exchange(
            TEST_ACT_ID, TEST_CMPT_INDEX, TEST_ITEM_ID, request);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(471, response.getResult());
        assertEquals("您还没有完成实名认证，请先完成实名认证", response.getReason());
    }
    
    /**
     * 测试操作太频繁场景
     */
    @Test
    public void testExchangeTooFrequent() {
        // 准备测试数据
        AovInviteActShopComponentAttr attr = createTestAttr();
        doReturn(attr).when(aovInviteActShopComponent).getComponentAttr(TEST_ACT_ID, TEST_CMPT_INDEX);
        
        // Mock活动状态正常
        when(actInfoService.actTimeStatus(TEST_ACT_ID)).thenReturn(0);
        
        // Mock实名认证通过
        when(userinfoThriftClient.getIdHash(TEST_UID)).thenReturn("test_id_hash");
        
        // Mock Redis锁失败
        when(actRedisDao.setNX(anyString(), anyString(), anyString(), anyInt())).thenReturn(false);
        
        // 执行测试
        Response<ExchangeResult> response = aovInviteActShopComponent.exchange(
            TEST_ACT_ID, TEST_CMPT_INDEX, TEST_ITEM_ID, request);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(1, response.getResult());
        assertEquals("操作太频繁，请稍后重试", response.getReason());
    }
    
    /**
     * 创建测试用的组件属性
     */
    private AovInviteActShopComponentAttr createTestAttr() {
        AovInviteActShopComponentAttr attr = new AovInviteActShopComponentAttr();
        attr.setActId(TEST_ACT_ID);
        attr.setCmptId(ComponentId.AOV_INVITE_ACT_SHOP);
        attr.setCmptUseInx(TEST_CMPT_INDEX);
        attr.setItemId(Long.parseLong(TEST_ITEM_ID.replaceAll("\\D", "")));
        attr.setOpenTime(Lists.newArrayList("2025-01-01 00:00:00&2025-12-31 23:59:59"));
        
        // 设置兑换结果配置
        ActShopExchangeResult exchangeResult = new ActShopExchangeResult();
        exchangeResult.setItemId(TEST_ITEM_ID);
        exchangeResult.setResult("测试兑换成功");
        exchangeResult.setTip("测试提示");
        attr.setShopExchangeResult(Lists.newArrayList(exchangeResult));
        
        return attr;
    }
    
    /**
     * 创建Mock的商品列表响应
     */
    private ZhuiyaShopServer.ListShopItemResp createMockListShopItemResp() {
        ZhuiyaShopServer.ItemCost itemCost = ZhuiyaShopServer.ItemCost.newBuilder()
            .setRemain(10)
            .setUserRemain(1)
            .build();
        
        ZhuiyaShopServer.ShopItemInfo shopItemInfo = ZhuiyaShopServer.ShopItemInfo.newBuilder()
            .setItemId(TEST_ITEM_ID)
            .setItemName("测试商品")
            .addItemCost(itemCost)
            .build();
        
        return ZhuiyaShopServer.ListShopItemResp.newBuilder()
            .setCode(0)
            .setMessage("success")
            .addShopItemInfo(shopItemInfo)
            .build();
    }
    
    /**
     * 创建Mock的购买响应
     */
    private ZhuiyaShopServer.BuyShopItemResp createMockBuyShopItemResp(boolean success) {
        ZhuiyaShopServer.BuyShopItemResp.Builder builder = ZhuiyaShopServer.BuyShopItemResp.newBuilder();
        
        if (success) {
            builder.setCode(ZhuiyaShopServer.BuyCode.SUCCESS)
                   .setItemName("测试商品")
                   .setMessage("购买成功");
        } else {
            builder.setCode(ZhuiyaShopServer.BuyCode.ACCOUNT_NOT_ENOUGH)
                   .setCodeValue(1001)
                   .setMessage("金币不足");
        }
        
        return builder.build();
    }
}
