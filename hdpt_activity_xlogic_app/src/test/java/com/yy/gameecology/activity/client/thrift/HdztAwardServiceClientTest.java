package com.yy.gameecology.activity.client.thrift;

import com.google.common.collect.Maps;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztaward.BatchWelfareRequest;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-01-13 10:09
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-3.properties"})
public class HdztAwardServiceClientTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Test
    public void doLotteryTest(){
        hdztAwardServiceClient.doLottery(DateUtil.getNowYyyyMMddHHmmss(),400,1700565220L,30141,1,247,"298a707c-930c-47fb-9446-7b0f68c5cda8");
    }

    @Test
    public void zwcjlbTest() {
        int loops = 10;
        int i = 1;
        while (i < loops) {
            //抽奖测试
            //追玩春节裂变-五卡抽卡
            BatchLotteryResult r1 = hdztAwardServiceClient
                    .doLottery(DateUtil.getNowYyyyMMddHHmmss(),
                            800,
                            123456,
                            20103,
                            2,
                            0, DateUtil.getNowYyyyMMddHHmmss());

            //追玩春节裂变-返场抽奖
            BatchLotteryResult r2 = hdztAwardServiceClient
                    .doLottery(DateUtil.format(DateUtil.getDate("2021-02-11 17:01:01")), 800, 123456, 20105, 2, 0, DateUtil.getNowYyyyMMddHHmmss());

            //发奖测试
            hdztAwardServiceClient
                    .doWelfare(DateUtil.getNowYyyyMMddHHmmss(), 800, 123456, 20104, 2, 10, DateUtil.getNowYyyyMMddHHmmss());
            i++;
        }

    }

    @Test
    public void fdsa() {
        String seq = java.util.UUID.randomUUID().toString();
        BatchWelfareRequest request = new BatchWelfareRequest();
        request.setBusiId(400);
        request.setUid(2574016105L);
        request.setTaskId(20127);
        request.setSeq(seq);
        Map<Long, Map<Long, Integer>> taskPackageIds = Maps.newHashMap();
        Map<Long, Integer> packageIdCount = Maps.newHashMap();
        packageIdCount.put(117L, 1);
        taskPackageIds.put(20127L, packageIdCount);
        request.setTaskPackageIds(taskPackageIds);

        BatchWelfareResult batchWelfareResult = hdztAwardServiceClient.doWelfare("2021-04-14 18:00:00", request.getBusiId(), request.getUid(),
                request.getTaskId(), 20, 117, request.getSeq());
        System.out.println(batchWelfareResult);
    }

    @Test
    public void sendTest() {
        Map<Long, Map<Long, Integer>> taskPackageIds = Maps.newHashMap();
        Map<Long,Integer> packageIdAmount = Maps.newHashMap();
        packageIdAmount.put(259L,1);

        taskPackageIds.put(30142L,packageIdAmount);
        hdztAwardServiceClient.doBatchWelfare("test_award_123456", 50042952L, taskPackageIds, DateUtil.getNowYyyyMMddHHmmss(), 3, Maps.newHashMap());
    }
}
